import QtQuick
import QtQuick.Controls.Material
import QtQuick.Layouts
import models 1.0

Rectangle {
    id: rootDialog
    width: 540
    height: Math.max(100, contentColumn.implicitHeight + 20)
    radius: 10
    color: Qt.rgba(backgroundColor.r, backgroundColor.g, backgroundColor.b, 0.8)
    border.color: borderColor
    border.width: 1

    property var mapRoot: null
    property string currentSearchKey: ""
    property int searchType: 0 // 0: location, 1: camera, 2: long lat

    property string foundAddress: ""
    property bool showSuggestions: false
    property bool showResults: false
    property int selectedSuggestion: -1

    property bool groupAllSelected: filterGroupCombobox.isAllSelected
    property bool aiAllSelected: filterAICombobox.isAllSelected
    property bool stateAllSelected: filterStateCombobox.isAllSelected

    property var controller: null
    property color textColor: controller ? controller.get_color_theme_by_key("text_color_all_app") : "black"
    property color primaryColor: controller ? controller.get_color_theme_by_key("primary") : "white"
    property color backgroundColor: controller ? controller.get_color_theme_by_key("main_background") : "white"
    property color borderColor: controller ? controller.get_color_theme_by_key("main_border") : "white"
    property string comboboxIcon: controller ? controller.get_image_theme_by_key("down_spinbox_temp") : "white"
    property string upArrowButton: controller ? controller.get_image_theme_by_key("up_arrow") : ""
    property string downArrowButton: controller ? controller.get_image_theme_by_key("down_arrow") : ""

    Connections{
        target: controller
        function onThemeChanged() {
            textColor = controller.get_color_theme_by_key("text_color_all_app")
            primaryColor = controller.get_color_theme_by_key("primary")
            backgroundColor = controller.get_color_theme_by_key("main_background")
            borderColor = controller.get_color_theme_by_key("main_border")
            comboboxIcon = controller.get_image_theme_by_key("down_spinbox_temp")
        }
    }

    ColumnLayout{
        id: contentColumn
        anchors.fill: parent
        anchors.margins: 10
        spacing: 10

        RowLayout {
            Layout.fillWidth: true
            Layout.preferredHeight: 40
            spacing: 0
            Text {
                text: qsTr("SEARCH")
                color: textColor
                font.pixelSize: 18
                font.bold: true
                Layout.alignment: Qt.AlignVCenter | Qt.AlignLeft
                Layout.fillWidth: true
            }
            Item { Layout.fillWidth: true }
            ToolButton {
                id: clearBtn
                Layout.preferredWidth: 70
                Layout.preferredHeight: 32
                background: Rectangle {
                    anchors.fill: parent
                    color: backgroundColor
                    border.color: primaryColor
                    border.width: 2
                    radius: 10
                }
                contentItem: Text {
                    text: qsTr("Clear")
                    color: primaryColor
                    font.pixelSize: 14
                    font.bold: true
                    anchors.centerIn: parent
                    horizontalAlignment: Text.AlignHCenter
                }
            }
            Item { width: 8 }
            ToolButton {
                id: hideBtn
                Layout.preferredWidth: 70
                Layout.preferredHeight: 32
                background: Rectangle {
                    anchors.fill: parent
                    color: backgroundColor
                    border.color: primaryColor
                    border.width: 2
                    radius: 10
                }
                contentItem: Text {
                    text: qsTr("Hide")
                    color: primaryColor
                    font.pixelSize: 14
                    font.bold: true
                    anchors.centerIn: parent
                    horizontalAlignment: Text.AlignHCenter
                }
            }
        }

        Rectangle{
            Layout.preferredWidth: 60
            Layout.preferredHeight: 4
            color: Qt.rgba(textColor.r, textColor.g, textColor.b, 0.4)
        }

        RowLayout {
            Layout.fillWidth: true
            Layout.preferredHeight: 32
            spacing: 10
            ComboBox{
                id: searchTypeCombobox
                Layout.preferredWidth: 120
                Layout.preferredHeight: 32
                Layout.alignment: Qt.AlignVCenter
                model: [qsTr("Location"), qsTr("Coordinate")]
                currentIndex: 0
                onCurrentIndexChanged: {
                    searchType = currentIndex
                    showSuggestions = false
                }
                contentItem: Text {
                    leftPadding: 10
                    rightPadding: searchTypeCombobox.indicator.width + searchTypeCombobox.spacing
                    text: searchTypeCombobox.displayText
                    font: searchTypeCombobox.font
                    color: Qt.rgba(textColor.r, textColor.g, textColor.b, 0.8)
                    verticalAlignment: Text.AlignVCenter
                    elide: Text.ElideRight
                }
                background: Rectangle {
                    color: backgroundColor
                    border.color: borderColor
                    border.width: 1
                    radius: 10
                }
                indicator: Image {
                    id: quality_icon_indicator
                    source: comboboxIcon
                    anchors.verticalCenter: parent.verticalCenter
                    anchors.right: parent.right
                    anchors.rightMargin: 8
                }
                delegate: ItemDelegate {
                    width: searchTypeCombobox.width
                    height: 32
                    hoverEnabled: true

                    onClicked: {
                        searchTypeCombobox.currentIndex = index
                    }

                    MouseArea {
                        id: hoverArea
                        anchors.fill: parent
                        hoverEnabled: true
                        propagateComposedEvents: true
                        onClicked: (mouse) => {
                            mouse.accepted = false
                            parent.onClicked()
                        }
                        onPressed: (mouse) => mouse.accepted = false
                        onReleased: (mouse) => mouse.accepted = false
                    }

                    contentItem: Item {
                        anchors.fill: parent

                        RowLayout {
                            anchors.fill: parent
                            anchors.leftMargin: 8
                            anchors.rightMargin: 8
                            anchors.topMargin: 4
                            anchors.bottomMargin: 4
                            spacing: 8

                            Text {
                                text: modelData
                                color: hoverArea.containsMouse ? primaryColor : textColor
                                font.pixelSize: 12
                                Layout.fillWidth: true
                                Layout.alignment: Qt.AlignVCenter
                                verticalAlignment: Text.AlignVCenter
                                horizontalAlignment: Text.AlignLeft
                                elide: Text.ElideRight
                                wrapMode: Text.NoWrap
                            }
                        }
                    }

                    background: Rectangle {
                        color: backgroundColor
                        border.color: hoverArea.containsMouse ? primaryColor : "transparent"
                        border.width: 1
                    }
                }
                popup: Popup {
                    y: searchTypeCombobox.height - 1
                    width: searchTypeCombobox.width
                    height: Math.min(contentItem.implicitHeight, searchTypeCombobox.Window.height - topMargin - bottomMargin)
                    padding: 1

                    contentItem: ListView {
                        clip: true
                        implicitHeight: contentHeight
                        model: searchTypeCombobox.popup.visible ? searchTypeCombobox.delegateModel : null
                        currentIndex: searchTypeCombobox.highlightedIndex
                    }

                    background: Rectangle {
                        color: backgroundColor
                        border.color: borderColor
                        radius: 4
                    }
                }
            }
            Loader{
                id: searchLoader
                Layout.fillWidth: true
                Layout.preferredHeight: 32
                Layout.alignment: Qt.AlignVCenter
                active: true
                sourceComponent: (function(){
                    return searchType === 0 ? locationInputComponent :
                           searchType === 1 ? floatInputComponent : null
                })()
            }
            Component{
                id: locationInputComponent
                Item{
                    RowLayout {
                        anchors.fill: parent
                        spacing: 8
                        property alias tfQuery: tfQuery

                        Timer {
                            id: debounceTimer
                            interval: 350
                            repeat: false
                            onTriggered: {
                                forwardGeocode(tfQuery.text);
                            }
                        }

                        TextField {
                            id: tfQuery
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            placeholderText: qsTr("Enter address...")
                            placeholderTextColor: textColor
                            verticalAlignment: Text.AlignVCenter
                            color: textColor
                            onTextChanged: {
                                currentSearchKey = tfQuery.text
                                debounceTimer.restart();
                            }
                            onActiveFocusChanged: {
                                if (!activeFocus) {
                                    showSuggestions = false;
                                }
                            }
                            Keys.onDownPressed: {
                                if (showSuggestions && suggestionsModel.count > 0) {
                                    selectedSuggestion = Math.min(selectedSuggestion + 1, suggestionsModel.count - 1);
                                }
                            }
                            Keys.onUpPressed: {
                                if (showSuggestions && suggestionsModel.count > 0) {
                                    selectedSuggestion = Math.max(selectedSuggestion - 1, 0);
                                }
                            }
                            Keys.onEnterPressed: {
                                if (showSuggestions && selectedSuggestion >= 0 && selectedSuggestion < suggestionsModel.count) {
                                    var suggestion = suggestionsModel.get(selectedSuggestion);
                                    tfQuery.text = suggestion.name;
                                    foundAddress = suggestion.name;
                                    showSuggestions = false;
                                    tfQuery.focus = false;
                                    animateToLocationWithBoundingBox(
                                        suggestion.latitude,
                                        suggestion.longitude,
                                        suggestion.north,
                                        suggestion.south,
                                        suggestion.west,
                                        suggestion.east
                                    );
                                } else {
                                    forwardGeocode(tfQuery.text);
                                    showSuggestions = false;
                                    tfQuery.focus = false;
                                }
                            }

                            background: Rectangle {
                                color: backgroundColor
                                border.color: tfQuery.activeFocus ? primaryColor : borderColor
                                border.width: 2
                                radius: 10
                            }
                        }

                        ToolButton {
                            Layout.preferredWidth: 60
                            Layout.fillHeight: true
                            Layout.alignment: Qt.AlignVCenter
                            visible: tfQuery.text.length > 0
                            onClicked: {
                                tfQuery.text = ""
                                tfQuery.forceActiveFocus()
                            }
                            background: Rectangle {
                                color: backgroundColor
                                border.color: primaryColor
                                border.width: 1
                                radius: 10
                            }

                            contentItem: Text {
                                text: qsTr("Clear")
                                color: primaryColor
                                font.pixelSize: 12
                                font.bold: true
                                anchors.centerIn: parent
                            }
                        }
                    }

                    Rectangle {
                        id: suggestionsOverlay
                        parent: rootDialog.parent ? rootDialog.parent : rootDialog
                        visible: showSuggestions && suggestionsModel.count > 0 && searchType === 0
                        onVisibleChanged: {
                            if (visible) {
                                updatePosition()
                            }
                        }
                        width: tfQuery.width
                        height: Math.min(5, suggestionsModel.count) * 32
                        color: backgroundColor
                        border.color: borderColor
                        border.width: 1
                        radius: 5
                        z: 1000

                        Component.onCompleted: {
                            updatePosition()
                        }

                        Connections {
                            target: rootDialog
                            function onXChanged() { suggestionsOverlay.updatePosition() }
                            function onYChanged() { suggestionsOverlay.updatePosition() }
                        }

                        function updatePosition() {
                            if (tfQuery && rootDialog.parent) {
                                var pos = tfQuery.mapToItem(rootDialog.parent, 0, tfQuery.height + 2)
                                x = pos.x
                                y = pos.y
                            }
                        }

                        ListView {
                            id: suggestionList
                            anchors.fill: parent
                            model: suggestionsModel
                            interactive: true
                            clip: true
                            delegate: Rectangle {
                                width: parent ? parent.width : 0
                                height: 32
                                color: backgroundColor
                                border.color: index === selectedSuggestion ? primaryColor : "transparent"
                                border.width: 1
                                Text {
                                    anchors.verticalCenter: parent.verticalCenter
                                    anchors.left: parent.left; anchors.leftMargin: 8
                                    text: name
                                    color: index === selectedSuggestion ? primaryColor : textColor
                                    elide: Text.ElideRight
                                    width: parent.width - 16
                                }
                                MouseArea {
                                    anchors.fill: parent
                                    hoverEnabled: true
                                    onClicked: selectSuggestion()
                                    onEntered: selectedSuggestion = index
                                }
                                function selectSuggestion() {
                                    if (searchLoader.item && searchLoader.item.tfQuery) {
                                        searchLoader.item.tfQuery.text = name;
                                    }
                                    foundAddress = name;
                                    showSuggestions = false;
                                    animateToLocationWithBoundingBox(latitude, longitude, north, south, west, east);
                                }
                            }
                        }
                    }

                    MouseArea {
                        anchors.fill: parent
                        enabled: showSuggestions && searchType === 0
                        onClicked: {
                            showSuggestions = false
                        }
                        z: 999
                    }
                }
            }
            // Component{
            //     id: cameraNameInputComponent
            //     TextField {
            //         id: cameraNameInput
            //         anchors.fill: parent
            //         placeholderText: qsTr("Enter camera name...")
            //         color: textColor
            //         placeholderTextColor: textColor
            //         verticalAlignment: Text.AlignVCenter

            //         background: Rectangle {
            //             color: backgroundColor
            //             border.color: cameraNameInput.activeFocus ? primaryColor : borderColor
            //             border.width: 2
            //             radius: 10
            //         }

            //         onTextChanged: {
            //             currentSearchKey = cameraNameInput.text
            //         }
            //     }
            // }
            Component{
                id: floatInputComponent
                RowLayout{
                    anchors.fill: parent
                    spacing: 8
                    property alias latitudeText: latitudeInput.text
                    property alias longitudeText: longitudeInput.text

                    TextField {
                        id: latitudeInput
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        Layout.alignment: Qt.AlignVCenter
                        placeholderText: qsTr("Enter latitude")
                        color: textColor
                        placeholderTextColor: textColor
                        verticalAlignment: Text.AlignVCenter

                        background: Rectangle {
                            color: backgroundColor
                            border.color: latitudeInput.activeFocus ? primaryColor : borderColor
                            border.width: 2
                            radius: 10
                        }

                        validator: DoubleValidator {
                            bottom: -90
                            top: 90
                            decimals: 6
                            notation: DoubleValidator.StandardNotation
                        }
                    }
                    TextField {
                        id: longitudeInput
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        Layout.alignment: Qt.AlignVCenter
                        placeholderText: qsTr("Enter longitude")
                        color: textColor
                        placeholderTextColor: textColor
                        verticalAlignment: Text.AlignVCenter

                        background: Rectangle {
                            color: backgroundColor
                            border.color: longitudeInput.activeFocus ? primaryColor : borderColor
                            border.width: 2
                            radius: 10
                        }

                        validator: DoubleValidator {
                            bottom: -180
                            top: 180
                            decimals: 6
                            notation: DoubleValidator.StandardNotation
                        }
                    }

                    ToolButton {
                        Layout.preferredWidth: 60
                        Layout.fillHeight: true
                        Layout.alignment: Qt.AlignVCenter
                        visible: longitudeInput.text.length > 0 && latitudeInput.text.length > 0
                        onClicked: {
                            mapRoot.animateToLocationWithBoundingBox(latitudeInput.text, 
                                                                    longitudeInput.text, 
                                                                    latitudeInput.text - 0.001,
                                                                    latitudeInput.text + 0.001,
                                                                    longitudeInput.text - 0.001,
                                                                    longitudeInput.text + 0.001)
                        }
                        background: Rectangle {
                            color: backgroundColor
                            border.color: primaryColor
                            border.width: 1
                            radius: 10
                        }

                        contentItem: Text {
                            text: qsTr("Find")
                            color: primaryColor
                            font.pixelSize: 12
                            font.bold: true
                            anchors.centerIn: parent
                        }
                    }
                }
            }
        }

        RowLayout {
            Layout.fillWidth: true
            Layout.preferredHeight: 32
            spacing: 10
            CheckboxComboBox {
                id: filterGroupCombobox
                Layout.preferredWidth: 120
                allText: qsTr("All Groups")
                _backgroundColor: backgroundColor
                _borderColor: borderColor
                _primaryColor: primaryColor
                _textColor: textColor
                _comboboxIcon: comboboxIcon
                model: { return mapRoot.thisMapModel.groupIds }
                onSelectedItemsChanged: { mapRoot.selectedGroup = selectedItems }
            }
            CheckboxComboBox {
                id: filterStateCombobox
                Layout.preferredWidth: 120
                allText: qsTr("All States")
                _backgroundColor: backgroundColor
                _borderColor: borderColor
                _primaryColor: primaryColor
                _textColor: textColor
                _comboboxIcon: comboboxIcon
                model: { return [ {"name": qsTr("Connected"), "id": "CONNECTED"}, {"name": qsTr("Disconnected"), "id": "DISCONNECTED"} ] }
                onSelectedItemsChanged: { mapRoot.selectedState = selectedItems }
            }
            CheckboxComboBox {
                id: filterAICombobox
                allText: qsTr("All AI Flows")
                Layout.preferredWidth: 120
                _backgroundColor: backgroundColor
                _borderColor: borderColor
                _primaryColor: primaryColor
                _textColor: textColor
                _comboboxIcon: comboboxIcon
                model: { return [ {"name": qsTr("Recognition"), "id": "RECOGNITION"}, {"name": qsTr("Protection"), "id": "PROTECTION"}, {"name": qsTr("Frequency"), "id": "FREQUENCY"}, {"name": qsTr("Access"), "id": "ACCESS"}, {"name": qsTr("Motion"), "id": "MOTION"}, {"name": qsTr("Traffic"), "id": "TRAFFIC"}, {"name": qsTr("Weapon"), "id": "WEAPON"}, {"name": qsTr("UFO"), "id": "UFO"}, {"name": qsTr("Fire"), "id": "FIRE"} ] }
                onSelectedItemsChanged: { mapRoot.selectedAI = selectedItems }
            }
        }

        RowLayout{
            Layout.fillWidth: true
            Layout.preferredHeight: 32
            spacing: 10
            Text {
                text: qsTr("RESULTS")
                color: textColor
                font.pixelSize: 18
                font.bold: true
                Layout.alignment: Qt.AlignVCenter | Qt.AlignLeft
                Layout.fillWidth: true
            }
            Item { Layout.fillWidth: true }
            ToolButton {
                Layout.preferredWidth: 32
                Layout.preferredHeight: 32
                contentItem: Image{
                    source: showResults ? upArrowButton : downArrowButton
                    anchors.centerIn: parent
                }
                onClicked: {
                    showResults = !showResults
                }
            }
        }
        Rectangle{
            Layout.preferredWidth: 60
            Layout.preferredHeight: 4
            color: Qt.rgba(textColor.r, textColor.g, textColor.b, 0.4)
        }
        Text{
            text: qsTr("0 results")
            color: textColor
            font.pixelSize: 14
            font.bold: true
            Layout.alignment: Qt.AlignVCenter | Qt.AlignHCenter
            Layout.fillWidth: true
        }

        ScrollView{
            Layout.fillWidth: true
            Layout.fillHeight: true
            visible: showResults
            ListView{
                model: mapRoot.thisMapModel.devices
            }
        }
    }

    ListModel {
        id: suggestionsModel
    }

    function animateToLocationWithBoundingBox(latitude, longitude, north, south, west, east) {
        if (mapRoot && mapRoot.animateToLocationWithBoundingBox) {
            mapRoot.animateToLocationWithBoundingBox(latitude, longitude, north, south, west, east);
        }
    }

    function forwardGeocode(query) {
        if (!query || query.trim().length === 0) {
            suggestionsModel.clear();
            showSuggestions = false;
            return;
        }
        if (foundAddress === query) {
            return;
        }
        var url = "https://api.gpstech.vn/reverse-geo-service/search?"
                + "q=" + encodeURIComponent(query.trim())
                + "&format=json"
                + "&addressdetails=1"
                + "&limit=10"
                + "&access_token=" + mapRoot.thisMapModel.accessToken;
        var xhr = new XMLHttpRequest();
        xhr.open("GET", url);
        xhr.onreadystatechange = function() {
            if (xhr.readyState === XMLHttpRequest.DONE) {
                if (xhr.status === 200) {
                    var json = JSON.parse(xhr.responseText);
                    suggestionsModel.clear();
                    for (var i = 0; i < json.length; i++) {
                        suggestionsModel.append({
                            name: json[i].display_name,
                            latitude: parseFloat(json[i].lat),
                            longitude: parseFloat(json[i].lon),
                            address: json[i].address,
                            north: parseFloat(json[i].boundingbox[0]),
                            south: parseFloat(json[i].boundingbox[1]),
                            west: parseFloat(json[i].boundingbox[2]),
                            east: parseFloat(json[i].boundingbox[3])
                        });
                    }
                    showSuggestions = json.length > 0;
                    selectedSuggestion = -1;
                } else {
                    showSuggestions = false;
                    console.error("Forward geocode lỗi:", xhr.status);
                }
            }
        }
        xhr.send();
    }

}