from typing import Callable, List
import threading
import time
from src.common.controller.controller_manager import Controller, controller_manager
from src.common.model.camera_model import Camera,CameraModel, camera_model_manager
from src.common.model.group_model import Group, GroupModel, group_model_manager
from src.common.model.event_data_model import EventModel, event_manager
from src.common.model.record_model import Record,RecordModel,record_model_manager,RecordData
from src.common.qml.models.map_controller import FloorModel,floor_manager,BuildingModel,building_manager,MapModel,map_manager
from src.common.camera.video_player_manager import video_player_manager
from src.utils.utils import Utils
import queue
import logging
logger = logging.getLogger(__name__)

class MessageProcessor:
    def __init__(self,server_ip = None):
        self.server_ip = server_ip
        self.controller:Controller = controller_manager.get_controller(server_ip = server_ip)
        self.message_queue = queue.Queue()
        self.threads = self.start_threads(5,self._process_messages)
        self.handlers = {
            "CAMERA_RECORDING_STATE_UPDATE": self.cameraRecordingStateUpdate,
            "RECORDING_UPDATE": self.recordingUpdate,
            "CAMERA_STATE_UPDATE": self.cameraStateUpdate,
            # Camera
            "vms_camera_delete": self.vms_camera_delete,
            "vms_camera_update": self.vms_camera_update,
            "vms_camera_add": self.vms_camera_add,
            "vms_camera_group_add": self.vms_camera_group_add,
            "vms_camera_group_update": self.vms_camera_group_update,
            "vms_camera_group_delete": self.vms_camera_group_delete,
            "vms_aiflow_update": self.vms_aiflow_update,
            # Map
            "BUILDING_CREATE": self.buildingCreate,
            "BUILDING_DELETE": self.buildingDelete,
            "BUILDING_UPDATE": self.buildingUpdate,
            "FLOOR_CREATE": self.floorCreate,
            "FLOOR_DELETE": self.floorDelete,
            "FLOOR_UPDATE": self.floorUpdate,
            # Event
            "warning": self.warning,
            "SERVICE_STATE_UPDATE": self.serviceStateUpdate,
            # Auth
            "CAMERA_UNAUTHORIZED": self.cameraUnauthorized
        }

    def _process_messages(self):
        while True:
            message_json = self.message_queue.get()
            if message_json is None:
                break
            try:
                # Xử lý lâu ở đây, không làm nghẽn WebSocket
                event = message_json.get("event",message_json.get("type",None))
                data = message_json.get("data",None)
                # logger.debug(f"event = {event} data = {data}")
                handler = self.handlers.get(event, self.handle_default)
                handler(data)
            except Exception:
                logger.exception("Error processing message")
            self.message_queue.task_done()
        logger.info(f'_process_messages done')
        
    def handle_default(self, data):
        pass

    def cameraRecordingStateUpdate(self,data):
        camera_model:CameraModel = camera_model_manager.get_camera_model(id = data.get("id",None))
        if camera_model is not None:
            state = data.get("state", None)
            if state is not None:
                camera_model.recordingState = state

    def recordingUpdate(self, data):
        cameraId = data.get("cameraId",None)
        id = data.get("id",None)
        recordData: RecordData = record_model_manager.record_data.get(cameraId, None)
        if recordData is not None:
            # trạng thái con cam này đang có trên Grid rồi
            check = False
            for recordModel in recordData.data:
                if id == recordModel.data.id:
                    # case link record này đã có trên thanh timeline rồi
                    recordModel.data.start_duration = Utils.convert_string_to_duration(data.get("start",None))
                    recordModel.data.end_duration = Utils.convert_string_to_duration(data.get("end",None))
                    recordModel.recordModelChanged.emit()
                    check = True
                    break
            if not check:
                # case link record này chưa có trên thanh timeline
                record = Record.from_dict(data)
                recordModel = RecordModel(record=record)
                recordModel.data.start_duration = Utils.convert_string_to_duration(record.start)
                recordModel.data.end_duration = Utils.convert_string_to_duration(record.end)
                if recordModel.data.end_duration is not None:
                    if len(recordData.data) == 0:
                        recordData.start_duration = recordModel.data.start_duration
                        recordData.end_duration = recordModel.data.end_duration
                    recordData.data.append(recordModel)
                    recordData.recordDataChanged.emit()
        else:
            if cameraId == "0066fd36-2076-4d69-b53b-69928d8c4d6f":
                logger.info(f'recordingUpdate recordData = None')

    def cameraStateUpdate(self, data):
        camera_model:CameraModel = camera_model_manager.get_camera_model(id = data.get("id",None))
        if camera_model is not None:
            state = data.get("state", None)
            if state is not None:
                old_state = camera_model.state
                camera_id = camera_model.get_property("id")
                is_phone = camera_model.isPhoneCamera()

                logger.info(f'cameraStateUpdate = {state}')
                camera_model.set_state(state)

                # Special handling for PHONE cameras
                if is_phone:
                    if old_state == "CONNECTED" and state == "DISCONNECTED":
                        # Reset permission when PHONE camera disconnects
                        camera_model.permissionGranted = False
                    elif old_state == "DISCONNECTED" and state == "CONNECTED":
                        # Show permission notification only if permission not granted
                        current_permission = camera_model.get_property("permissionGranted", False)
                        if not current_permission:
                            # Show permission notification using ListenShowNotification
                            from src.common.widget.notifications.listen_message_notifications import listen_show_notification
                            listen_show_notification.listen_phone_permission_signal.emit(camera_model)

                video_player_manager.notify_camera_state_change(camera_model, state)

    def cameraUnauthorized(self, data):
        """
        ✅ Handler cho CAMERA_UNAUTHORIZED event từ WebSocket
        Giống serviceStateUpdate - khi refresh_token hết thì TẤT CẢ cameras đều unauthorized
        """
        logger.error(f'🔴 [TOKEN_EXPIRED] cameraUnauthorized event: {data}')

        # ✅ Giống serviceStateUpdate - xử lý TẤT CẢ cameras cùng lúc
        if data.get("type") == "TOKEN_EXPIRED" or data.get("reason") == "refresh_token_expired":
            logger.error(f'🔴 [TOKEN_EXPIRED] Refresh token expired - setting ALL cameras to unauthorized')

            # ✅ Get all camera models for this server (giống serviceStateUpdate pattern)
            camera_list = camera_model_manager.get_camera_list(server_ip=self.server_ip)
            if camera_list:
                logger.error(f'🔴 [TOKEN_EXPIRED] Setting {len(camera_list)} cameras to unauthorized state')

                for camera_id, camera_model in camera_list.items():
                    if camera_model:
                        try:
                            camera_name = camera_model.get_property("name", "Unknown")
                            logger.error(f'🔴 [TOKEN_EXPIRED] Setting camera {camera_name} ({camera_id}) to unauthorized')

                            # ✅ Set state to unauthorized
                            camera_model.set_state("unauthorized")

                            # ✅ Emit tokenExpired signal
                            if hasattr(camera_model, 'tokenExpired'):
                                camera_model.tokenExpired.emit()
                                logger.debug(f'🔴 [TOKEN_EXPIRED] Emitted tokenExpired for camera {camera_id}')

                        except Exception as e:
                            logger.error(f'🔴 [TOKEN_EXPIRED] Error setting camera {camera_id} to unauthorized: {e}')

                # ✅ Stop unauthorized players specifically
                logger.error(f'🔴 [TOKEN_EXPIRED] Stopping unauthorized players')
                video_player_manager.stop_unauthorized_players()

            else:
                logger.warning(f'🔴 [TOKEN_EXPIRED] No cameras found for server {self.server_ip}')
        else:
            logger.warning(f'🔴 [CAMERA_UNAUTHORIZED] Unknown unauthorized event type: {data}')

    def vms_camera_delete(self, data):
        logger.info(f'vms_camera_delete = {data.get("name","ahihi")}')

    def serviceStateUpdate(self, data):
        logger.debug(f'SERVER RESTART: serviceStateUpdate = {data}')
        if data.get("type") == "STREAMING":
            if data.get("state") == "CONNECTED":
                # Directly restart all video captures when streaming service reconnects
                video_player_manager.restart_all_players()

    def vms_camera_update(self, data):
        print(f'vms_camera_update = {data.get("name","ahihi")}')
        clientId = data.get("clientId",None)
        if clientId != Utils.clientId:
            camera_model = CameraModel(data=data)
            diff = []
            camera_model_origin:CameraModel = camera_model_manager.get_camera_model(id = camera_model.get_property("id",None))
            if camera_model_origin is not None:
                map_manager.updateCamera(camera_model_origin,data)
                diff = camera_model_origin.diff_camera_model(camera = camera_model.data)
                if 'cameraGroupIds' in diff:
                    self.controller.get_groups() 


    def vms_camera_add(self, data):
        print(f'vms_camera_add = {data.get("name","ahihi")}')
        data = Camera.from_dict(data)
        camera_model = CameraModel(data=data)
        camera_model.data.server_ip = self.server_ip
        camera_model_manager.add_camera(camera=camera_model)
        if camera_model.data.cameraGroupIds is not None and len(camera_model.data.cameraGroupIds) > 0:
            self.controller.get_groups()

    def vms_camera_group_add(self, data):
        print(f'vms_camera_group_add = {data}')
        if isinstance(data,dict):
            group_model = GroupModel(data=data)
            group_model.set_property("server_ip",self.server_ip)
            group_model_manager.add_group(group=group_model)

    def vms_camera_group_update(self, data):
        print(f'vms_camera_group_update = {data}')
        group_model:GroupModel = group_model_manager.get_group_model(id = data["id"])
        diff = []
        if group_model is not None:
            diff = group_model.diff_group_model(group = data)
        controller:Controller = controller_manager.get_controller(server_ip = self.server_ip)
        if controller is not None:
            controller.get_cameras()
            
    def vms_camera_group_delete(self, data):
        print(f'vms_camera_group_delete = {data}')
        controller:Controller = controller_manager.get_controller(server_ip = self.server_ip)
        if controller is not None:
            controller.get_groups()

    def vms_aiflow_update(self, data):
        pass

    def buildingCreate(self, data):
        clientId = data.get("clientId",None)
        if clientId != Utils.clientId:
            building_manager.createBuildingWSSignal.emit((self.server_ip,data))

    def buildingDelete(self, data):
        clientId = data.get("clientId",None)
        if clientId != Utils.clientId:
            building_manager.removeBuildingWSSignal.emit((self.server_ip,data))

    def buildingUpdate(self, data):
        clientId = data.get("clientId",None)
        if clientId != Utils.clientId:
            id = data.get("id", None)
            buildingModel:BuildingModel = building_manager.getBuilding(id)
            if buildingModel is not None:
                buildingModel.diffBuildingModel(data)
            building_manager.updateBuildingWSSignal.emit((self.server_ip,data))

    def floorCreate(self, data):
        pass
        # xử lý case này trong buildingUpdate rồi lên thôi
        # floor_manager.createFloorWSSignal.emit((self.server_ip,data))

    def floorDelete(self, data):
        clientId = data.get("clientId",None)
        if clientId != Utils.clientId:
            floor_manager.removeFloorWSSignal.emit((self.server_ip,data))

    def floorUpdate(self, data):
        id = data.get("id", None)
        logger.info(f'floorUpdate = {data.get("name", "ahihi")}')
        floorModel:FloorModel = floor_manager.getFloor(id= id)
        if floorModel is not None:
            floorModel.diff_floor_model(data)

    def warning(self, data):
        try:
            event_manager.add_event_data.emit(data)

        except Exception as e:
            logger.error(f'on_websocket_event error: {e}')
    
    def start_threads(self,number: int, target: Callable, *args) -> List[threading.Thread]:
        threads = []
        for _ in range(number):
            thread = threading.Thread(target=target, args=args)
            thread.daemon = True
            threads.append(thread)
            thread.start()
