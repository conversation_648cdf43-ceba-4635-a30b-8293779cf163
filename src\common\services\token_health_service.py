"""
Token Health Check Service

Service để check token health và trigger unauthorized state cho tất cả cameras
khi detect token expiration qua 401 response.
"""

import logging
import threading
import time
from typing import Dict, Optional

logger = logging.getLogger(__name__)

class TokenHealthService:
    """
    Service để monitor và check token health
    Khi detect 401 → trigger unauthorized cho tất cả cameras trong grid
    """
    
    def __init__(self):
        self._monitors: Dict[str, threading.Thread] = {}
        self._stop_flags: Dict[str, threading.Event] = {}
        
    def start_monitoring(self, server_ip: str, interval_seconds: int = 30):
        """
        Bắt đầu monitor token health cho server
        
        Args:
            server_ip: IP của server cần monitor
            interval_seconds: Th<PERSON>i gian gi<PERSON> c<PERSON>c lần check
        """
        try:
            if server_ip in self._monitors:
                logger.warning(f"🔍 [TOKEN_SERVICE] Monitor already running for {server_ip}")
                return
                
            # Create stop flag
            stop_flag = threading.Event()
            self._stop_flags[server_ip] = stop_flag
            
            # Create monitor thread
            monitor_thread = threading.Thread(
                target=self._monitor_loop,
                args=(server_ip, interval_seconds, stop_flag),
                daemon=True,
                name=f"TokenMonitor-{server_ip}"
            )
            
            self._monitors[server_ip] = monitor_thread
            monitor_thread.start()
            
            logger.info(f"✅ [TOKEN_SERVICE] Started token monitor for {server_ip} (interval: {interval_seconds}s)")
            
        except Exception as e:
            logger.error(f"🔴 [TOKEN_SERVICE] Error starting monitor for {server_ip}: {e}")
    
    def stop_monitoring(self, server_ip: str):
        """Stop monitoring cho server"""
        try:
            if server_ip in self._stop_flags:
                self._stop_flags[server_ip].set()
                
            if server_ip in self._monitors:
                del self._monitors[server_ip]
                del self._stop_flags[server_ip]
                
            logger.info(f"🛑 [TOKEN_SERVICE] Stopped token monitor for {server_ip}")
            
        except Exception as e:
            logger.error(f"🔴 [TOKEN_SERVICE] Error stopping monitor for {server_ip}: {e}")
    
    def force_check_server(self, server_ip: str) -> bool:
        """
        Force check token cho server ngay lập tức
        
        Returns:
            bool: True nếu token healthy, False nếu expired
        """
        try:
            logger.info(f"🔍 [TOKEN_SERVICE] Force checking token for {server_ip}")
            
            # Get API client for server
            api_client = self._get_api_client(server_ip)
            if not api_client:
                logger.warning(f"⚠️ [TOKEN_SERVICE] No API client found for {server_ip}")
                return True
                
            # Check token health
            is_healthy = api_client.check_token_health()
            
            if is_healthy:
                logger.info(f"✅ [TOKEN_SERVICE] Token healthy for {server_ip}")
            else:
                logger.error(f"🔴 [TOKEN_SERVICE] Token expired for {server_ip} - cameras will show unauthorized")
                
            return is_healthy
            
        except Exception as e:
            logger.error(f"🔴 [TOKEN_SERVICE] Error in force check for {server_ip}: {e}")
            return True
    
    def check_all_servers(self):
        """Check token health cho tất cả servers"""
        try:
            logger.info(f"🔍 [TOKEN_SERVICE] Checking all servers")
            
            # Get all API clients
            api_clients = self._get_all_api_clients()
            
            for server_ip, api_client in api_clients.items():
                try:
                    is_healthy = api_client.check_token_health()
                    if not is_healthy:
                        logger.error(f"🔴 [TOKEN_SERVICE] Server {server_ip} has expired token")
                    else:
                        logger.debug(f"✅ [TOKEN_SERVICE] Server {server_ip} token is healthy")
                        
                except Exception as e:
                    logger.error(f"🔴 [TOKEN_SERVICE] Error checking {server_ip}: {e}")
                    
        except Exception as e:
            logger.error(f"🔴 [TOKEN_SERVICE] Error in check all servers: {e}")
    
    def _monitor_loop(self, server_ip: str, interval_seconds: int, stop_flag: threading.Event):
        """Background monitoring loop"""
        logger.debug(f"🔍 [TOKEN_SERVICE] Started monitoring loop for {server_ip}")
        
        while not stop_flag.is_set():
            try:
                # Get API client
                api_client = self._get_api_client(server_ip)
                if not api_client:
                    logger.warning(f"⚠️ [TOKEN_SERVICE] No API client for {server_ip} - stopping monitor")
                    break
                
                # Check token health
                is_healthy = api_client.check_token_health()
                if not is_healthy:
                    logger.error(f"🔴 [TOKEN_SERVICE] Token expired for {server_ip} - stopping monitor")
                    break
                
                # Wait for next check
                stop_flag.wait(interval_seconds)
                
            except Exception as e:
                logger.error(f"🔴 [TOKEN_SERVICE] Error in monitor loop for {server_ip}: {e}")
                stop_flag.wait(interval_seconds)  # Continue monitoring despite errors
        
        logger.debug(f"🛑 [TOKEN_SERVICE] Stopped monitoring loop for {server_ip}")
    
    def _get_api_client(self, server_ip: str):
        """Get API client for server"""
        try:
            from src.api.api_client import api_client_manager
            return api_client_manager.get_api_client(server_ip=server_ip)
        except Exception as e:
            logger.error(f"🔴 [TOKEN_SERVICE] Error getting API client for {server_ip}: {e}")
            return None
    
    def _get_all_api_clients(self) -> Dict[str, any]:
        """Get all API clients"""
        try:
            from src.api.api_client import api_client_manager
            return api_client_manager.get_all_api_clients()
        except Exception as e:
            logger.error(f"🔴 [TOKEN_SERVICE] Error getting all API clients: {e}")
            return {}

# Global instance
token_health_service = TokenHealthService()

# Convenience functions for easy access
def force_check_all_tokens():
    """
    Convenience function: Force check tất cả tokens ngay lập tức
    Dùng để gọi từ UI hoặc debug
    """
    logger.info("🔍 [TOKEN_SERVICE] Force checking all tokens")
    token_health_service.check_all_servers()

def force_check_server_token(server_ip: str) -> bool:
    """
    Convenience function: Force check token cho server cụ thể

    Returns:
        bool: True nếu healthy, False nếu expired
    """
    return token_health_service.force_check_server(server_ip)

def start_token_monitoring(server_ip: str, interval_seconds: int = 30):
    """
    Convenience function: Start monitoring cho server
    """
    token_health_service.start_monitoring(server_ip, interval_seconds)

def stop_token_monitoring(server_ip: str):
    """
    Convenience function: Stop monitoring cho server
    """
    token_health_service.stop_monitoring(server_ip)
