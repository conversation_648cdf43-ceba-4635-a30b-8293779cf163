# Video Manager System - Settings Design

## Overview
Thiết kế chi tiết cho các tab Settings trong Video Manager System, bao gồm General, User Interface, Security, và Advanced settings.

## 1. General Settings Tab ✅ (<PERSON><PERSON> ho<PERSON>n thành)

### Features
- **Interface Theme**: Dark mode, Light mode, System theme
- **Data Protection**: HTTPS, Encryption, Video traffic encryption, Watermark
- **User Activity**: Audit trail, Session duration, Server display, Archive encryption
- **Change Password**: Button để thay đổi mật khẩu

### Widgets Used
- `ThemeOptionWidget` (Custom widget với hover effect)
- `QCheckBox` cho các tùy chọn bảo mật
- `QComboBox` cho thời gian session
- `QPushButton` cho change password

---

## 2. User Interface Tab

### Features Based on Codebase Analysis

#### **Grid Display Settings**
- **Grid Layout**: 1x1, 2x2, 3x3, 4x4, 5x5, 6x6, 7x7, 8x8, 9x9, 10x10, 11x11, 12x12 (từ GridConstants.GRID_PROGRESSION)
- **Show Grid Lines**: Enable/Disable (từ GridLinesOverlay.qml)
- **Grid Lines Opacity**: 0.1, 0.2, 0.3, 0.5 (từ gridLinesOpacity property)
- **Show Camera Labels**: Enable/Disable (từ showLabels property trong MainGrid.qml)
- **Auto Grid Expansion**: Enable/Disable (từ autoExpansionEnabled trong GridModel)

#### **Video Display Settings**
- **Max Video Decode Resolution**: 1280x720, 1920x1080, 2560x1440 (từ MAX_WIDTH_VIDEO_DECODE, MAX_HEIGHT_VIDEO_DECODE)
- **Default Frame Size**: 1920x1080 (từ FRAME_WIDTH_DEFAULT_SERVER, FRAME_HEIGHT_DEFAULT_SERVER)
- **Higher Grid Sub-Stream Threshold**: 9, 16, 25 cameras (từ HIGHER_GRID_NUMBER_USE_SUB_STREAM)
- **Video Aspect Ratio**: 16:9 (từ RATIO_CAMERA)

#### **Toolbar & UI Elements**
- **Show Record Button**: Enable/Disable (từ ENABLE_RECORD_BUTTON)
- **Show Capture Button**: Enable/Disable (từ ENABLE_CAPTURE_BUTTON)
- **Show Speaker Button**: Enable/Disable (từ ENABLE_SPEAKER_BUTTON)
- **Show Microphone Button**: Enable/Disable (từ ENABLE_MICROPHONE_BUTTON)
- **Show Fullscreen Button**: Enable/Disable (từ ENABLE_FULL_SCREEN_BUTTON)
- **Show Grid Button**: Enable/Disable (từ ENABLE_GRID_BUTTON)
- **Show Stream Flow Button**: Enable/Disable (từ ENABLE_STREAM_FLOW_BUTTON)

#### **Debug & Information Display**
- **Show FPS Debug Info**: Enable/Disable (từ SHOW_FPS_CAMERA_DEBUG)
- **Show Camera ID Debug**: Enable/Disable (từ SHOW_ID_CAMERA_DEBUG)
- **Show Event Bar**: Enable/Disable (từ ENABLE_EVENT_BAR)
- **Show Warning Alerts**: Enable/Disable (từ ENABLE_WARNING_ALERT_CAMERA)

#### **Notification Settings**
- **Notification Duration**: 2s, 3s, 5s, 10s (từ Notifications class time parameter)
- **Notification Position**: Auto-position based on parent widget
- **Notification Max Width**: 200px, 300px, 400px (từ setMinimumWidth, setMaximumWidth)

### Widgets to Use
```python
# Grid Display Settings
QComboBox - Grid layout selection (1x1 to 12x12)
QCheckBox - Show grid lines
QSlider - Grid lines opacity (0.1 to 0.5)
QCheckBox - Show camera labels
QCheckBox - Auto grid expansion

# Video Display Settings
QComboBox - Max decode resolution
QComboBox - Default frame size
QSpinBox - Sub-stream threshold (number of cameras)
QLabel - Aspect ratio display (read-only: 16:9)

# Toolbar & UI Elements
QCheckBox - Show record button
QCheckBox - Show capture button
QCheckBox - Show speaker button
QCheckBox - Show microphone button
QCheckBox - Show fullscreen button
QCheckBox - Show grid button
QCheckBox - Show stream flow button

# Debug & Information Display
QCheckBox - Show FPS debug info
QCheckBox - Show camera ID debug
QCheckBox - Show event bar
QCheckBox - Show warning alerts

# Notification Settings
QComboBox - Notification duration
QComboBox - Notification max width
```

---

## 3. Security Tab

### Features Proposed

#### **Authentication**
- **Login Method**: Username/Password, Windows Authentication, LDAP
- **Two-Factor Authentication**: Enable/Disable
- **Session Timeout**: 15min, 30min, 1hour, 2hours, Never
- **Remember Login**: Enable/Disable

#### **Access Control**
- **User Permissions**: View Only, Operator, Administrator
- **Camera Access**: All cameras, Selected cameras
- **Time-based Access**: 24/7, Business hours, Custom schedule
- **IP Restrictions**: Allow all, Whitelist only

#### **Data Security**
- **Encryption Level**: AES-128, AES-256
- **Secure Protocols**: HTTPS only, Mixed, HTTP allowed
- **Certificate Management**: Upload, Generate, View current
- **Audit Logging**: Enable/Disable, Log level (Info, Warning, Error)

#### **Privacy Settings**
- **Video Recording**: Always, Motion only, Manual, Disabled
- **Audio Recording**: Enable/Disable
- **Screenshot Capture**: Enable/Disable
- **Export Restrictions**: Admin only, All users

### Widgets to Use
```python
# Authentication
QComboBox - Login method
QCheckBox - Two-factor authentication
QComboBox - Session timeout
QCheckBox - Remember login

# Access Control
QComboBox - User permissions
QListWidget - Camera selection (multi-select)
QRadioButton - Time-based access options
QTextEdit - IP whitelist input

# Data Security
QComboBox - Encryption level
QRadioButton - Protocol options
QPushButton - Certificate management
QCheckBox - Audit logging
QComboBox - Log level

# Privacy Settings
QComboBox - Video recording mode
QCheckBox - Audio recording
QCheckBox - Screenshot capture
QRadioButton - Export restrictions
```

---

## 4. Advanced Tab

### Features Proposed

#### **Performance Settings**
- **CPU Usage Limit**: 25%, 50%, 75%, Unlimited
- **Memory Usage Limit**: 1GB, 2GB, 4GB, 8GB, Unlimited
- **Network Bandwidth**: 1Mbps, 10Mbps, 100Mbps, Unlimited
- **Hardware Acceleration**: Auto, NVIDIA, Intel, Disabled

#### **Storage Management**
- **Recording Path**: Browse folder selection
- **Max Storage Size**: 100GB, 500GB, 1TB, 2TB, Unlimited
- **Auto-cleanup**: 7 days, 30 days, 90 days, Never
- **Compression**: H.264, H.265, MJPEG

#### **Network Settings**
- **RTSP Port**: Default 554, Custom
- **HTTP Port**: Default 80, Custom  
- **HTTPS Port**: Default 443, Custom
- **Multicast**: Enable/Disable
- **Buffer Size**: 1MB, 5MB, 10MB, 50MB

#### **Debug & Maintenance**
- **Log Level**: Debug, Info, Warning, Error
- **Log File Size**: 10MB, 50MB, 100MB, 500MB
- **Auto-restart**: Enable/Disable
- **Crash Reports**: Send automatically, Ask, Never
- **Database Maintenance**: Auto-optimize, Manual, Disabled

#### **Integration Settings**
- **API Access**: Enable/Disable
- **Webhook URLs**: Add/Edit/Delete
- **Third-party Plugins**: Enable/Disable
- **Export Formats**: AVI, MP4, MOV

### Widgets to Use
```python
# Performance Settings
QComboBox - CPU usage limit
QComboBox - Memory usage limit  
QComboBox - Network bandwidth
QComboBox - Hardware acceleration

# Storage Management
QLineEdit + QPushButton - Recording path browser
QComboBox - Max storage size
QComboBox - Auto-cleanup period
QComboBox - Compression format

# Network Settings
QSpinBox - RTSP port
QSpinBox - HTTP port
QSpinBox - HTTPS port
QCheckBox - Multicast
QComboBox - Buffer size

# Debug & Maintenance
QComboBox - Log level
QComboBox - Log file size
QCheckBox - Auto-restart
QRadioButton - Crash report options
QComboBox - Database maintenance

# Integration Settings
QCheckBox - API access
QListWidget - Webhook URLs with Add/Edit/Delete buttons
QCheckBox - Third-party plugins
QCheckBox - Export format options (multiple)
```

---

## Implementation Structure

### File Organization
```
src/presentation/setting_screen/widget/
├── general_setting_tab.py ✅ (Completed)
├── user_interface_setting_tab.py (New)
├── security_setting_tab.py (New)
├── advanced_setting_tab.py (New)
└── base_setting_tab.py (New - Common base class)
```

### Common Base Class
Tạo `BaseSettingTab` để share common functionality:
- Layout management (horizontal sections)
- Styling methods
- Signal handling
- Theme updates

### Styling Consistency
- Sử dụng cùng pattern như `general_setting_tab.py`
- Horizontal layout: Labels bên trái, Controls bên phải
- Separator lines giữa các sections
- Consistent spacing và margins
- Theme-aware colors

### Signal Handling
Mỗi tab sẽ emit signals khi settings thay đổi:
```python
# Common signals for all tabs
setting_changed = Signal(str, object)  # (setting_name, value)
apply_settings = Signal()
reset_settings = Signal()
```

---

## Next Steps

1. **Create BaseSettingTab class** - Common functionality
2. **Implement UserInterfaceSettingTab** - Display và UI preferences
3. **Implement SecuritySettingTab** - Authentication và security
4. **Implement AdvancedSettingTab** - Performance và advanced options
5. **Update main settings dialog** - Add new tabs
6. **Add settings persistence** - Save/load từ QSettings

Mỗi tab sẽ có UI hoàn chỉnh với proper styling và hover effects, nhưng chỉ có basic signal handling mà không có business logic phức tạp.
