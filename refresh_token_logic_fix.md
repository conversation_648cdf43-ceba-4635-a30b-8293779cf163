# Refresh Token Logic Fix

## Problem Identified

**User Feedback**: "check lại refresh_access_token nếu refresh_token hết hạn thì bắt đăng nhập lại chứ, sao lại refresh cả access cả refresh token"

**Issue**: Logic trong `refresh_access_token()` method không đúng:
- ❌ Khi refresh thành công, code update cả `access_token` VÀ `refresh_token`
- ❌ Điều này không đúng với token refresh logic
- ❌ refresh_token chỉ nên thay đổi khi user login lại

## Correct Token Refresh Logic

### **Standard OAuth2 Token Refresh Flow:**

```
┌─────────────────┐    ┌──────────────────┐
│   access_token  │    │  refresh_token   │
│   (Short-lived) │    │  (Long-lived)    │
└─────────────────┘    └──────────────────┘
         │                       │
         ▼                       ▼
    Expires every              Expires after
    15-30 minutes             7-30 days
         │                       │
         ▼                       ▼
   Use refresh_token         Need full re-login
   to get NEW access_token   to get BOTH tokens
         │                       │
         ▼                       ▼
   refresh_token stays       Clear all tokens
   UNCHANGED                 Force re-authentication
```

### **Two Refresh Token Patterns:**

1. **Static Refresh Tokens** (Most Common):
   - refresh_token stays the same until it expires
   - Only access_token is renewed during refresh

2. **Rotating Refresh Tokens** (More Secure):
   - Both tokens are renewed during refresh
   - Old refresh_token becomes invalid immediately

## Fixed Implementation

### **Enhanced `refresh_access_token()` Method**

**File**: `src/api/api_client.py`

#### Before (Incorrect):
```python
def refresh_access_token(self):
    # ... refresh logic ...
    if response.status_code == HTTPStatus.OK.value:
        data = json_data.get("data")
        if data:
            # ❌ WRONG: Always update both tokens
            self.refresh_token = data.get("refreshToken")  # Wrong!
            self.access_token = data.get("accessToken")
            return self.access_token
```

#### After (Correct):
```python
def refresh_access_token(self):
    # ... refresh logic ...
    if response.status_code == HTTPStatus.OK.value:
        data = json_data.get("data")
        if data:
            # ✅ CORRECT: Only update access_token by default
            new_access_token_value = data.get("accessToken")
            if new_access_token_value:
                self.access_token = new_access_token_value
                AuthQSettings.get_instance().save_access_token(self.access_token)
                
                logger.info(f"✅ [REFRESH] Access token refreshed successfully")
                logger.debug(f"✅ [REFRESH] refresh_token: UNCHANGED (as expected)")
                
                # ⚠️ OPTIONAL: Handle rotating refresh tokens if server provides them
                new_refresh_token_value = data.get("refreshToken")
                if new_refresh_token_value and new_refresh_token_value != self.refresh_token:
                    logger.info(f"🔄 [REFRESH] Server provided new refresh_token (rotating refresh tokens)")
                    self.refresh_token = new_refresh_token_value
                
                return self.access_token
    
    # ✅ REFRESH TOKEN EXPIRED: Clear tokens and require re-login
    elif response.status_code == HTTPStatus.UNAUTHORIZED.value:
        logger.error(f"🔴 [REFRESH] Refresh token expired - user needs to login again")
        self._clear_expired_tokens()  # ✅ Clear all tokens
        return "REFRESH_TOKEN_EXPIRED"
```

### **New Helper Method: `_clear_expired_tokens()`**

```python
def _clear_expired_tokens(self):
    """
    ✅ CLEANUP: Clear expired tokens when refresh_token is invalid
    """
    try:
        logger.info(f"🧹 [CLEANUP] Clearing expired tokens for server: {self.server_ip}")
        
        # Clear tokens from memory
        self.access_token = None
        self.refresh_token = None
        self.user_id = None
        self.clientId = None
        
        # Clear tokens from persistent storage
        AuthQSettings.get_instance().save_access_token("")
        
        logger.info(f"🧹 [CLEANUP] User needs to login again to continue")
        
    except Exception as e:
        logger.error(f"🔴 [CLEANUP] Error clearing expired tokens: {e}")
```

## Flow Diagrams

### **Correct Access Token Refresh Flow:**

```
API Request with expired access_token
↓
401 Unauthorized Error
↓
Call refresh_access_token() with existing refresh_token
↓
Server Response:
├─ 200 OK with new access_token
│  ├─ Update ONLY access_token ✅
│  ├─ Keep refresh_token unchanged ✅
│  └─ Continue operation seamlessly ✅
│
├─ 401 Unauthorized (refresh_token expired)
│  ├─ Clear ALL tokens ✅
│  ├─ Return "REFRESH_TOKEN_EXPIRED" ✅
│  └─ Trigger re-login UI ✅
│
└─ Other errors
   ├─ Log error details ✅
   └─ Return None ✅
```

### **Refresh Token Expiration Flow:**

```
refresh_token expires after 7-30 days
↓
User tries to use app
↓
access_token also expired
↓
Call refresh_access_token()
↓
Server returns 401 Unauthorized
├─ _clear_expired_tokens() called ✅
├─ All tokens cleared from memory ✅
├─ Tokens cleared from storage ✅
└─ Return "REFRESH_TOKEN_EXPIRED" ✅
↓
WebSocket receives "REFRESH_TOKEN_EXPIRED"
├─ _trigger_relogin_ui() called ✅
├─ Show ReLoginDialog to user ✅
└─ Wait for user re-authentication ✅
↓
User enters credentials
├─ ✅ Success → Get NEW access_token + refresh_token
└─ ❌ Fail → Show error, try again
```

## Key Improvements

### ✅ **Correct Token Lifecycle**
- **access_token**: Refreshed every 15-30 minutes, refresh_token stays same
- **refresh_token**: Only changes on full re-login (or rotating tokens)
- **Token expiration**: Proper cleanup when refresh_token expires

### ✅ **Enhanced Security**
- Clear all tokens when refresh_token expires
- No token persistence after expiration
- Force full re-authentication when needed

### ✅ **Better Logging**
- Clear distinction between access_token refresh vs refresh_token expiration
- Detailed logging for debugging token issues
- Proper error messages for different scenarios

### ✅ **Flexible Architecture**
- Support for both static and rotating refresh tokens
- Graceful handling of different server implementations
- Clean separation of concerns

## Expected Behavior

### **Normal Usage (access_token expires)**:
```
15:30 - User using app normally
15:45 - access_token expires (15 min lifetime)
15:45 - API call gets 401 → Auto refresh with refresh_token
15:45 - New access_token received, refresh_token unchanged
15:45 - User continues seamlessly ✅
```

### **Long Idle (refresh_token expires)**:
```
Day 1 - User logs in, gets both tokens
Day 30 - refresh_token expires (30 day lifetime)
Day 30 - User returns, access_token also expired
Day 30 - Refresh attempt gets 401 → Clear all tokens
Day 30 - Show "Please login again" dialog
Day 30 - User enters password → Get fresh tokens ✅
```

## Testing Scenarios

1. **Normal refresh**: ✅ access_token renewed, refresh_token unchanged
2. **Refresh token expired**: ✅ All tokens cleared, re-login required
3. **Network errors**: ✅ Proper error handling, no token corruption
4. **Rotating refresh tokens**: ✅ Handle servers that rotate refresh tokens

The refresh token logic is now **correct and secure**! 🎉

- **access_token expires** → Refresh with existing refresh_token ✅
- **refresh_token expires** → Clear all tokens, require full re-login ✅
