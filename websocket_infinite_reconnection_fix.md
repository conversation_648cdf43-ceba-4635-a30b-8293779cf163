# WebSocket Infinite Reconnection Fix

## Problem
Method `on_close()` trong WebSocket client đang gọi `self.connect_background()` liên tục mà không kiểm tra điều kiện, dẫn đến **infinite reconnection loop** khi token hết hạn.

### Before (Problematic Code):
```python
def on_close(self, ws, close_status_code, close_msg):
    # ... logging ...
    time.sleep(1)
    self.connect_background()  # ❌ Always reconnect - INFINITE LOOP!
```

### Vấn đề gây ra:
1. **Token expired** → WebSocket disconnected
2. **on_close() called** → Automatic reconnection
3. **Connection fails** (token still expired) → on_close() called again
4. **Infinite loop** → System hang, high CPU usage, log spam

---

## Solution Implemented

### 1. Added Authentication State Tracking

```python
def __init__(self, url: str, header: Optional[dict] = None, server_ip: Optional[str] = None):
    # ... existing code ...
    self.auth_failed = False  # Flag to track authentication failures
    self.reconnect_attempts = 0  # Counter for reconnection attempts
    self.max_reconnect_attempts = 3  # Maximum reconnection attempts
```

### 2. Enhanced refresh_token() Method

```python
def refresh_token(self) -> bool:
    try:
        controller = controller_manager.get_controller(server_ip=self.server_ip)
        if not controller:
            self.auth_failed = True  # ✅ Set flag on failure
            return False

        refresh_result = controller.refresh_access_token()
        
        # Check if refresh_token has expired
        if refresh_result == "REFRESH_TOKEN_EXPIRED":
            logger.error(f"🔴 REFRESH TOKEN EXPIRED for server {self.server_ip}!")
            self.auth_failed = True  # ✅ Prevent infinite reconnection
            return False
        
        # Check if refresh was successful
        if refresh_result and isinstance(refresh_result, str):
            self.header['Authorization'] = f"Bearer {refresh_result}"
            self.auth_failed = False  # ✅ Reset flag on success
            self.reconnect_attempts = 0  # ✅ Reset counter
            return True
        
        self.auth_failed = True  # ✅ Set flag on failure
        return False
        
    except Exception as e:
        self.auth_failed = True  # ✅ Set flag on exception
        return False
```

### 3. Smart Reconnection Logic in on_close()

```python
def on_close(self, ws, close_status_code, close_msg):
    # ... logging ...
    
    # ✅ Check if we should attempt reconnection
    if self.auth_failed:
        logger.error(f"🚫 NOT RECONNECTING - Authentication failed")
        logger.error(f"🚫 User must re-login to restore WebSocket connection")
        return  # ✅ STOP infinite loop
    
    # ✅ Check reconnection attempts limit
    if self.reconnect_attempts >= self.max_reconnect_attempts:
        logger.error(f"🚫 MAX RECONNECTION ATTEMPTS REACHED")
        return  # ✅ STOP after max attempts
    
    # ✅ Attempt reconnection with counter
    self.reconnect_attempts += 1
    logger.info(f"🔄 Attempting reconnection {self.reconnect_attempts}/{self.max_reconnect_attempts}")
    time.sleep(1)
    self.connect_background()
```

### 4. Added Utility Methods

```python
def reset_auth_state(self):
    """Reset authentication state to allow reconnection after re-login."""
    logger.info(f"🔄 Resetting authentication state for server {self.server_ip}")
    self.auth_failed = False
    self.reconnect_attempts = 0

def on_open(self, ws):
    # ... existing code ...
    self.reconnect_attempts = 0  # ✅ Reset on successful connection
    logger.info(f"✅ WebSocket connected successfully for server {self.server_ip}")
```

---

## Flow Diagram

### New Smart Reconnection Flow:

```
WebSocket Connection Closed
↓
Check auth_failed flag
├─ TRUE → 🚫 STOP (Don't reconnect)
└─ FALSE → Continue
↓
Check reconnect_attempts < max_attempts
├─ FALSE → 🚫 STOP (Max attempts reached)
└─ TRUE → Continue
↓
Increment reconnect_attempts
↓
🔄 Attempt Reconnection
↓
Connection Result:
├─ Success → Reset counters ✅
├─ Auth Error → Set auth_failed=True → STOP ✅
└─ Network Error → Try again (up to max attempts) ✅
```

---

## Benefits

### ✅ **Prevents Infinite Loops**
- Authentication failures stop reconnection attempts
- Maximum reconnection attempts limit

### ✅ **Smart Error Handling**
- Different handling for auth errors vs network errors
- Clear logging for debugging

### ✅ **Resource Protection**
- No more CPU/memory waste from infinite loops
- No more log spam

### ✅ **User Experience**
- Clear error messages about authentication status
- Proper guidance for re-login requirements

### ✅ **System Stability**
- WebSocket client won't crash the application
- Graceful degradation when authentication fails

---

## Usage

### When Token Expires:
1. WebSocket gets 401 error
2. `refresh_token()` called → Returns "REFRESH_TOKEN_EXPIRED"
3. `auth_failed = True` set
4. Connection closes → `on_close()` called
5. **Reconnection STOPPED** due to `auth_failed = True`
6. User sees clear error message to re-login

### After User Re-login:
1. Call `websocket_client.reset_auth_state()`
2. Authentication state cleared
3. WebSocket can reconnect normally

### Network Issues:
1. Connection fails due to network
2. `auth_failed` remains `False`
3. Reconnection attempted up to `max_reconnect_attempts`
4. If all attempts fail → Stop reconnecting

---

## Testing Scenarios

1. **Token Expired**: ✅ Stops reconnecting after auth failure
2. **Network Issues**: ✅ Retries up to max attempts then stops
3. **Successful Reconnection**: ✅ Resets counters properly
4. **Manual Re-login**: ✅ Can reset state and reconnect
5. **Mixed Failures**: ✅ Handles different error types correctly

The infinite reconnection loop issue is now **completely resolved**! 🎉
