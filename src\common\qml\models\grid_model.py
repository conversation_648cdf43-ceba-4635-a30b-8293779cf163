from enum import IntEnum
import logging
import json
import time
import copy
import ast
import traceback
from datetime import datetime
from PySide6.QtCore import QObject, Signal, Slot, Property, QPoint,QEnum
from src.common.model.camera_model import camera_model_manager
from src.common.model.group_model import group_model_manager
from src.common.controller.main_controller import main_controller
from src.common.qml.models.list_grid_items import ListGridItems
from src.common.threads.sub_thread import SubThread
from src.styles.style import Style
from src.common.onvif_api.calib_data import CalibData
from src.common.onvif_api.worker_thread import WorkerThread
from src.common.qml.models.common_enum import CommonEnum
from src.common.qml.models.grid_item_selected import GridItemSelected
from src.common.qml.models.camera_grid_item import CameraGridItem
from src.common.qml.models.event_grid_item import EventGridItem
from src.common.qml.models.digital_map_grid_item import DigitalMapGridItem
from src.common.qml.models.floor_map_grid_item import FloorMapGridItem
from src.common.qml.models.map_controller import MapModel,BuildingModel,FloorModel,floor_manager,map_manager
from src.common.model.event_data_model import EventModel, event_manager
from queue import Queue


logger = logging.getLogger(__name__)

class GridState(QObject):
    class GridType(IntEnum):
        Normal = 0
        Tracking = 1
    QEnum(GridType)
    
    gridTypeChanged = Signal()

    def __init__(self):
        super().__init__()
        self._gridType = GridState.GridType.Normal

    @Property(int, notify=gridTypeChanged)
    def gridType(self):
        return self._gridType
    @gridType.setter
    def gridType(self, value: int):
        if self._gridType != value:
            self._gridType = value
            self.gridTypeChanged.emit()

class GridConstants:
    """
    ✅ CONSTANTS: Grid size limits and configuration

    MULTI-CELL SCENARIOS:
    - Grid 12x12 = 144 total cells
    - 1 item (2x1) = 2 cells occupied
    - Remaining: 144 - 2 = 142 cells for other items
    - Maximum items: 1 item (2x1) + 142 items (1x1) = 143 items total
    - Available slots ≠ Available cells for multi-cell items
    """
    MAX_COLUMNS = 12
    MAX_ROWS = 12
    MAX_CAPACITY = MAX_COLUMNS * MAX_ROWS  # 144 cells (not items)

    # Grid progression for expansion
    GRID_PROGRESSION = [
        (1, 1), (2, 1), (2, 2), (3, 2), (3, 3), (4, 3), (4, 4),
        (5, 4), (5, 5), (6, 5), (6, 6), (7, 6), (7, 7), (8, 7),
        (8, 8), (9, 8), (9, 9), (10, 9), (10, 10), (11, 10),
        (11, 11), (12, 11), (12, 12)
    ]

    # O(1) progression map for direct lookup
    PROGRESSION_MAP = {
        (1, 1): (2, 1), (2, 1): (2, 2), (2, 2): (3, 2), (3, 2): (3, 3),
        (3, 3): (4, 3), (4, 3): (4, 4), (4, 4): (5, 4), (5, 4): (5, 5),
        (5, 5): (6, 5), (6, 5): (6, 6), (6, 6): (7, 6), (7, 6): (7, 7),
        (7, 7): (8, 7), (8, 7): (8, 8), (8, 8): (9, 8), (9, 8): (9, 9),
        (9, 9): (10, 9), (10, 9): (10, 10), (10, 10): (11, 10), (11, 10): (11, 11),
        (11, 11): (12, 11), (12, 11): (12, 12)
    }



class GridModel(QObject):
    """
    Grid Model - Quản lý trạng thái và logic của camera grid

    Chức năng chính:
    - Quản lý kích thước grid (columns, rows) với progression tự động
    - Xử lý drop operations (camera, group, multi-selection)
    - Tính toán row/col từ pixel coordinates
    - Hỗ trợ Ctrl+Wheel scaling với grid sizes định sẵn
    - Quản lý selection state của grid items
    - Theme management và color properties
    """

    listGridItemsChanged = Signal()
    columnsChanged = Signal()
    rowsChanged = Signal()
    spacingChanged = Signal()
    isSaveChanged = Signal()
    saveSignalChanged = Signal(QObject, bool)
    tabTypeChanged = Signal()
    focusChanged = Signal()
    dataChanged = Signal()
    themeChanged = Signal()
    isDarkThemeChanged = Signal()
    backgroundColorChanged = Signal()
    foregroundColorChanged = Signal()
    borderColorChanged = Signal()
    headerColorChanged = Signal()
    itemBackgroundColorChanged = Signal()
    hoverColorChanged = Signal()
    dragActiveColorChanged = Signal()
    videoBackgroundColorChanged = Signal()
    gridSizeAnimationRequested = Signal(int, int, int, int)

    # ✅ GRID LIMITS: Drop notification signals
    partialDropCompleted = Signal(int, int, arguments=['added', 'rejected'])
    gridCapacityReached = Signal()

    selectionCountChanged = Signal()
    isSingleGridItemChanged = Signal()
    openTreeItem = Signal(dict, str)

    def __init__(self, parent=None, data: dict = {}):
        """
        Khởi tạo GridModel với cấu hình mặc định

        Args:
            parent: QObject parent
            data: Dictionary chứa dữ liệu khởi tạo
        """
        super().__init__(parent)
        self._data = data
        self._currentData: dict = copy.deepcopy(data)
        self.controller = self.get_controller()
        self._isSave: bool = True
        # Grid dimensions - bắt đầu từ 1x1
        self._columns: int = 1
        self._rows: int = 1
        self._previous_row_size = 1
        self._previous_col_size = 1
        self._spacing = 2
        self._grid_item_selected = GridItemSelected()
        # Auto expansion settings
        self._autoExpansionEnabled: bool = True

        # Focus management
        self._focusedRowCol: tuple = None

        # Grid items collection
        self._listGridItems = ListGridItems(grid_model=self)

        # ✅ GRID LIMITS: Maximum grid size constraints (constants)
        self._max_columns = GridConstants.MAX_COLUMNS
        self._max_rows = GridConstants.MAX_ROWS

        # ✅ O(1) PERFORMANCE: Real-time counters for instant calculations
        self._occupied_cells_count = 0  # Track occupied cells in real-time
        self._last_known_empty_position = 0  # Cache for next available position
        # Theme management
        main_controller.theme_change_signal.connect(self._on_theme_changed)
        self.coordinate_queue = Queue()
        self.current_time = time.time()
        self.previous_time = time.time()
        # Initialize worker threads with proper callbacks
        self.process_queue_thread = WorkerThread(parent=self, target=self.process_queue, callback=self.on_process_queue_complete)
        self.process_joystick_queue_thread = WorkerThread(parent=self, target=self.process_joystick_queue, callback=self.on_process_joystick_queue_complete)
        self.current_joystick_msg = {}
        self.previous_joystick_msg = {}

        # Theme properties với default dark theme
        self._is_dark_theme = main_controller.is_dark_theme()
        self._background_color: str = "#1a1a1a"
        self._foreground_color: str = "#ffffff"
        self._border_color: str = "#333333"
        self._header_color: str = "#1f2937"
        self._item_background_color: str = "#2d2d2d"
        self._hover_color: str = "#3d3d3d"
        self._drag_active_color: str = "#4d4d4d"
        self._video_background_color: str = "#1f2937"
        main_controller.theme_change_signal.connect(self.themeChanged)
        self._isSingleGridItem = True
        # Kết nối signal itemCountChanged của listGridItems để cập nhật isSingleGridItem
        if self._listGridItems:
            self._listGridItems.itemCountChanged.connect(self._updateIsSingleGridItem)
        self.openTreeItem.connect(self.processHandleDrop)
    def loadData(self):
        self.refreshData()
        # Nếu là SavedView, lấy thông số columns và rows từ currentGrid
        self.tabType = self.get_property("type",CommonEnum.TabType.NORMAL)
        if self.get_property("type") != CommonEnum.TabType.NORMAL:
            try:
                # Lấy thông tin currentGrid
                currentGrid = self.get_property("currentGrid")
                logger.debug(f"Grid configuration: {currentGrid}")

                # Phân tích chuỗi JSON
                if isinstance(currentGrid, str):
                    grid_config = json.loads(currentGrid.replace("'", '"'))
                    if 'row' in grid_config and 'col' in grid_config:
                        self.rows = int(grid_config['row'])
                        self.columns = int(grid_config['col'])
                        logger.debug(f"Set grid size from saved view: {self.rows}x{self.columns}")
                listGridData = {}
                grid_data_str = self.get_property('listGridData',None)
                if isinstance(grid_data_str, str):
                    logger.debug(f"🔍🔍 Grid data is string, attempting to parse {grid_data_str}")
                    try:
                        # Thử phân tích bằng json.loads
                        listGridData = json.loads(grid_data_str.replace("'", '"'))
                    except Exception as json_error:
                        logger.debug(f"🔍🔍 JSON parse error: {json_error}")
                        try:
                            # Thử phân tích bằng ast.literal_eval
                            listGridData = ast.literal_eval(grid_data_str)
                            logger.debug(f"🔍🔍 Successfully parsed grid data using ast.literal_eval")
                        except Exception as ast_error:
                            logger.debug(f"🔍🔍 AST parse error: {ast_error}")
                            # Thử một cách khác - tìm kiếm pattern trong chuỗi
                            logger.debug(f"🔍🔍 Attempting regex pattern matching")
                            try:
                                # Pattern cho id, position, width, height
                                import re
                                pattern = r"\((\d+),\s*(\d+)\):\s*{[^}]*'type':\s*<ItemType\.(\w+):\s*\d+>[^}]*'position':\s*\((\d+),\s*(\d+)\)[^}]*'id':\s*'([^']+)'[^}]*'row':\s*(\d+)[^}]*'col':\s*(\d+)[^}]*'rows_cell':\s*(\d+)[^}]*'cols_cell':\s*(\d+)[^}]*'width':\s*([\d.]+)[^}]*'height':\s*(\d+)"
                                matches = re.findall(pattern, grid_data_str)
                                # logger.debug(f"matches = {grid_data_str}")
                                # logger.debug(f"matches1 = {matches}")
                                if matches:
                                    logger.debug(f"Found {len(matches)} camera entries using regex")
                                    # Tạo listGridData từ các matches
                                    for match in matches:
                                        key_row = int(match[0])
                                        key_col = int(match[1])
                                        item_type_str = match[2]  # Lấy ra type, vd: "CAMERA"
                                        pos_row = int(match[3])
                                        pos_col = int(match[4])
                                        id = match[5]
                                        row = int(match[6])
                                        col = int(match[7])
                                        rows_cell = int(match[8])
                                        cols_cell = int(match[9])
                                        width = float(match[10])
                                        height = float(match[11])
                                        listGridData[(key_row, key_col)] = {
                                            'type': item_type_str.capitalize(), # Sử dụng type đã lấy được
                                            'position': (pos_row, pos_col),
                                            'id': id,
                                            'row': row,
                                            'col': col,
                                            'rows_cell': rows_cell,
                                            'cols_cell': cols_cell,
                                            'width': width,
                                            'height': height
                                        }
                                    logger.debug(f"Created grid data with {len(listGridData)} cameras")
                            except Exception as regex_error:
                                logger.debug(f"🔍🔍 Regex error: {regex_error}")
                # logger.debug(f"🔍🔍 Successfully parsed grid data: {listGridData}")
                elif isinstance(grid_data_str, dict):
                    logger.debug(f"🔍🔍 Grid data is already a dictionary with {len(grid_data_str)} items")
                    listGridData = grid_data_str
                if listGridData:
                    for index, item in listGridData.items():
                        if item['type'] == "Camera":
                            camera_model = camera_model_manager.get_camera_model(id=item['id'])
                            if camera_model is not None:
                                cameraGridItem = CameraGridItem(row=item.get('row',0),col=item.get('col',0),rows_cell=item.get('rows_cell',1),cols_cell=item.get('cols_cell',1))
                                cameraGridItem.cameraModel = camera_model
                                self._listGridItems.insertItemAt(item['position'], cameraGridItem)
                        elif item['type'] == "Event":
                            eventAi = event_manager.get_event(id=item['id'])
                            if eventAi is not None:
                                eventGridItem = EventGridItem(row=item.get('row',0),col=item.get('col',0),rows_cell=item.get('rows_cell',1),cols_cell=item.get('cols_cell',1))
                                eventGridItem.eventModel = eventAi
                                self._listGridItems.insertItemAt(item['position'], eventGridItem)
                        elif item['type'] == "Digital_map":
                            mapModel = map_manager.get_map_model(id=item['id'])
                            if mapModel is not None:
                                digitalGridItem = DigitalMapGridItem(row=item.get('row',0),col=item.get('col',0),rows_cell=item.get('rows_cell',1),cols_cell=item.get('cols_cell',1))
                                digitalGridItem.mapModel = mapModel
                                self._listGridItems.insertItemAt(item['position'], digitalGridItem)

                        elif item['type'] == "Floor_map":
                            floorMap = floor_manager.getFloor(id = item['id'])
                            if floorMap is not None:
                                floorMapGridItem = FloorMapGridItem(row=item.get('row',0),col=item.get('col',0),rows_cell=item.get('rows_cell',1),cols_cell=item.get('cols_cell',1))
                                floorMapGridItem.floorModel = floorMap
                                self._listGridItems.insertItemAt(item['position'], floorMapGridItem)
                                
                                    

            except Exception as e:
                logger.debug(f"Error setting grid size from saved view: {e}")
                logger.debug(traceback.format_exc())
            self.isSave = True
    
    def refreshData(self):
        self._currentData: dict = copy.deepcopy(self._data)
        self._isSave: bool = True

        # Grid dimensions - bắt đầu từ 1x1
        self._columns: int = 1
        self._rows: int = 1
        self._previous_row_size = 1
        self._previous_col_size = 1
        self._spacing = 2
        self._grid_item_selected = GridItemSelected()
        # Auto expansion settings
        self._autoExpansionEnabled: bool = True

        # Focus management
        self._focusedRowCol: tuple = None
        self._lastSelectedRowCol: tuple = None

        # Grid items collection
        self._listGridItems = ListGridItems(grid_model=self)

        # ✅ GRID LIMITS: Maximum grid size constraints (constants)
        self._max_columns = GridConstants.MAX_COLUMNS
        self._max_rows = GridConstants.MAX_ROWS

        # ✅ O(1) PERFORMANCE: Real-time counters for instant calculations
        self._occupied_cells_count = 0  # Track occupied cells in real-time
        self._last_known_empty_position = 0  # Cache for next available position

    @Property(bool,notify=isSaveChanged)
    def isSave(self):
        return self._isSave

    @isSave.setter
    def isSave(self, value: bool):
        if self._isSave != value:
            self._isSave = value
            self.isSaveChanged.emit()
            self.saveSignalChanged.emit(self, value)

    @Property(int,notify=tabTypeChanged)
    def tabType(self):
        return self.get_property("type", CommonEnum.TabType.NORMAL)

    @tabType.setter
    def tabType(self, value: int):
        if self.get_property("type", CommonEnum.TabType.NORMAL) != value:
            self.set_property("type", value)
            self.tabTypeChanged.emit()

    @Property(int, notify=columnsChanged)
    def columns(self):
        return self._columns

    @columns.setter 
    def columns(self, value: int):
        if self._columns != value:
            self._columns = value
            self.columnsChanged.emit()

    @Property(int, notify=rowsChanged)
    def rows(self):
        return self._rows

    @rows.setter
    def rows(self, value: int):
        if self._rows != value:
            self._rows = value
            self.rowsChanged.emit()

    @Property(int, notify=spacingChanged)
    def spacing(self):
        return self._spacing

    @spacing.setter
    def spacing(self, value: int):
        if self._spacing != value:
            self._spacing = value
            self.spacingChanged.emit()

    @Property(QObject)
    def gridItemSelected(self):
        return self._grid_item_selected
    # ✅ SELECTION MANAGEMENT: MVVM-compliant properties
    @Property(int, notify=selectionCountChanged)
    def selectionCount(self):
        """Get count of selected items"""
        count = 0
        items_dict = self._listGridItems.getItemsDict()
        for item in items_dict.values():
            if hasattr(item, 'selected') and item.selected:
                count += 1
        return count

    @Slot(result='QVariantList')
    def getSelectedItems(self):
        """Get list of all currently selected GridItem objects"""
        selected_items = []
        items_dict = self._listGridItems.getItemsDict()
        for item in items_dict.values():
            if hasattr(item, 'selected') and item.selected:
                selected_items.append(item)
        return selected_items

    @Slot(result='QVariantList')
    def getSelectedRowCols(self):
        """Get list of selected item (row, col) coordinates"""
        selected_coords = []
        items_dict = self._listGridItems.getItemsDict()
        for item in items_dict.values():
            if hasattr(item, 'selected') and item.selected and hasattr(item, 'row') and hasattr(item, 'col'):
                selected_coords.append((item.row, item.col))
        return selected_coords

    @Property('QVariantList', notify=focusChanged)
    def focusedRowCol(self):
        """Get currently focused item (row, col) coordinates"""
        return list(self._focusedRowCol) if self._focusedRowCol else []

    @Property('QVariantList', notify=selectionCountChanged)
    def lastSelectedRowCol(self):
        """Get last selected item (row, col) coordinates"""
        return list(self._lastSelectedRowCol) if self._lastSelectedRowCol else []

    @Slot(bool)
    def enableAlternativeSelectionIndicator(self, enable: bool):
        """
       Enable/disable alternative selection indicator for performance testing
        """
        logger.debug(f"🔄 [ALT_SELECTION] Alternative selection indicator: {'ENABLED' if enable else 'DISABLED'}")
        # This will be used by QML to toggle between Rectangle border and alternative indicator
    @Slot(int, int, bool)
    def selectItemAt(self, row: int, col: int, multiSelect: bool = False):
        """
        Select item at row/col position

        Args:
            row: Hàng của item cần chọn
            col: Cột của item cần chọn
            multiSelect: True để thêm vào selection, False để thay thế selection
        """
        try:
            if not multiSelect:
                self.clearAllSelections()

            item = self._listGridItems.getItemAt(row, col)
            if item and hasattr(item, 'selected'):
                old_selected = item.selected
                item.selected = True
                self.selectionCountChanged.emit()
            else:
                logger.warning(f"No item found at ({row},{col}) or item has no 'selected' property")
        except Exception as e:
            logger.error(f"Error selecting item at ({row},{col}): {e}")

    @Slot()
    def clearAllSelections(self):
        """Clear all selections"""
        try:
            all_items = self._listGridItems.getAllItems()
            cleared_count = 0

            for item in all_items:
                if hasattr(item, 'selected') and item.selected:
                    item.selected = False
                    cleared_count += 1
            self.selectionCountChanged.emit()
        except Exception as e:
            logger.error(f"Error clearing selections: {e}")

    @Slot(int, int)
    def deselectItemAt(self, row: int, col: int):
        """
        Deselect item at row/col position

        Args:
            row: Hàng của item cần bỏ chọn
            col: Cột của item cần bỏ chọn
        """
        try:
            item = self._listGridItems.getItemAt(row, col)
            if item and hasattr(item, 'selected') and item.selected:
                item.selected = False
                self.selectionCountChanged.emit()
        except Exception as e:
            logger.error(f"Error deselecting item at ({row},{col}): {e}")

    @Slot(int, int)
    def toggleSelectionAt(self, row: int, col: int):
        """
        Toggle selection state of item at row/col

        Args:
            row: Hàng của item
            col: Cột của item
        """
        try:
            item = self._listGridItems.getItemAt(row, col)
            if item and hasattr(item, 'selected'):
                old_selected = item.selected
                item.selected = not item.selected
                self.selectionCountChanged.emit()
            else:
                logger.warning(f"No item found at ({row},{col}) for toggle")
        except Exception as e:
            logger.error(f"Error toggling selection at ({row},{col}): {e}")

    @Slot()
    def clearSelectionRowCol(self):
        """Clear selection"""
        self.clearAllSelections()

    @Slot(result=bool)
    def hasSelection(self) -> bool:
        """Check if any items are selected"""
        items_dict = self._listGridItems.getItemsDict()
        for item in items_dict.values():
            if hasattr(item, 'selected') and item.selected:
                return True
        return False

    @Slot(int, int, result=bool)
    def isItemSelectedAt(self, row: int, col: int) -> bool:
        """
        Check if item at row/col is selected using item property

        Args:
            row: Hàng cần kiểm tra
            col: Cột cần kiểm tra

        Returns:
            bool: True nếu item đang được chọn
        """
        try:
            item = self._listGridItems.getItemAt(row, col)
            if item and hasattr(item, 'selected'):
                return item.selected
            return False
        except Exception as e:
            logger.error(f"Error checking selection at ({row},{col}): {e}")
            return False

    @Slot()
    def selectAll(self):
        """Optimized select all items with batch processing"""
        try:
            # ✅ OPTIMIZED: Use direct dict iteration instead of list conversion
            items_dict = self._listGridItems.getItemsDict()

            if not items_dict:
                return

            # Batch update all items without individual signal emissions
            selected_count = 0
            last_item = None
            for item in items_dict.values():
                if hasattr(item, '_selected') and not item._selected:
                    item._selected = True
                    selected_count += 1
                last_item = item

            # Update last selected position
            if last_item:
                if hasattr(last_item, 'row') and hasattr(last_item, 'col'):
                    self._lastSelectedRowCol = (last_item.row, last_item.col)

            # Single signal emission for all changes
            if selected_count > 0:
                # Emit selectedChanged for all items at once
                for item in items_dict.values():
                    if hasattr(item, 'selectedChanged'):
                        item.selectedChanged.emit()

                self.selectionCountChanged.emit()

        except Exception as e:
            logger.error(f"Error in selectAll: {e}")

    @Slot(int, int)
    def selectRange(self, end_row: int, end_col: int):
        """
        Sequential selection from last selected to end position

        Args:
            end_row: End row for range selection
            end_col: End col for range selection
        """
        try:
            if not self._lastSelectedRowCol:
                self.selectItemAt(end_row, end_col, False)
                return

            start_row, start_col = self._lastSelectedRowCol

            # Convert positions to linear indices for sequential selection
            start_index = start_row * self.columns + start_col
            end_index = end_row * self.columns + end_col

            # Ensure start <= end for sequential selection
            min_index = min(start_index, end_index)
            max_index = max(start_index, end_index)

            # Clear current selection
            self.clearAllSelections()

            # Select all items sequentially from min_index to max_index
            # ✅ OPTIMIZED: Use direct dict iteration instead of list conversion
            items_dict = self._listGridItems.getItemsDict()

            # Create position map for existing items
            item_positions = {}
            for item in items_dict.values():
                if hasattr(item, 'row') and hasattr(item, 'col'):
                    linear_index = item.row * self.columns + item.col
                    item_positions[linear_index] = item

            # Select items in sequential order
            for linear_index in range(min_index, max_index + 1):
                if linear_index in item_positions:
                    item = item_positions[linear_index]
                    if hasattr(item, 'selected'):
                        item.selected = True

            self._lastSelectedRowCol = (end_row, end_col)
            self.selectionCountChanged.emit()

        except Exception as e:
            logger.error(f"Error selecting sequential range: {e}")

    @Slot(int, int, int, int)
    def selectRectangle(self, start_row: int, start_col: int, end_row: int, end_col: int):
        """Select items in rectangle area"""
        try:
            min_row = min(start_row, end_row)
            max_row = max(start_row, end_row)
            min_col = min(start_col, end_col)
            max_col = max(start_col, end_col)

            items_dict = self._listGridItems.getItemsDict()

            # Generate rectangle coordinates using itertools.product
            import itertools
            rectangle_coords = set(itertools.product(
                range(min_row, max_row + 1),
                range(min_col, max_col + 1)
            ))

            # Set intersection - only items that exist in both rectangle and items_dict
            target_positions = rectangle_coords & items_dict.keys()

            items_to_deselect = []
            items_to_select = []

            for (row, col), item in items_dict.items():
                if hasattr(item, 'selected'):
                    is_currently_selected = item.selected
                    should_be_selected = (row, col) in target_positions

                    if is_currently_selected and not should_be_selected:
                        items_to_deselect.append(item)
                    elif not is_currently_selected and should_be_selected:
                        items_to_select.append(item)

            changes_made = len(items_to_deselect) > 0 or len(items_to_select) > 0

            if changes_made:
                for item in items_to_deselect:
                    item.selected = False

                for item in items_to_select:
                    item.selected = True

                self.selectionCountChanged.emit()

        except Exception as e:
            logger.error(f"Error selecting rectangle: {e}")

    #Focus management methods
    @Slot(int, int)
    def setFocusAt(self, row: int, col: int):
        """Set focus to item at row/col"""
        try:
            if self._focusedRowCol != (row, col):
                # Clear previous focus
                if self._focusedRowCol:
                    prev_item = self._listGridItems.getItemAt(self._focusedRowCol[0], self._focusedRowCol[1])
                    if prev_item and hasattr(prev_item, 'focused'):
                        prev_item.focused = False

                # Set new focus
                self._focusedRowCol = (row, col)
                item = self._listGridItems.getItemAt(row, col)
                if item and hasattr(item, 'focused'):
                    item.focused = True

                self.focusChanged.emit()

        except Exception as e:
            logger.error(f"Error setting focus: {e}")

    @Slot()
    def clearFocus(self):
        """Clear current focus"""
        try:
            if self._focusedRowCol:
                item = self._listGridItems.getItemAt(self._focusedRowCol[0], self._focusedRowCol[1])
                if item and hasattr(item, 'focused'):
                    item.focused = False
                self._focusedRowCol = None
                self.focusChanged.emit()
        except Exception as e:
            logger.error(f"Error clearing focus: {e}")

    @Slot(str)
    def handleKeyPress(self, key: str):
        """
       Handle keyboard navigation

        Args:
            key: Key string ("Up", "Down", "Left", "Right", "Space", "Escape", "Ctrl+A")
        """
        try:
            if key == "Escape":
                self.clearSelectionRowCol()
                self.clearFocus()
            elif key == "Ctrl+A":
                self.selectAll()
            elif key in ["Up", "Down", "Left", "Right"]:
                self._handleArrowKey(key)
            elif key == "Space":
                self._handleSpaceKey()

        except Exception as e:
            logger.error(f"Error handling key {key}: {e}")

    def _handleArrowKey(self, direction: str):
        """Handle arrow key navigation"""
        try:
            # If no focus, set focus to first item
            if not self._focusedRowCol:
                # ✅ OPTIMIZED: Use direct dict iteration instead of list conversion
                items_dict = self._listGridItems.getItemsDict()
                if items_dict:
                    # Get first item from dict values
                    first_item = next(iter(items_dict.values()))
                    self.setFocusAt(first_item.row, first_item.col)
                return

            current_row, current_col = self._focusedRowCol
            new_row, new_col = current_row, current_col

            # Calculate new position
            if direction == "Up":
                new_row = max(0, current_row - 1)
            elif direction == "Down":
                new_row = min(self.rows - 1, current_row + 1)
            elif direction == "Left":
                new_col = max(0, current_col - 1)
            elif direction == "Right":
                new_col = min(self.columns - 1, current_col + 1)

            # Check if there's an item at new position
            item = self._listGridItems.getItemAt(new_row, new_col)
            if item:
                self.setFocusAt(new_row, new_col)
            else:
                # Find nearest item in that direction
                self._findNearestItem(direction, current_row, current_col)

        except Exception as e:
            logger.error(f"Error handling arrow key {direction}: {e}")

    def _handleSpaceKey(self):
        """Handle space key - toggle selection of focused item"""
        if self._focusedRowCol:
            row, col = self._focusedRowCol
            self.toggleSelectionAt(row, col)

    def _findNearestItem(self, direction: str, start_row: int, start_col: int):
        """Find nearest item in given direction"""
        try:
            # ✅ OPTIMIZED: Use direct dict iteration instead of list conversion
            items_dict = self._listGridItems.getItemsDict()
            if not items_dict:
                return

            best_item = None
            best_distance = float('inf')

            for item in items_dict.values():
                if direction == "Up" and item.row < start_row:
                    distance = start_row - item.row + abs(item.col - start_col)
                elif direction == "Down" and item.row > start_row:
                    distance = item.row - start_row + abs(item.col - start_col)
                elif direction == "Left" and item.col < start_col:
                    distance = start_col - item.col + abs(item.row - start_row)
                elif direction == "Right" and item.col > start_col:
                    distance = item.col - start_col + abs(item.row - start_row)
                else:
                    continue

                if distance < best_distance:
                    best_distance = distance
                    best_item = item

            if best_item:
                self.setFocusAt(best_item.row, best_item.col)

        except Exception as e:
            logger.error(f"Error finding nearest item: {e}")



    def autoExpandGrid(self) -> bool:
        """
        Tự động mở rộng grid để phù hợp với số lượng camera hiện tại

        Returns:
            bool: True nếu grid đã được mở rộng
        """
        try:
            if not self._autoExpansionEnabled:
                return False
            camera_count = self._listGridItems.itemCount
            if camera_count <= 0:
                return False

            # Tính toán kích thước grid tối ưu
            optimal_cols, optimal_rows = self.calculateOptimalGridSize(camera_count)
            current_capacity = self._columns * self._rows
            needed_capacity = camera_count

            # Kiểm tra có cần mở rộng không
            if needed_capacity <= current_capacity and (self._columns, self._rows) == (optimal_cols, optimal_rows):
                return False

            # Cập nhật kích thước grid
            self._columns = optimal_cols
            self._rows = optimal_rows
            self.columnsChanged.emit()
            self.rowsChanged.emit()

            # ✅ PHASE 4: Notify items about auto expansion
            self._notifyItemsGridExpansion(optimal_cols, optimal_rows)

            return True
        except Exception as e:
            logger.error(f"Error in auto expand grid: {e}")
            return False



    @Slot(float, float, float, float, result='QVariant')
    def calculateGridRowCol(self, x: float, y: float, cellWidth: float, cellHeight: float):
        """
        Tính toán row/col từ tọa độ pixel

        Args:
            x: Tọa độ X trong pixels
            y: Tọa độ Y trong pixels
            cellWidth: Chiều rộng của mỗi cell
            cellHeight: Chiều cao của mỗi cell

        Returns:
            dict: {"row": int, "col": int} - Tọa độ grid
        """
        try:
            if cellWidth <= 0 or cellHeight <= 0:
                return {"row": 0, "col": 0}

            # Tính toán raw coordinates
            raw_col = x / cellWidth
            raw_row = y / cellHeight
            col = int(raw_col)
            row = int(raw_row)

            # Clamp vào bounds của grid
            clamped_col = max(0, min(col, self._columns - 1))
            clamped_row = max(0, min(row, self._rows - 1))

            return {"row": clamped_row, "col": clamped_col}
        except Exception as e:
            logger.error(f"Error calculating grid row/col: {e}")
            return {"row": 0, "col": 0}







    @Slot(int, result=bool)
    def handleCtrlWheel(self, delta: int) -> bool:
        """
        Xử lý Ctrl+Wheel để scale grid theo các kích thước định sẵn

        Args:
            delta: Giá trị wheel (dương = zoom in/grid nhỏ hơn, âm = zoom out/grid lớn hơn)

        Returns:
            bool: True nếu grid size đã thay đổi
        """
        try:
            # Danh sách các kích thước grid được hỗ trợ
            grid_sizes = [
                (1, 1), (2, 1), (2, 2), (3, 2), (3, 3), (4, 3),
                (4, 4), (5, 4), (5, 5), (6, 5), (6, 6), (7, 6),
                (7, 7), (8, 7), (8, 8), (9, 8), (9, 9), (10, 9),
                (10, 10), (11, 10), (11, 11), (12, 11), (12, 12)
            ]

            # Tìm index hiện tại trong danh sách
            current_size = (self._columns, self._rows)
            current_index = -1
            for i, size in enumerate(grid_sizes):
                if size == current_size:
                    current_index = i
                    break

            # Nếu không tìm thấy, tìm size gần nhất
            if current_index == -1:
                total_cells = self._columns * self._rows
                closest_index = 0
                closest_diff = float('inf')
                for i, (cols, rows) in enumerate(grid_sizes):
                    diff = abs(cols * rows - total_cells)
                    if diff < closest_diff:
                        closest_diff = diff
                        closest_index = i
                current_index = closest_index

            # Tính toán index mới
            if delta > 0:
                new_index = max(0, current_index - 1)  # Zoom in = grid nhỏ hơn
            else:
                new_index = min(len(grid_sizes) - 1, current_index + 1)  # Zoom out = grid lớn hơn

            # Kiểm tra có thay đổi không
            if new_index == current_index:
                return False

            new_cols, new_rows = grid_sizes[new_index]

            # Xử lý mở rộng hoặc thu nhỏ
            if new_index > current_index:
                # Mở rộng grid - luôn được phép
                self._columns = new_cols
                self._rows = new_rows
            else:
                # Thu nhỏ grid - cần kiểm tra có items bị mất không
                if not self.canShrinkGridRowCol(new_cols, new_rows):
                    return False
                self._columns = new_cols
                self._rows = new_rows

            # Emit signals để update UI
            self.columnsChanged.emit()
            self.rowsChanged.emit()

            # ✅ PHASE 4: Notify all items about grid expansion
            self._notifyItemsGridExpansion(new_cols, new_rows)

            return True
        except Exception as e:
            logger.error(f"Error handling Ctrl+Wheel: {e}")
            return False

    def _notifyItemsGridExpansion(self, new_cols: int, new_rows: int):
        """
        ✅ PHASE 4: Notify all camera items about grid expansion

        Args:
            new_cols: New total grid columns
            new_rows: New total grid rows
        """
        try:
            if not self._listGridItems:
                return

            # Get all camera items and notify them about grid expansion
            items = self._listGridItems.getAllItems()
            for item in items:
                if hasattr(item, 'handleGridExpansion') and callable(getattr(item, 'handleGridExpansion')):
                    item.handleGridExpansion(new_rows, new_cols)

            logger.debug(f"🚀 [GRID_EXPANSION] Notified {len(items)} items about grid expansion to {new_cols}x{new_rows}")

        except Exception as e:
            logger.error(f"Error notifying items about grid expansion: {e}")

    def canShrinkGridRowCol(self, new_rows: int, new_cols: int) -> bool:
        """
        Kiểm tra có thể thu nhỏ grid xuống kích thước mới không

        Args:
            new_rows: Số hàng mới
            new_cols: Số cột mới

        Returns:
            bool: True nếu có thể thu nhỏ mà không mất items
        """
        try:
            items_outside = []

            # Kiểm tra tất cả items hiện tại
            for row_col, item in self._listGridItems._items.items():
                row, col = row_col

                # Kiểm tra vị trí bắt đầu của item
                if row >= new_rows or col >= new_cols:
                    items_outside.append((item, row, col, "coordinates outside new grid"))
                    continue

                # Kiểm tra vị trí kết thúc của multi-cell item
                end_row = row + item.rows_cell - 1
                end_col = col + item.cols_cell - 1
                if end_row >= new_rows or end_col >= new_cols:
                    items_outside.append((item, row, col, f"multi-cell extends to ({end_row},{end_col})"))
                    continue

            # Nếu có items bị ảnh hưởng thì không thể thu nhỏ
            if items_outside:
                return False
            return True
        except Exception as e:
            logger.error(f"Error checking if grid can shrink (row/col): {e}")
            return False

    @Property(bool, notify=themeChanged)
    def isDarkTheme(self):
        return self._is_dark_theme

    @isDarkTheme.setter
    def isDarkTheme(self, value):
        if self._is_dark_theme != value:
            self._is_dark_theme = value
            self.themeChanged.emit()
            
    @Property(str, notify=backgroundColorChanged)
    def backgroundColor(self):
        return self._background_color

    @backgroundColor.setter
    def backgroundColor(self, value):
        if self._background_color != value:
            self._background_color = value
            self.backgroundColorChanged.emit()

    @Property(str, notify=foregroundColorChanged)
    def foregroundColor(self):
        return self._foreground_color

    @foregroundColor.setter
    def foregroundColor(self, value):
        if self._foreground_color != value:
            self._foreground_color = value
            self.foregroundColorChanged.emit()

    @Property(str, notify=borderColorChanged)
    def borderColor(self):
        return self._border_color

    @borderColor.setter
    def borderColor(self, value):
        if self._border_color != value:
            self._border_color = value
            self.borderColorChanged.emit()

    @Property(str, notify=headerColorChanged)
    def headerColor(self):
        return self._header_color

    @headerColor.setter
    def headerColor(self, value):
        if self._header_color != value:
            self._header_color = value
            self.headerColorChanged.emit()

    @Property(str, notify=itemBackgroundColorChanged)
    def itemBackgroundColor(self):
        return self._item_background_color

    @itemBackgroundColor.setter
    def itemBackgroundColor(self, value):
        if self._item_background_color != value:
            self._item_background_color = value
            self.itemBackgroundColorChanged.emit()

    @Property(str, notify=hoverColorChanged)
    def hoverColor(self):
        return self._hover_color

    @hoverColor.setter
    def hoverColor(self, value):
        if self._hover_color != value:
            self._hover_color = value
            self.hoverColorChanged.emit()

    @Property(str, notify=dragActiveColorChanged)
    def dragActiveColor(self):
        return self._drag_active_color

    @dragActiveColor.setter
    def dragActiveColor(self, value):
        if self._drag_active_color != value:
            self._drag_active_color = value
            self.dragActiveColorChanged.emit()

    @Property(str, notify=videoBackgroundColorChanged)
    def videoBackgroundColor(self):
        return self._video_background_color

    @videoBackgroundColor.setter
    def videoBackgroundColor(self, value):
        if self._video_background_color != value:
            self._video_background_color = value
            self.videoBackgroundColorChanged.emit()

    @Slot("QVariant", str)
    def processHandleDrop(self, raw_data, itemType):
        """
        Generic drop handler hỗ trợ nhiều loại item

        Args:
            raw_data: Raw data từ QML drop event
            itemType: Loại item ("camera", "group", "multi-selection")
        """
        self.isSave = False
        try:
            data = self._parseDropData(raw_data)
            if not data:
                logger.error(f"Failed to parse drop data: {raw_data}")
                return False

            success = self._dispatchDropByType(data, itemType)

            if not success:
                logger.debug(f"Failed to process drop for itemType: {itemType}")

            return success

        except Exception as e:
            logger.error(f"Error processing drop: {str(e)}")
            return False

    def _dispatchDropByType(self, data, itemType):
        """
        Dispatch drop operation theo loại item

        Args:
            data: Parsed drop data
            itemType: Loại item cần xử lý

        Returns:
            bool: True nếu xử lý thành công
        """
        if itemType in ["Camera", "Group", "multi-selection","DigitalMap","FloorMap","Event"]:
            logger.debug(f"Handling drop for itemType: {itemType}")
            return self._handleCameraTypeDrop(data, itemType)
        else:
            logger.error(f"Unsupported item type: {itemType}")
            return False

    def _handleCameraTypeDrop(self, data, itemType):
        """
        ✅ GRID LIMITS: Xử lý drop cho camera-related items với size validation

        Args:
            data: Parsed drop data
            itemType: "camera", "group", hoặc "multi-selection","DigitalMap","FloorMap"

        Returns:
            bool: True nếu thêm cameras thành công
        """
        items = []

        if itemType == "Camera":
            camera_id = {"id":data.get('id') or (data.get('data', {}).get('id') if isinstance(data.get('data'), dict) else None),"type":itemType}
            if camera_id:
                items.append(camera_id)
        elif itemType == "Group":
            group_id = data.get('id') or (data.get('data', {}).get('id') if isinstance(data.get('data'), dict) else None)
            group_camera_ids = self._getGroupCameraIds(group_id)

            items.extend(group_camera_ids)
        elif itemType == "multi-selection":
            multi_camera_ids = self._getMultiSelectionCameraIds(data)
            items.extend(multi_camera_ids)
        elif itemType == "DigitalMap":
            map_id = {"id":data.get('id') or (data.get('data', {}).get('id') if isinstance(data.get('data'), dict) else None),"type":itemType}
            if map_id:
                items.append(map_id)
        elif itemType == "FloorMap":
            floor_id = {"id":data.get('id') or (data.get('data', {}).get('id') if isinstance(data.get('data'), dict) else None),"type":itemType}
            if floor_id:
                items.append(floor_id)
        elif itemType == "Event":
            event_id = {"id":data.get('id') or (data.get('data', {}).get('id') if isinstance(data.get('data'), dict) else None),"type":itemType}
            if event_id:
                items.append(event_id)
        if items:
            available_slots = self._getAvailableGridSlots()



            if len(items) <= available_slots:
                # All cameras can fit
                result = self.addItemBatch(items)
                logger.debug(f"✅ [GRID_LIMIT] Added all {len(items)} cameras successfully")
                return result
            elif available_slots > 0:
                # Partial fit - add what we can
                cameras_to_add = items[:available_slots]
                cameras_rejected = items[available_slots:]

                result = self.addItemBatch(cameras_to_add)

                # ✅ GRID LIMITS: Emit partial drop signal for UI notification
                self.partialDropCompleted.emit(len(cameras_to_add), len(cameras_rejected))
                logger.debug(f"⚠️ [GRID_LIMIT] Partial drop: Added {len(cameras_to_add)} cameras, rejected {len(cameras_rejected)} (grid full at {GridConstants.MAX_COLUMNS}x{GridConstants.MAX_ROWS})")
                return result
            else:
                # No space available
                self.gridCapacityReached.emit()
                logger.debug(f"🚫 [GRID_LIMIT] Cannot add any cameras - grid is full at {GridConstants.MAX_COLUMNS}x{GridConstants.MAX_ROWS} capacity")
                return False
        else:
            return False


    def _parseDropData(self, raw_data):
        """
        Parse drop data from various formats

        Args:
            raw_data: Raw data từ QML drop event

        Returns:
            dict/list: Parsed data hoặc None nếu parse failed
        """
        try:
            if hasattr(raw_data, 'toVariant'):
                return raw_data.toVariant()
            elif hasattr(raw_data, 'toJson'):
                try:
                    json_str = raw_data.toJson()
                    return json.loads(json_str)
                except Exception:
                    return None
            elif isinstance(raw_data, dict):
                return raw_data
            elif hasattr(raw_data, 'data'):
                return raw_data.data
            else:
                return raw_data

        except Exception as e:
            logger.error(f"Error parsing drop data: {e}")
            return None

    def _getGroupCameraIds(self, group_id):
        """
        Get camera IDs from group

        Args:
            group_id: ID của group

        Returns:
            list: Danh sách camera IDs trong group
        """
        if not group_id:
            return []

        try:
            group_model = group_model_manager.get_group_model(id=group_id)
            if group_model:
                camera_ids = []
                if group_model.get_property("cameraIds") is not None:
                    for id in group_model.get_property("cameraIds"):
                        camera_ids.append({"id":id,"type":"Camera"})
                return camera_ids
            else:
                return []
        except Exception as e:
            logger.error(f"Error getting group cameras: {e}")
            return []

    def _getMultiSelectionCameraIds(self, data):
        """
        Get camera IDs from multi-selection data

        Args:
            data: Multi-selection data (dict hoặc list)

        Returns:
            list: Danh sách camera IDs từ multi-selection
        """
        items = []

        try:
            if isinstance(data, dict):
                # Format: {id: {type: "Camera/Group", ...}, ...}
                for item_id, item_info in data.items():
                    if isinstance(item_info, dict):
                        item_type = item_info.get('type', '')

                        if item_type == "Camera":
                            items.append({"id":item_id,"type":item_type})
                        elif item_type == "Group":
                            group_cameras = self._getGroupCameraIds(item_id)
                            items.extend(group_cameras)
                        elif item_type == "DigitalMap":
                            items.append({"id":item_id,"type":item_type})
                        elif item_type == "FloorMap":
                            items.append({"id":item_id,"type":item_type})
            elif isinstance(data, list):
                # Format: [{"id": "...", "type": "Camera/Group"}, ...]
                for item in data:
                    if isinstance(item, dict):
                        item_id = item.get('id')
                        item_type = item.get('type', '')

                        if item_type == "Camera" and item_id:
                            items.append({"id":item_id,"type":item_type})
                        elif item_type == "Group" and item_id:
                            group_cameras = self._getGroupCameraIds(item_id)
                            items.extend(group_cameras)
                        elif item_type == "DigitalMap":
                            items.append({"id":item_id,"type":item_type})
                        elif item_type == "FloorMap":
                            items.append({"id":item_id,"type":item_type})

            return items

        except Exception as e:
            logger.error(f"Error processing multi-selection: {e}")
            return []


        
    @Slot(str, result=bool)
    def addCamera(self, camera_id: str) -> bool:
        """✅ O(1) PERFORMANCE: Add camera with early validation"""
        try:
            # ✅ O(1) EARLY VALIDATION: Check capacity before processing
            available_cells = self._getAvailableGridSlots()
            if available_cells <= 0:
                logger.debug(f"🚫 [EARLY_CHECK] Grid full - cannot add camera {camera_id}")
                self.gridCapacityReached.emit()
                return False

            camera_model = camera_model_manager.get_camera_model(id=camera_id)
            if not camera_model:
                return False

            return self._listGridItems.addCameraItem(camera_model)

        except Exception as e:
            logger.error(f"Error adding camera {camera_id}: {e}")
            return False



    def _expandGridIfNeeded(self) -> bool:
        """
        Mở rộng grid theo progression nếu cần
        """
        # Kiểm tra xem có item nào vượt quá bounds hiện tại không
        needs_expansion = False
        required_cols = self._columns
        required_rows = self._rows

        # ✅ OPTIMIZED: Use direct dict iteration instead of list conversion
        items_dict = self._listGridItems.getItemsDict()
        for item in items_dict.values():
            item_end_row = item.row + item.rows_cell - 1
            item_end_col = item.col + item.cols_cell - 1

            if item_end_row >= self._rows or item_end_col >= self._columns:
                needs_expansion = True
                required_cols = max(required_cols, item_end_col + 1)
                required_rows = max(required_rows, item_end_row + 1)

        if not needs_expansion:
            return True

        # ✅ GRID LIMITS: Sử dụng progression với size validation
        grid_size = self._getNextGridSizeForRequirement(required_cols, required_rows)
        if grid_size is None:
            logger.debug(f"🚫 [GRID_LIMIT] Cannot expand grid for requirement {required_cols}x{required_rows} - exceeds {GridConstants.MAX_COLUMNS}x{GridConstants.MAX_ROWS} limit")
            return False

        new_cols, new_rows = grid_size
        logger.debug(f"🔍 [EXPAND_GRID] Expanding from {self._columns}x{self._rows} to {new_cols}x{new_rows}")
        return self._batchUpdateGridDimensions(new_cols, new_rows)

    def _getNextGridSizeForRequirement(self, min_cols: int, min_rows: int) -> tuple:
        """
        Lấy kích thước grid tiếp theo đủ lớn cho requirement (max 12x12)
        """
        # Check if requirement exceeds maximum
        if min_cols > GridConstants.MAX_COLUMNS or min_rows > GridConstants.MAX_ROWS:
            logger.debug(f"🚫 [GRID_LIMIT] Requirement {min_cols}x{min_rows} exceeds maximum {GridConstants.MAX_COLUMNS}x{GridConstants.MAX_ROWS}")
            return None

        # Predefined progression (capped at 12x12)
        grid_progression = [
            (1, 1), (2, 1), (2, 2), (3, 2), (3, 3), (4, 3), (4, 4),
            (5, 4), (5, 5), (6, 5), (6, 6), (7, 6), (7, 7), (8, 7),
            (8, 8), (9, 8), (9, 9), (10, 9), (10, 10), (11, 10),
            (11, 11), (12, 11), (12, 12)
        ]

        # Tìm kích thước đầu tiên đủ lớn
        for cols, rows in grid_progression:
            if cols >= min_cols and rows >= min_rows:
                return (cols, rows)

        # If no progression matches and within limits, return requirement
        if min_cols <= GridConstants.MAX_COLUMNS and min_rows <= GridConstants.MAX_ROWS:
            return (min_cols, min_rows)

        # Requirement exceeds limits
        return None

    @Slot('QStringList', result=bool)
    def addItemBatch(self, items: list) -> bool:
        """
        ✅ O(1) PERFORMANCE: Add multiple cameras with early validation

        Args:
            items: List camera IDs cần thêm

        Returns:
            bool: True nếu thêm thành công ít nhất 1 camera
        """
        # try:
        if not items:
            return True

        # ✅ O(1) EARLY VALIDATION: Check capacity before processing
        available_cells = self._getAvailableGridSlots()
        if available_cells <= 0:
            logger.debug(f"🚫 [EARLY_CHECK] Grid full - cannot add {len(items)} cameras")
            self.gridCapacityReached.emit()
            return False

        # ✅ O(1) PARTIAL VALIDATION: Determine how many can fit
        cameras_to_process = min(len(items), available_cells)
        if cameras_to_process < len(items):
            logger.debug(f"⚠️ [EARLY_CHECK] Partial add: {cameras_to_process}/{len(items)} cameras can fit")

        # Get camera models từ camera_model_manager
        camera_models = []
        map_models = []
        floor_models = []
        event_models = []
        for item in items[:cameras_to_process]:
            item_type = item.get("type",None)
            if item_type == "Camera":
                camera_model = camera_model_manager.get_camera_model(id=item.get("id",None))
                if camera_model:
                    camera_models.append(camera_model)
            elif item_type == "DigitalMap":
                map_model = map_manager.get_map_model(id=item.get("id",None))
                if map_model:
                    map_models.append(map_model)
            elif item_type == "FloorMap":
                floor_model = floor_manager.getFloor(id=item.get("id",None))
                if floor_model:
                    floor_models.append(floor_model)
            elif item_type == "Event":
                logger.info(f"list_models = {item}")
                eventModel = event_manager.get_event(id=item.get("id",None))
                logger.info(f"list_models2 = {eventModel}")
                if eventModel:
                    event_models.append(eventModel)      

        if not camera_models and not map_models and not floor_models and not event_models:
            return False
        list_models = camera_models + map_models + floor_models + event_models
        logger.info(f"list_models1 = {list_models}")
        # Add cameras using optimized algorithm
        added_count = self._listGridItems.addItems(list_models)

        # ✅ EMIT PARTIAL SIGNAL: If some cameras were rejected
        if cameras_to_process < len(items):
            rejected_count = len(items) - cameras_to_process
            self.partialDropCompleted.emit(added_count, rejected_count)

        return added_count > 0

        # except Exception as e:
        #     logger.error(f"Error in batch add cameras: {e}")
        #     return False
         
    @Slot(QObject)
    def removeItem(self, item: QObject):
        """Remove item with selection cleanup"""
        # Check if item was selected before removal
        was_selected = item and hasattr(item, 'selected') and item.selected

        self._listGridItems.removeItem(item)

        # Update selection count if item was selected
        if was_selected:
            self.selectionCountChanged.emit()

    # ✅ REMOVED: Complex action signals - using direct method calls instead

    @Slot(int, int, result=bool)
    def removeItemAt(self, row: int, col: int) -> bool:
        """Remove camera at row/col coordinates with proper cleanup"""
        try:
            # logger.debug(f"🗑️ [DELETE] removeItemAt({row},{col})")

            if not self._listGridItems.hasItemAt(row, col):
                logger.warning(f"⚠️ [DELETE] No item found at ({row},{col})")
                return False

            # Check if item was selected before removal
            item = self._listGridItems.getItemAt(row, col)
            was_selected = item and hasattr(item, 'selected') and item.selected

            if not self._listGridItems.removeItemAt(row, col):
                logger.error(f"❌ [DELETE] Failed to remove item at ({row},{col})")
                return False

            # Update selection count if item was selected
            if was_selected:
                self.selectionCountChanged.emit()

            return True

        except Exception as e:
            logger.error(f"❌ [DELETE] Error removing camera at row/col ({row},{col}): {e}")
            return False

    @Slot('QVariantList', result=int)
    def removeMultipleCamerasAt(self, positions: list) -> int:
        """
        Batch camera removal API with selection cleanup

        Args:
            positions: List of position objects with 'row' and 'col' properties

        Returns:
            int: Number of cameras successfully removed
        """
        logger.debug(f"🗑️ [DELETE] removeMultipleCamerasAt() called with {len(positions) if positions else 0} positions")

        if not positions:
            logger.debug(f"⚠️ [DELETE] No positions provided")
            return 0

        # Convert QML positions to Python tuples
        position_tuples = []
        for pos in positions:
            if isinstance(pos, dict) and 'row' in pos and 'col' in pos:
                position_tuples.append((pos['row'], pos['col']))
                logger.debug(f"📍 [DELETE] Added position ({pos['row']},{pos['col']}) to delete list")
            elif isinstance(pos, (list, tuple)) and len(pos) >= 2:
                position_tuples.append((pos[0], pos[1]))
                logger.debug(f"📍 [DELETE] Added position ({pos[0]},{pos[1]}) to delete list")

        if not position_tuples:
            logger.warning(f"⚠️ [DELETE] No valid positions found")
            return 0

        # Check if any items to be removed are selected
        had_selected_items = False
        for row, col in position_tuples:
            item = self._listGridItems.getItemAt(row, col)
            if item and hasattr(item, 'selected') and item.selected:
                had_selected_items = True
                break

        # Remove items in batch
        removed_count = self._listGridItems.removeMultipleItemsBatch(position_tuples)

        # Update selection count if any selected items were removed
        if had_selected_items and removed_count > 0:
            self.selectionCountChanged.emit()

        return removed_count

    def _batchUpdateGridDimensions(self, new_cols: int, new_rows: int, emit_signals: bool = True) -> bool:
        """
        Update grid dimensions with optional signal batching
        Returns True if dimensions actually changed
        """
        cols_changed = self._columns != new_cols
        rows_changed = self._rows != new_rows

        if not cols_changed and not rows_changed:
            return False

        # Update internal state
        if cols_changed:
            self._columns = new_cols
        if rows_changed:
            self._rows = new_rows

        # Emit signals if requested
        if emit_signals:
            if cols_changed:
                self.columnsChanged.emit()
            if rows_changed:
                self.rowsChanged.emit()

        return True

    # ✅ REMOVED: Complex helper methods - using direct operations in main methods

    @Slot(int, int, result=bool)
    def hasGridItemAt(self, row: int, col: int) -> bool:
        """Check if there is a grid item at the specified row/col"""
        try:
            return self._listGridItems.hasItemAt(row, col)
        except Exception as e:
            logger.error(f"Error checking grid item at row/col ({row},{col}): {e}")
            return False

    def maximizeItemAt(self, row: int, col: int):
        """✅ NEW APPROACH: Set fullscreen state on individual item"""
        try:
            if self.hasGridItemAt(row, col):
                item = self._listGridItems.getItemAt(row, col)
                if item and hasattr(item, 'fullscreen'):
                    item.fullscreen = True
                    logger.debug(f"Set fullscreen for item at row/col ({row},{col})")
            else:
                logger.debug(f"Cannot maximize - no item at row/col ({row},{col})")
        except Exception as e:
            logger.error(f"Error setting fullscreen for item at row/col ({row},{col}): {e}")

    def restoreGrid(self):
        """✅ NEW APPROACH: Clear fullscreen state from all items"""
        try:
            for i in range(self._listGridItems.count()):
                item = self._listGridItems.getItem(i)
                if item and hasattr(item, 'fullscreen'):
                    item.fullscreen = False
            logger.debug("Restored grid - cleared all fullscreen states")
        except Exception as e:
            logger.error(f"Error restoring grid: {e}")
        
    def updateGrid(self, emit_signals: bool = True):
        """
        ✅ O(1) PERFORMANCE: Update grid size based on space requirements, not item count
        """
        # ✅ O(1): Use real-time counter instead of counting items
        current_capacity = self._columns * self._rows

        # ✅ FIXED LOGIC: Only expand if current grid is full and we're under limit
        if self._occupied_cells_count >= current_capacity and (self._columns < GridConstants.MAX_COLUMNS or self._rows < GridConstants.MAX_ROWS):
            # ✅ O(1): Calculate next size in progression
            next_size = self._getNextGridSize()
            if next_size:
                new_cols, new_rows = next_size
                if self._columns != new_cols or self._rows != new_rows:
                    old_cols, old_rows = self._columns, self._rows
                    self._columns, self._rows = new_cols, new_rows
                    if emit_signals:
                        self.columnsChanged.emit()
                        self.rowsChanged.emit()

                        # ✅ PHASE 4: Notify items about grid update
                        self._notifyItemsGridExpansion(new_cols, new_rows)

    def _getNextGridSize(self) -> tuple:
        """
        ✅ O(1) PERFORMANCE: Get next grid size using direct calculation

        Returns:
            tuple: (cols, rows) for next size or None if at maximum
        """
        # ✅ O(1): Direct progression lookup based on current size
        current_size = (self._columns, self._rows)

        # ✅ O(1): Use predefined progression map for instant lookup
        return GridConstants.PROGRESSION_MAP.get(current_size, None)
               
    @Property(QObject, notify=listGridItemsChanged)
    def listGridItems(self):
        return self._listGridItems

    @listGridItems.setter
    def listGridItems(self, value: ListGridItems):
        if self._listGridItems != value:
            self._listGridItems = value
            self.listGridItemsChanged.emit()

    def addGridItem(self, item):
        """Add a grid item to the list - QAbstractListModel auto-binding"""
        self._listGridItems.addItem(item)

    def insertGridItemAt(self, row: int, col: int, item):
        """Insert a grid item at the specified row/col - QAbstractListModel auto-binding"""
        item.row = row
        item.col = col
        return self._listGridItems.addItem(item)

    def removeGridItem(self, item):
        """Remove a grid item from the list with selection cleanup"""
        # Check if item was selected before removal
        was_selected = item and hasattr(item, 'selected') and item.selected

        self._listGridItems.removeCamera(item)

        # Update selection count if item was selected
        if was_selected:
            self.selectionCountChanged.emit()


    def clearGridItems(self):
        """Clear all grid items with selection cleanup"""
        logger.debug(f"🗑️ [DELETE] clearGridItems() called")

        # Check if any items were selected
        had_selected_items = self.hasSelection()
        if had_selected_items:
            logger.debug(f"📍 [DELETE] Had selected items before clearing")

        self._listGridItems = ListGridItems()
        self.listGridItemsChanged.emit()
        logger.debug(f"📡 [DELETE] Emitted listGridItemsChanged after clear")

        # Update selection count if there were selected items
        if had_selected_items:
            logger.debug(f"🔄 [DELETE] Emitting selectionCountChanged due to grid clear")
            self.selectionCountChanged.emit()

        logger.debug(f"✅ [DELETE] clearGridItems completed")

    def get_property(self, key, default=None):
        """Get property from _data dictionary"""
        return self._data.get(key, default)

    def set_property(self, key, value):
        """Set property in _data dictionary"""
        self._data[key] = value

    # @Property(dict, notify=dataChanged)
    # def data(self):
    #     return self._data

    # @data.setter
    # def data(self, value):
    #     if self._data != value:
    #         self._data = value
    #         self.dataChanged.emit()


    @Slot(int, int, result=bool)
    def resizeGrid(self, newColumns, newRows) -> bool:
        """
        ✅ GRID LIMITS: Resize the grid with size validation
        Args:
            newColumns (int): New number of columns
            newRows (int): New number of rows
        Returns:
            bool: True if resize was successful
        """
        # ✅ GRID LIMITS: Validate size constraints
        if not self.isGridSizeValid(newColumns, newRows):
            logger.debug(f"🚫 [GRID_LIMIT] Cannot resize to {newColumns}x{newRows} - exceeds {GridConstants.MAX_COLUMNS}x{GridConstants.MAX_ROWS} limit")
            return False

        # ✅ PERFORMANCE: Use batched update method
        self._batchUpdateGridDimensions(newColumns, newRows, emit_signals=True)

        return True

    def getItemRowCol(self, item):
        """Get the current row/col of an item in the grid"""
        return (item.row, item.col) if item else None

    def isRowColValid(self, row: int, col: int):
        """Check if a row/col is valid in the current grid"""
        return 0 <= row < self._rows and 0 <= col < self._columns

    def getAdjacentRowCols(self, row: int, col: int):
        """Get valid adjacent row/col coordinates for a given position"""
        adjacent = []
        for dr, dc in [(-1, 0), (1, 0), (0, -1), (0, 1)]:  # up, down, left, right
            new_row, new_col = row + dr, col + dc
            if self.isRowColValid(new_row, new_col):
                adjacent.append((new_row, new_col))
        return adjacent

    @Slot(int, int, result='QVariant')
    def getCellDimensions(self, row: int, col: int):
        """
        Get cell dimensions for a grid item at the specified row/col
        Args:
            row (int): Grid row
            col (int): Grid column
        Returns:
            dict: {"width": int, "height": int} - cell dimensions
        """
        try:
            # Get item at row/col
            item = self._listGridItems.getItemAt(row, col)
            if not item:
                return {"width": 1, "height": 1}

            # Return cell dimensions
            return {
                "width": item.cols_cell,
                "height": item.rows_cell
            }

        except Exception as e:
            print(f"❌ Error getting cell dimensions for row/col ({row},{col}): {e}")
            return {"width": 1, "height": 1}



    #REMOVED - No longer needed (single source of truth)
    def updateOccupiedMap(self):
        """DEPRECATED: No longer needed - using single source of truth"""
        # Just trigger UI updates, no data duplication
        self.triggerResizeValidityUpdate()

    #REMOVED - No longer needed (single source of truth)
    def updateOccupiedMapIncremental(self, item, old_positions: list, new_positions: list):
        """DEPRECATED: No longer needed - using single source of truth"""
        # Suppress unused parameter debugs
        _ = item, old_positions, new_positions
        # Just trigger UI updates, no data duplication
        self.triggerResizeValidityUpdate()

    def getItemFromRowCol(self, row: int, col: int):
        """✅ PHASE 1: REDIRECT to single source - O(1) lookup"""
        return self._listGridItems.getItemAt(row, col)

    def getItemFromRowColAnyPosition(self, row: int, col: int):
        """
        ✅ FIX MULTI-CELL: Get item at any occupied position (including non-primary positions)

        Sử dụng method này khi cần detect item tại bất kỳ vị trí occupied nào,
        không chỉ primary position. Cần thiết cho multi-cell swap operations.

        Args:
            row: Hàng
            col: Cột

        Returns:
            GridItem nếu tìm thấy, None nếu không
        """
        return self._listGridItems.getItemAtAnyPosition(row, col)

    def _createOccupiedMapFromItems(self) -> dict:
        """
       Helper method to create occupied map when needed
        Only used for collision detection, not stored permanently
        """
        occupied_map = {}
        for item in self._listGridItems._items.values():
            for pos in item.occupied_positions:
                occupied_map[pos] = item
        return occupied_map



    @Slot(int, int, result=bool)
    def isGridSizeValid(self, cols: int, rows: int) -> bool:
        """
        ✅ GRID LIMITS: Check if grid size is within limits

        Args:
            cols: Number of columns
            rows: Number of rows

        Returns:
            bool: True if size is valid (within 12x12 limit)
        """
        return (1 <= cols <= GridConstants.MAX_COLUMNS and 1 <= rows <= GridConstants.MAX_ROWS)

    @Slot(result=int)
    def getMaxColumns(self) -> int:
        """Get maximum allowed columns"""
        return GridConstants.MAX_COLUMNS

    @Slot(result=int)
    def getMaxRows(self) -> int:
        """Get maximum allowed rows"""
        return GridConstants.MAX_ROWS

    def _validateGridCapacityForItems(self, item_count: int) -> bool:
        """
        ✅ MULTI-CELL FIX: Validate if adding items would exceed grid capacity

        Args:
            item_count: Number of 1x1 items to add

        Returns:
            bool: True if items can be added without exceeding grid limits
        """
        available_cells = self._getAvailableGridSlots()
        # For 1x1 items, item_count = cells_needed
        return item_count <= available_cells

    def _getAvailableGridSlots(self) -> int:
        """
        ✅ MULTI-CELL FIX: Calculate available cells (not slots) using real-time counter

        Returns:
            int: Number of available cells for new items (1x1 items only)
        """
        # ✅ O(1): Use real-time counter for actual occupied cells
        available_cells = GridConstants.MAX_CAPACITY - self._occupied_cells_count
        return max(0, available_cells)

    @Slot(int, result=bool)
    def canAddItems(self, item_count: int) -> bool:
        """
        ✅ O(1) PERFORMANCE: QML-accessible early validation for UI responsiveness

        Args:
            item_count: Number of items to add

        Returns:
            bool: True if items can be added without exceeding limits
        """
        return self._validateGridCapacityForItems(item_count)

    @Slot(int, result=int)
    def getMaxAddableItems(self, requested_count: int) -> int:
        """
        ✅ O(1) PERFORMANCE: Get maximum number of items that can be added

        Args:
            requested_count: Number of items requested to add

        Returns:
            int: Maximum number that can actually be added (0 if grid full)
        """
        available_cells = self._getAvailableGridSlots()
        return min(requested_count, available_cells)

    @Slot(result=bool)
    def isGridFull(self) -> bool:
        """
        ✅ O(1) PERFORMANCE: Quick grid full check for UI

        Returns:
            bool: True if grid is completely full
        """
        return self._getAvailableGridSlots() <= 0

    @Slot(result=int)
    def getMaxGridCapacity(self) -> int:
        """
        ✅ GRID LIMITS: Get maximum grid capacity

        Returns:
            int: Maximum number of items that can fit in grid
        """
        return GridConstants.MAX_CAPACITY

    @Slot(result=int)
    def getCurrentGridCapacity(self) -> int:
        """
        ✅ GRID LIMITS: Get current grid capacity

        Returns:
            int: Current number of items that can fit in grid
        """
        return self._columns * self._rows

    @Slot(result=int)
    def getRemainingCapacity(self) -> int:
        """
        ✅ O(1) PERFORMANCE: Get remaining capacity using real-time counter

        Returns:
            int: Number of additional items that can be added
        """
        # ✅ O(1): Use real-time counter instead of counting items
        current_capacity = self._columns * self._rows
        return max(0, current_capacity - self._occupied_cells_count)

    @Slot(result=int)
    def getAvailableSlots(self) -> int:
        """
        ✅ MULTI-CELL FIX: Get available cells (for 1x1 items) up to 12x12 maximum

        Returns:
            int: Number of 1x1 items that can still be added to grid
        """
        return self._getAvailableGridSlots()

    @Slot(result=int)
    def getOccupiedCellsCount(self) -> int:
        """
        ✅ MULTI-CELL: Get actual number of occupied cells (not items)

        Returns:
            int: Total cells occupied by all items (including multi-cell)
        """
        return self._occupied_cells_count

    @Slot(result=int)
    def getItemCount(self) -> int:
        """
        ✅ MULTI-CELL: Get actual number of items (not cells)

        Returns:
            int: Total number of items in grid
        """
        return self._listGridItems.itemCount

    def _incrementOccupiedCells(self, count: int = 1):
        """
        ✅ O(1) PERFORMANCE: Increment occupied cells counter

        Args:
            count: Number of cells to add (default: 1)
        """
        self._occupied_cells_count += count

    def _decrementOccupiedCells(self, count: int = 1):
        """
        ✅ O(1) PERFORMANCE: Decrement occupied cells counter

        Args:
            count: Number of cells to remove (default: 1)
        """
        self._occupied_cells_count = max(0, self._occupied_cells_count - count)

    # ✅ NEW: Row/Col-based QML methods
    @Slot(int, int, result='QVariant')
    def getItemAtRowCol(self, row: int, col: int):
        """QML-accessible method to get item from row/col coordinates"""
        return self.getItemFromRowCol(row, col)

    @Slot(int, int, result='QVariant')
    def getItemAtRowColAnyPosition(self, row: int, col: int):
        """
        ✅ FIX MULTI-CELL: QML-accessible method to get item at any occupied position

        Sử dụng method này trong drag operations để detect multi-cell items
        ngay cả khi drag vào non-primary positions.
        """
        return self.getItemFromRowColAnyPosition(row, col)

    @Slot(int, int, result=bool)
    def hasItemAtRowCol(self, row: int, col: int):
        """QML-accessible method to check if row/col has item"""
        return self._listGridItems.hasItemAt(row, col)

    @Slot(int, int, int, int, result=bool)
    def swapItemsRowCol(self, row1: int, col1: int, row2: int, col2: int):
        """QML-accessible method to swap items using row/col coordinates"""
        success = self._listGridItems.swapItemsRowCol(row1, col1, row2, col2)
        if success:
            self.updateOccupiedMap()
        return success

    @Slot(int, int, int, int, result=bool)
    def canResizeItemAt(self, row: int, col: int, new_cols: int, new_rows: int) -> bool:
        """
        ✅ GRID LIMITS: QML-accessible method để check resize validity với collision detection và grid limits

        Args:
            row: Item row position
            col: Item column position
            new_cols: Target number of columns
            new_rows: Target number of rows

        Returns:
            bool: True if resize is valid (no collision, within bounds, within grid limits)
        """
        item = self.getItemFromRowCol(row, col)
        if not item:
            return False

        # ✅ GRID LIMITS: Check if resize would exceed grid boundaries
        item_end_row = row + new_rows - 1
        item_end_col = col + new_cols - 1

        if item_end_row >= GridConstants.MAX_ROWS or item_end_col >= GridConstants.MAX_COLUMNS:
            logger.debug(f"🚫 [GRID_LIMIT] Resize would exceed {GridConstants.MAX_COLUMNS}x{GridConstants.MAX_ROWS} limit: item at ({row},{col}) with size {new_cols}x{new_rows}")
            return False

        #Use helper method instead of stored _occupied_map
        occupied_map = self._createOccupiedMapFromItems()
        can_resize, _ = item.canResizeToWithCollision(
            new_rows, new_cols, self._rows, self._columns, occupied_map
        )
        return can_resize

    @Slot(int, int, int, int, result='QVariantList')
    def getResizeConflicts(self, row: int, col: int, new_cols: int, new_rows: int):
        """
        Trả về list các items bị conflict khi resize

        Args:
            row: Item row position
            col: Item column position
            new_cols: Target number of columns
            new_rows: Target number of rows

        Returns:
            QVariantList: List of conflicting items info
        """
        item = self.getItemFromRowCol(row, col)
        if not item:
            return []

        #Use helper method instead of stored _occupied_map
        occupied_map = self._createOccupiedMapFromItems()
        _, conflicts = item.canResizeToWithCollision(
            new_rows, new_cols, self._rows, self._columns, occupied_map
        )

        # Convert to QML-friendly format
        conflict_list = []
        for conflict_item in conflicts:
            conflict_list.append({
                "row": conflict_item.row,
                "col": conflict_item.col,
                "rows_cell": conflict_item.rows_cell,
                "cols_cell": conflict_item.cols_cell,
                "itemType": conflict_item.itemType
            })
        return conflict_list

    @Slot(int, int, int, int, result=bool)
    def resizeItemSafe(self, row: int, col: int, new_cols: int, new_rows: int) -> bool:
        """
        Safe resize với incremental occupied map update

        Args:
            row: Item row position
            col: Item column position
            new_cols: Target number of columns
            new_rows: Target number of rows

        Returns:
            bool: True if resize was successful
        """
        # Kiểm tra validity trước khi resize
        if not self.canResizeItemAt(row, col, new_cols, new_rows):
            logger.debug(f"🚫 [RESIZE] Cannot resize item at ({row},{col}) to {new_cols}x{new_rows} - collision or bounds violation")
            return False

        # Thực hiện resize
        item = self.getItemFromRowCol(row, col)
        if not item:
            return False

        # Store old positions and cell count before update
        old_positions = list(item.occupied_positions)
        old_cells_count = len(old_positions)

        # Update item size properties
        item._cols_cell = new_cols
        item._rows_cell = new_rows
        item._cached_occupied = None

        # Get new positions and cell count after update
        new_positions = list(item.occupied_positions)
        new_cells_count = len(new_positions)

        # ✅ MULTI-CELL FIX: Update occupied cells counter for resize
        cells_difference = new_cells_count - old_cells_count
        if cells_difference > 0:
            self._incrementOccupiedCells(cells_difference)
        elif cells_difference < 0:
            self._decrementOccupiedCells(-cells_difference)

        # Incremental occupied map update
        self.updateOccupiedMapIncremental(item, old_positions, new_positions)

        # Emit signals
        item.cellDimensionsChanged.emit()
        item.occupiedPositionsChanged.emit()

        logger.debug(f"✅ [RESIZE] Successfully resized item at ({row},{col}) to {new_cols}x{new_rows}")
        return True

    @Slot(int, int, int, int)
    def resizeItem(self, row: int, col: int, new_cols: int, new_rows: int):
        """
        Legacy resize method - giữ để backward compatibility
        Resize item at (row, col) and update occupied map atomically
        """
        item = self.getItemFromRowCol(row, col)
        if not item:
            return

        # Update item size properties
        item._cols_cell = new_cols
        item._rows_cell = new_rows
        item._cached_occupied = None

        # Update occupied map and emit signals
        self.updateOccupiedMap()
        item.cellDimensionsChanged.emit()
        item.occupiedPositionsChanged.emit()

    @Slot(int, int, int, int, result=bool)
    def moveItemRowCol(self, from_row: int, from_col: int, to_row: int, to_col: int):
        """
        QML-accessible method to move item using row/col coordinates
        Handles both move and swap operations automatically
        """
        try:
            source_item = self.getItemFromRowCol(from_row, from_col)
            target_item = self.getItemFromRowCol(to_row, to_col)

            if not source_item:
                logger.error(f"No source item at ({from_row},{from_col})")
                return False

            # Check bounds for multi-cell items
            item_cols = getattr(source_item, 'cols_cell', 1)
            item_rows = getattr(source_item, 'rows_cell', 1)
            end_col = to_col + item_cols - 1
            end_row = to_row + item_rows - 1

            if (to_row < 0 or to_col < 0 or end_row >= self._rows or end_col >= self._columns):
                logger.error(f"Target position ({to_row},{to_col}) with size {item_cols}x{item_rows} exceeds grid bounds")
                return False

            # Determine operation type and execute
            if target_item and target_item is source_item:
                success = self._listGridItems.moveItemTo(from_row, from_col, to_row, to_col)
            elif target_item and target_item is not source_item:
                success = self._listGridItems.swapItemsRowCol(from_row, from_col, to_row, to_col)
            else:
                success = self._listGridItems.moveItemTo(from_row, from_col, to_row, to_col)

            if success:
                self.updateOccupiedMap()

            return success

        except Exception as e:
            logger.error(f"❌ [MOVE_ROWCOL] Error: {e}")
            return False


    def clearAllSelection(self):
        """Clear all selection"""
        self._listGridItems.clearAllSelection()

    # ✅ IMPROVED: Signal for resize validity updates
    resizeValidityUpdateRequested = Signal()

    def triggerResizeValidityUpdate(self):
        """Trigger resize validity update for all items"""
        self.resizeValidityUpdateRequested.emit()

    def saveCurrentData(self):
        listGridData = {}
        currentGrid = {
            "row": self.rows,
            "col": self.columns,
            "timestamp": int(time.time()),
            "name": f"View_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        }
        for index, item in self._listGridItems._items.items():
            logger.debug(f'saveCurrentData = {index,item}')
            if isinstance(item,CameraGridItem):
                listGridData[index] = {
                    "type": CommonEnum.ItemType.CAMERA,
                    "position": index,
                    "id": item.cameraModel.id,
                    "row": item.row,
                    "col": item.col,
                    "rows_cell": item.rows_cell,
                    "cols_cell": item.cols_cell,   
                    "width": item.width,
                    "height": item.height
                }
            elif isinstance(item,EventGridItem):
                listGridData[index] = {
                    "type": CommonEnum.ItemType.EVENT,
                    "position": index,
                    "id": item.eventModel.id,
                    "row": item.row,
                    "col": item.col,
                    "rows_cell": item.rows_cell,
                    "cols_cell": item.cols_cell,   
                    "width": item.width,
                    "height": item.height
                }
            elif isinstance(item,DigitalMapGridItem):
                listGridData[index] = {
                    "type": CommonEnum.ItemType.DIGITAL_MAP,
                    "position": index,
                    "id": item.mapModel.id,
                    "row": item.row,
                    "col": item.col,
                    "rows_cell": item.rows_cell,
                    "cols_cell": item.cols_cell,   
                    "width": item.width,
                    "height": item.height
                }
            elif isinstance(item,FloorMapGridItem):
                listGridData[index] = {
                    "type": CommonEnum.ItemType.FLOOR_MAP,
                    "position": index,
                    "id": item.floorModel.id,
                    "row": item.row,
                    "col": item.col,
                    "rows_cell": item.rows_cell,
                    "cols_cell": item.cols_cell,   
                    "width": item.width,
                    "height": item.height
                }
        self._currentData["currentGrid"] = str(currentGrid)
        self._currentData["listGridData"] = str(listGridData)
        logger.debug(f'saveCurrentData = {self._currentData}')

    def shortcut_activated(self):
        self.saveCurrentData()
        # logger.debug(f'shortcut_activated = {self.isSave}')
        if self.isSave:
            logger.info(f'View already saved, skipping save operation')
            return
        try:
            def callback(data):
                if data is not None:
                    self._data = self._currentData
                    self.isSave = True
                else:
                    from src.common.widget.notifications.notify import Notifications
                    Notifications(parent=main_controller.list_parent['CameraScreen'], title=self.tr('Failed to save the grid'), icon=Style.PrimaryImage.fail_result)
            logger.info(f'self._currentData = {self._currentData}')
            if self.controller is None:
                self.controller = self.get_controller()
            subThread = SubThread(parent=self, target=self.controller.update_tabmodel_by_put, args=(self._currentData,),callback=callback)
            subThread.start()
        except Exception as e:
            logger.info(f'shortcut_activated error: {e}')

    def get_controller(self):
        from src.common.controller.controller_manager import controller_manager
        return controller_manager.get_controller(server_ip=self.get_property("server_ip"))
    
    @Slot(str, result=str)
    def get_image_theme_by_key(self, key = None):
        if key is None:
            return None
        return main_controller.get_theme_attribute("Image", key)

    @Slot(str, result=str)
    def get_color_theme_by_key(self, key = None):
        if key is None:
            return None
        return main_controller.get_theme_attribute("Color", key)

    # ✅ PTZ FUNCTIONALITY: Helper methods for cleaner code
    def _getCameraAndController(self, position):
        """Get camera model and controller for a grid position"""
        try:
            # Convert position to row/col coordinates
            if self._columns <= 0:
                logger.debug(f"🎮 [PTZ] Invalid grid columns: {self._columns}")
                return None, None, None

            row = position // self._columns
            col = position % self._columns

            # Get the grid item at the calculated row/col
            grid_item = self._listGridItems.getItemAt(row, col)
            if not grid_item:
                # logger.debug(f"🎮 [PTZ] No grid item found at position {position} (row={row}, col={col})")
                return None, None, None

            # Get camera model from grid item
            camera_model = grid_item.cameraModel
            if not camera_model:
                # logger.debug(f"🎮 [PTZ] No camera model found for position {position}")
                return None, None, None

            camera_id = camera_model.get_property("id")
            if not camera_id:
                # logger.debug(f"🎮 [PTZ] No camera ID found for position {position}")
                return None, None, None

            # Get the controller for this camera
            server_ip = camera_model.get_property("server_ip")
            from src.common.controller.controller_manager import controller_manager
            controller = controller_manager.get_controller(server_ip=server_ip)
            if not controller:
                # logger.debug(f"🎮 [PTZ] Controller not found for camera: {camera_id}")
                return None, None, None

            # Check if camera supports PTZ
            if not camera_model.get_property("ptzCap") or len(camera_model.get_property("ptzCap", [])) == 0:
                # logger.debug(f"🎮 [PTZ] Camera {camera_id} does not support PTZ")
                return None, None, None

            return camera_model, controller, camera_id

        except Exception as e:
            logger.error(f"🎮 [PTZ] Error getting camera and controller: {e}")
            return None, None, None

    def _sendAvigilonPTZ(self, controller, camera_id, pan=0, tilt=0, zoom=0, action="START"):
        """Send PTZ command to Avigilon camera"""
        try:
            data = {
                "cameraId": camera_id,
                "endPoint": "/camera/commands/pan-tilt-zoom",
                "requestData": {
                    "continuous": {
                        "panAmount": pan,
                        "tiltAmount": -tilt,  # Invert Y for Avigilon
                        "zoomAmount": zoom,
                        "action": action
                    }
                }
            }
            controller.forward_avigilon_ptz(cameraId=data["cameraId"], requestData=data["requestData"])
            # logger.debug(f"🎮 [AVIGILON_PTZ] Sent command: {data}")
            return True
        except Exception as e:
            # logger.error(f"🎮 [AVIGILON_PTZ] Error: {e}")
            return False

    def _sendOnvifPTZ(self, controller, camera_id, x=0, y=0, zoom=0):
        """Send PTZ command to ONVIF camera"""
        try:
            # Clamp x, y về [-1, 1] để đảm bảo server/camera nhận lệnh hợp lệ
            x = max(-1.0, min(1.0, x))
            y = max(-1.0, min(1.0, y))
            controller.ptz_continuous_move(
                parent=self,
                cameraId=camera_id,
                x=x,
                y=y,
                zoom=zoom
            )
            logger.debug(f"🎮 [ONVIF_PTZ] Sent command: x={x}, y={y}, zoom={zoom}")
            return True
        except Exception as e:
            logger.error(f"🎮 [ONVIF_PTZ] Error: {e}")
            return False

    def _sendOnvifPTZStop(self, controller, camera_id):
        """Send PTZ stop command to ONVIF camera"""
        try:
            controller.ptz_stop(cameraId=camera_id)
            logger.debug(f"🎮 [ONVIF_PTZ_STOP] Sent stop command for camera {camera_id}")
            return True
        except Exception as e:
            logger.error(f"🎮 [ONVIF_PTZ_STOP] Error: {e}")
            return False

    @Slot(int, int, int, int, int)
    def controlPTZ(self, position, _arg1=0, _arg2=0, x=0, y=0):
        """Control PTZ movement for direction-based commands (PTZ panel buttons)"""
        try:
            logger.debug(f"🎮 [PTZ_DIRECTION] PTZ direction control for position {position}")

            camera_model, controller, camera_id = self._getCameraAndController(position)
            if not camera_model or not controller or not camera_id:
                return

            # Get the stored PTZ speed and apply it to the movement values
            ptz_speed = self._getPTZSpeed(camera_model)
            logger.debug(f"🎮 [PTZ_DIRECTION] Using PTZ speed: {ptz_speed}")

            # Apply speed multiplier to movement values
            scaled_x = x * ptz_speed if x != 0 else 0
            scaled_y = y * ptz_speed if y != 0 else 0

            # Direction-based PTZ control (from PTZ panel buttons)
            logger.debug(f"🎮 [PTZ_DIRECTION] Direction PTZ: x={scaled_x}, y={scaled_y} (original: x={x}, y={y})")

            # Send command based on camera type
            if camera_model.get_property("type") == "AVIGILON":
                self._sendAvigilonPTZ(controller, camera_id, pan=scaled_x, tilt=scaled_y, zoom=0, action="START")
            else:
                self._sendOnvifPTZ(controller, camera_id, x=scaled_x, y=scaled_y, zoom=0)

        except Exception as e:
            logger.error(f"🎮 [PTZ_DIRECTION] Error: {e}")

    @Slot(int, 'QPoint', 'QPoint', float, float)
    def controlPTZ3D(self, position, startPoint, endPoint, video_width, video_height):
        """Control PTZ movement for 3D mouse-based commands"""
        try:
            logger.debug(f"🎮 [PTZ_3D] PTZ 3D control for position {position}")

            camera_model, controller, camera_id = self._getCameraAndController(position)
            if not camera_model or not controller or not camera_id:
                return

            # Calculate movement vector from center to mouse position
            center_x = startPoint.x()
            center_y = startPoint.y()
            mouse_x = endPoint.x()
            mouse_y = endPoint.y()

            logger.debug(f"🎮 [PTZ_3D] Center: ({center_x}, {center_y}), Mouse: ({mouse_x}, {mouse_y})")

            dx = mouse_x - center_x
            dy = mouse_y - center_y

            # Normalize to video area size (dùng width/height thực tế)
            # Nếu width/height không hợp lệ, fallback về 400x300
            if not video_width or video_width <= 0:
                video_width = 400.0
            if not video_height or video_height <= 0:
                video_height = 300.0
            print("helloasd", video_width, video_height)
            normalized_x = dx / (video_width / 2.0) if video_width > 0 else 0
            normalized_y = dy / (video_height / 2.0) if video_height > 0 else 0

            # ✅ FIX PTZ 3D DIRECTION: Invert Y coordinate for correct up/down movement
            normalized_y = -normalized_y

            # Clamp to [-1, 1] range
            normalized_x = max(-1.0, min(1.0, normalized_x))
            normalized_y = max(-1.0, min(1.0, normalized_y))

            speed = 1.5  # Reduced speed for smoother control
            x = normalized_x * speed
            y = normalized_y * speed

            logger.debug(f"🎮 [PTZ_3D] Normalized movement: x={x}, y={y}")

            if camera_model.get_property("type") == "AVIGILON":
                self._sendAvigilonPTZ(controller, camera_id, pan=x, tilt=y, zoom=0, action="START")
            else:
                self._sendOnvifPTZ(controller, camera_id, x=x, y=y, zoom=0)

        except Exception as e:
            logger.error(f"🎮 [PTZ_3D] Error: {e}")

    @Slot(int)
    def stopPTZ(self, position):
        """Stop PTZ movement for the camera at the specified position"""
        try:
            logger.debug(f"🎮 [PTZ_STOP] PTZ stop called for position {position}")

            camera_model, controller, camera_id = self._getCameraAndController(position)
            if not camera_model or not controller or not camera_id:
                return

            # Send stop command based on camera type
            if camera_model.get_property("type") == "AVIGILON":
                self._sendAvigilonPTZ(controller, camera_id, pan=0, tilt=0, zoom=0, action="STOP")
            else:
                self._sendOnvifPTZStop(controller, camera_id)

        except Exception as e:
            logger.error(f"🎮 [PTZ_STOP] Error stopping PTZ: {e}")

    @Slot(int, float)
    def controlPTZZoom(self, position, factor):
        """Control PTZ zoom for the camera at the specified position"""
        try:
            logger.debug(f"🎮 [PTZ_ZOOM] PTZ zoom called for position {position}, factor {factor}")

            camera_model, controller, camera_id = self._getCameraAndController(position)
            if not camera_model or not controller or not camera_id:
                return

            # Get the stored PTZ speed and apply it to the zoom factor
            ptz_speed = self._getPTZSpeed(camera_model)
            logger.debug(f"🎮 [PTZ_ZOOM] Using PTZ speed: {ptz_speed}")

            # Apply speed multiplier to zoom factor
            scaled_factor = factor * ptz_speed

            logger.debug(f"🎮 [PTZ_ZOOM] Scaled zoom factor: {scaled_factor} (original: {factor})")

            # Send zoom command based on camera type
            if camera_model.get_property("type") == "AVIGILON":
                self._sendAvigilonPTZ(controller, camera_id, pan=0, tilt=0, zoom=scaled_factor, action="START")
            else:
                self._sendOnvifPTZ(controller, camera_id, x=0, y=0, zoom=scaled_factor)

        except Exception as e:
            logger.error(f"🎮 [PTZ_ZOOM] Error controlling PTZ zoom: {e}")

    @Slot(int, float, float, float, float, int, int, float, float, float)
    def setDragToZoom(self, position, start_x, start_y, end_x, end_y, width, height, center_x_norm, center_y_norm, zoom_factor):
        """Set drag-to-zoom for the camera at the specified position by delegating to backend controller logic."""
        try:
            camera_model, controller, camera_id = self._getCameraAndController(position)
            if not camera_model or not controller or not camera_id:
                logger.error("🎮 [DRAG_ZOOM] Invalid camera or controller")
                return

            if not camera_model.ptz3DSupport:
                logger.warning("🎮 [DRAG_ZOOM] Camera does not support 3D locate")
                return

            # Prepare QPoint objects for backend
            from PySide6.QtCore import QPoint
            start_point = QPoint(int(start_x), int(start_y))
            end_point = QPoint(int(end_x), int(end_y))
            calib_data = getattr(camera_model, 'calib_data', None)
            print("calib_data", calib_data)
            # Call backend controller logic for drag-to-zoom
            controller.set_drag_to_zoom(
                parent=self,
                cameraId=camera_id,
                start_point=start_point,
                end_point=end_point,
                width=width,
                height=height,
                calib_data=calib_data
            )

        except Exception as e:
            logger.error(f"🎮 [DRAG_ZOOM] Error (backend call): {e}")

    @Slot(int, float)
    def setPTZSpeed(self, position, speed):
        """Set PTZ speed for the camera at the specified position"""
        try:
            logger.debug(f"🎮 [PTZ_SPEED] Setting PTZ speed for position {position}: {speed}")

            camera_model, controller, camera_id = self._getCameraAndController(position)
            if not camera_model or not controller or not camera_id:
                return

            # Store speed in camera model for future PTZ operations
            camera_model.set_property("ptz_speed", speed)
            logger.debug(f"🎮 [PTZ_SPEED] Set PTZ speed {speed} for camera {camera_id}")

        except Exception as e:
            logger.error(f"🎮 [PTZ_SPEED] Error setting PTZ speed: {e}")

    def _getPTZSpeed(self, camera_model):
        """Get PTZ speed from camera model, default to 0.5"""
        try:
            speed = camera_model.get_property("ptz_speed")
            if speed is None:
                speed = 1# Default speed
            return float(speed)
        except:
            return 1  # Default speed

    # ✅ PTZ STATE MANAGEMENT: Global PTZ state control
    @Slot(int, str)
    def activatePtzForCamera(self, position, buttonType):
        logger.info(f'activatePtzForCamera')
        """
        Activate PTZ for a specific camera and deactivate for all others

        Args:
            position: Position of the camera to activate PTZ for (-1 to deactivate all)
            buttonType: Type of PTZ button ("ptz", "ptz3d", "dragZoom", "none")
        """
        try:
            # logger.debug(f"🎮 [PTZ_GLOBAL] Activating PTZ '{buttonType}' for position {position}")

            # Special case: position = -1 means deactivate all PTZ
            if position == -1:
                self.deactivateAllPtz()
                return

            # First, deactivate PTZ for all other cameras
            if buttonType != "none":
                self._deactivateAllOtherPtz(position)

            # Get the target camera and activate its PTZ
            camera_model, controller, camera_id = self._getCameraAndController(position)
            if camera_model:
                # Get the grid item for this position
                if self._columns <= 0:
                    return

                row = position // self._columns
                col = position % self._columns
                grid_item = self._listGridItems.getItemAt(row, col)

                if grid_item and hasattr(grid_item, 'activatePtzButton'):
                    grid_item.activatePtzButton(buttonType)
                    # logger.debug(f"🎮 [PTZ_GLOBAL] Activated PTZ '{buttonType}' for camera {camera_id}")

        except Exception as e:
            logger.error(f"🎮 [PTZ_GLOBAL] Error activating PTZ: {e}")

    def process_queue(self):
        logger.debug(f'init process_queue')
        while True:
            msg = self.coordinate_queue.get()
            if msg is None:
                self.controller.ptz_stop(cameraId=self.camera_model.get_property("id", None))
                break
            
            self.current_time = time.time()
            if (self.current_time - self.previous_time) > 0.2:
                logger.debug(f'process_queue = {msg} -- {self.current_time - self.previous_time}s')
                self.previous_time = time.time()
                self.controller.ptz_continuous_move(parent=self,cameraId=self.camera_model.get_property("id", None),x = msg['pan'],y = msg['tilt'],zoom=msg['zoom'])
            self.coordinate_queue.task_done()

    def on_process_queue_complete(self, result=None):
        """Callback for process_queue_thread completion"""
        if result is not None:
            logger.debug(f"Process queue completed with result: {result}")
        else:
            logger.debug("Process queue completed")

    def process_joystick_queue(self):
        while True:
            current_time = time.time()
            try:
                msg = self.current_joystick_msg
                if msg != self.previous_joystick_msg:
                    # logger.debug(f'run_joystick_thread = {self.previous_joystick_msg}')
                    self.previous_joystick_msg = msg.copy()
                    x = 0
                    y = 0
                    speed_zoom = 0
                    if msg['type'] == 'button_zoom':
                        speed_zoom = msg['speed']
                        if speed_zoom == 0:
                            self.controller.ptz_stop(cameraId=self.camera_model.get_property("id", None))
                            logger.debug(f'stop run_joystick_thread')
                            time.sleep(0.5)
                            return
                        else:
                            if (current_time - self.previous_time) > 0.5:
                                self.controller.ptz_continuous_move(parent=self,cameraId=self.camera_model.get_property("id", None),x = x,y = y,zoom=speed_zoom)
                                self.previous_time = time.time()
                            # sleep(0.02)
                    elif msg['type'] == 'button_focus':
                        pass
                        # speed_focus = msg['speed']
                        # # logger.debug(f'msg = {msg}')
                        # if speed_focus == 0:
                        #     self.stop_focus(profileToken=msg['profileToken'])
                        #     return
                        # else:
                        #     if (current_time - self.previous_time) > 0.1:
                        #         logger.debug(f'start move = {msg}')
                        #         self.move_focus_continuous(profileToken=msg['profileToken'],speed=speed_focus,position=0)
                        #         self.previous_time = time.time()
                    elif msg['type'] == 'axis':
                        x = msg['x']
                        y = msg['y']
                        if x == 0 and y == 0:
                            self.controller.ptz_stop(cameraId=self.camera_model.get_property("id", None))
                            logger.debug(f'stop run_joystick_thread')
                            return
                        else:
                            if (current_time - self.previous_time) > 0.1:
                                logger.debug(f'start move = {msg}')
                                self.controller.ptz_continuous_move(parent=self,cameraId=self.camera_model.get_property("id", None),x = x,y = y,zoom=speed_zoom)
                                self.previous_time = time.time()
                else:
                    time.sleep(0.001)
            except Exception as e:
                logger.error(f'process_joystick_queue fail {e}')

    def on_process_joystick_queue_complete(self, result=None):
        """Callback for process_joystick_queue_thread completion"""
        if result is not None:
            logger.debug(f"Process joystick queue completed with result: {result}")
        else:
            logger.debug("Process joystick queue completed")

    def _deactivateAllOtherPtz(self, excludePosition):
        """
        Deactivate PTZ for all cameras except the specified position

        Args:
            excludePosition: Position to exclude from deactivation
        """
        try:
            logger.debug(f"🎮 [PTZ_GLOBAL] Deactivating PTZ for all cameras except position {excludePosition}")

            # Iterate through all grid items
            for row in range(self._rows):
                for col in range(self._columns):
                    current_position = row * self._columns + col

                    # Skip the excluded position
                    if current_position == excludePosition:
                        continue

                    grid_item = self._listGridItems.getItemAt(row, col)
                    if grid_item and hasattr(grid_item, 'deactivateAllPtz'):
                        grid_item.deactivateAllPtz()

        except Exception as e:
            logger.error(f"🎮 [PTZ_GLOBAL] Error deactivating other PTZ: {e}")

    @Slot()
    def deactivateAllPtz(self):
        """
        Deactivate PTZ for all cameras (called when clicking outside grid)
        """
        try:
            logger.debug(f"🎮 [PTZ_GLOBAL] Deactivating PTZ for all cameras")

            # Iterate through all grid items
            for row in range(self._rows):
                for col in range(self._columns):
                    grid_item = self._listGridItems.getItemAt(row, col)
                    if grid_item and hasattr(grid_item, 'deactivateAllPtz'):
                        grid_item.deactivateAllPtz()

        except Exception as e:
            logger.error(f"🎮 [PTZ_GLOBAL] Error deactivating all PTZ: {e}")

    @Slot(result=int)
    def getActivePtzPosition(self):
        """
        Get the position of the camera with active PTZ, or -1 if none
        """
        try:
            for row in range(self._rows):
                for col in range(self._columns):
                    grid_item = self._listGridItems.getItemAt(row, col)
                    if grid_item and hasattr(grid_item, 'hasActivePtz') and grid_item.hasActivePtz():
                        return row * self._columns + col
            return -1
        except Exception as e:
            logger.error(f"🎮 [PTZ_GLOBAL] Error getting active PTZ position: {e}")
            return -1



    @Slot()
    def openSelectedItemsInNewTab(self):
        """Create a new tab and add all selected camera items to it"""
        try:
            # Get all selected items with camera models
            selected_items = self.getSelectedItems()

            if not selected_items or len(selected_items) == 0:
                logger.warning("No selected items with cameras found")
                return

            # Extract camera models from selected items
            camera_models = []
            for item in selected_items:
                if hasattr(item, 'cameraModel') and item.cameraModel:
                    camera_models.append(item.cameraModel)

            if len(camera_models) == 0:
                logger.warning("No valid camera models found in selected items")
                return

            # Create new tab model (Invalid type = regular tab)
            from src.presentation.camera_screen.managers.grid_manager import gridManager
            import uuid
            import math

            # Generate unique name for new tab
            camera_screen = main_controller.list_parent['CameraScreen']
            tab_name = camera_screen.find_name_selected(tab_type=CommonEnum.TabType.NORMAL)

            # Calculate optimal grid size for the number of cameras
            num_cameras = len(camera_models)
            optimal_size = math.ceil(math.sqrt(num_cameras))
            optimal_size = max(1, optimal_size)

            # Create new tab model data
            data = {
                "id": str(uuid.uuid4()),
                "name": tab_name,
                "type": CommonEnum.TabType.NORMAL,  # Regular tab
                "isShow": True,
                "currentGrid": "{}",
                "listGridData": "{}",
                "listGridCustomData": "NewGrid",
                "direction": str({'id': str(uuid.uuid4())})
            }

            # Create GridModel and add to manager
            new_grid_model = GridModel(data=data)
            gridManager.addGridModel(new_grid_model)

            # Set optimal grid size for the new grid
            new_grid_model.set_property('columns', optimal_size)
            new_grid_model.set_property('rows', optimal_size)

            # Add tab widget to UI first
            camera_screen.add_tab_widget(gridModel=new_grid_model)

            # Add cameras to the new grid starting from position 0
            for i, camera_model in enumerate(camera_models):
                if i >= optimal_size * optimal_size:
                    logger.warning(f"Too many cameras for grid size {optimal_size}x{optimal_size}, skipping camera {i}")
                    break

                camera_id = camera_model.get_property('id')

                # Ensure camera_id is a string (hashable)
                if isinstance(camera_id, dict):
                    if 'id' in camera_id:
                        camera_id = str(camera_id['id'])
                    else:
                        camera_id = str(camera_id)
                elif camera_id is None:
                    logger.warning(f"Camera model at index {i} has no ID")
                    continue
                else:
                    camera_id = str(camera_id)

                # Add camera to the new grid
                new_grid_model.addCamera(camera_id)

            # Execute camera addition after a short delay

            logger.debug(f"Created new tab '{tab_name}' with {len(camera_models)} selected cameras in {optimal_size}x{optimal_size} grid")

        except Exception as e:
            logger.error(f"Error opening selected items in new tab: {e}")
            logger.error(f"Exception details: {traceback.format_exc()}")

    @Slot(int)
    def showCameraInfo(self, position):
        """Show camera information dialog for the camera at the specified position

        Args:
            position: The position of the camera in the grid
        """
        try:
            # Convert position to row/col coordinates using the same logic as _getCameraAndController
            if self._columns <= 0:
                logger.debug(f"Invalid grid columns: {self._columns}")
                return

            row = position // self._columns
            col = position % self._columns

            # Get the grid item at the calculated row/col
            grid_item = self._listGridItems.getItemAt(row, col)
            if not grid_item:
                logger.debug(f"No grid item found at position {position} (row={row}, col={col})")
                return

            # Check if it's a camera grid item and get the camera model
            if not hasattr(grid_item, 'cameraModel') or not grid_item.cameraModel:
                logger.debug(f"No camera model found for grid item at position {position}")
                return

            camera_model = grid_item.cameraModel

            # Show the camera info dialog
            from src.common.widget.dialogs.camera_info_dialog import CameraInfoDialog
            from src.common.controller.main_controller import main_controller

            # Get the parent widget (usually CameraScreen)
            parent = main_controller.list_parent.get('CameraScreen')
            if not parent:
                logger.debug("Parent widget not found")
                parent = None

            # Create and show the dialog
            camera_info = CameraInfoDialog(parent=parent, data=camera_model)
            camera_info.exec()

        except Exception as e:
            logger.error(f"Error showing camera info: {e}")
            logger.error(traceback.format_exc())

    def _on_theme_changed(self):
        self.isDarkTheme = main_controller.is_dark_theme()

    @Slot(str)
    def saveSelectedItemsSavedView(self, view_name: str):
        """
        Saves the currently selected items and grid layout as a new saved view.
        """
        try:
            if not view_name:
                logger.warning("View name cannot be empty.")
                return

            selected_items = self.getSelectedItems()
            listGridData = {}
            currentGrid = {
                "row": self.rows,
                "col": self.columns,
                "timestamp": 0,
                "name": f"ahihi"
            }
            for item in selected_items:
                if isinstance(item,CameraGridItem):
                    if self.controller is None:
                        from src.common.controller.controller_manager import controller_manager
                        self.controller = controller_manager.get_controller(server_ip=item.cameraModel.get_property("server_ip"))
                    logger.info(f'selected_items1 = {item}')
                    listGridData[(item.row,item.col)] = {
                        "type": CommonEnum.ItemType.CAMERA,
                        "position": (item.row,item.col),
                        "id": item.cameraModel.id,
                        "row": item.row,
                        "col": item.col,
                        'rows_cell': item.rows_cell,
                        'cols_cell': item.cols_cell,
                        "width": item.width,
                        "height": item.height
                    }
                elif isinstance(item,DigitalMapGridItem):
                    if self.controller is None:
                        from src.common.controller.controller_manager import controller_manager
                        self.controller = controller_manager.get_controller(server_ip=item.mapModel.get_property("server_ip"))
                    listGridData[(item.row,item.col)] = {
                        "type": CommonEnum.ItemType.DIGITAL_MAP,
                        "position": (item.row,item.col),
                        "id": item.mapModel.id,
                        "row": item.row,
                        "col": item.col,
                        'rows_cell': item.rows_cell,
                        'cols_cell': item.cols_cell,
                        "width": item.width,
                        "height": item.height
                    }
                elif isinstance(item,FloorMapGridItem):
                    if self.controller is None:
                        from src.common.controller.controller_manager import controller_manager
                        self.controller = controller_manager.get_controller(server_ip=item.floorModel.get_property("server_ip"))
                    listGridData[(item.row,item.col)] = {
                        "type": CommonEnum.ItemType.FLOOR_MAP,
                        "position": (item.row,item.col),
                        "id": item.floorModel.id,
                        "row": item.row,
                        "col": item.col,
                        'rows_cell': item.rows_cell,
                        'cols_cell': item.cols_cell,
                        "width": item.width,
                        "height": item.height
                    }
                elif isinstance(item,EventGridItem):
                    if self.controller is None:
                        from src.common.controller.controller_manager import controller_manager
                        self.controller = controller_manager.get_controller(server_ip=item.eventModel.server_ip)
                    listGridData[(item.row,item.col)] = {
                        "type": CommonEnum.ItemType.EVENT,
                        "position": (item.row,item.col),
                        "id": item.eventModel.id,
                        "row": item.row,
                        "col": item.col,
                        'rows_cell': item.rows_cell,
                        'cols_cell': item.cols_cell,
                        "width": item.width,
                        "height": item.height
                    }
            data = {
                "name": view_name,
                "type": CommonEnum.TabType.SAVEDVIEW,
                "isShow": False,
                "currentGrid": str(currentGrid),
                "listGridData": str(listGridData),
                "listGridCustomData": "NewGrid"
            }
            def callback(data):
                if data is not None:
                    main_tree_view = main_controller.list_parent['MainTreeView']
                    main_tree_view.auto_add_savedview(data,self.controller.server.data.server_ip) 
                else:
                    pass
                    # thêm notify lỗi tạo SavedView
            subThread = SubThread(parent=self, target=self.controller.create_tabmodel, args=(data,),callback=callback)
            subThread.start() 

        except Exception as e:
            logger.error(f"Error in saveSelectedItemsAsNewView: {e}", exc_info=True)


    @Slot(str)
    def saveSelectedItemsVirtualWindow(self, view_name: str):
        """
        Saves the currently selected items and grid layout as a new saved view.
        """
        try:
            if not view_name:
                logger.warning("View name cannot be empty.")
                return

            selected_items = self.getSelectedItems()
            listGridData = {}
            currentGrid = {
                "row": self.rows,
                "col": self.columns,
                "timestamp": 0,
                "name": f"ahihi"
            }
            for item in selected_items:
                if isinstance(item,CameraGridItem):
                    if self.controller is None:
                        from src.common.controller.controller_manager import controller_manager
                        self.controller = controller_manager.get_controller(server_ip=item.cameraModel.get_property("server_ip"))
                    logger.info(f'selected_items1 = {item}')
                    listGridData[(item.row,item.col)] = {
                        "type": CommonEnum.ItemType.CAMERA,
                        "position": (item.row,item.col),
                        "id": item.cameraModel.id,
                        "row": item.row,
                        "col": item.col,
                        'rows_cell': item.rows_cell,
                        'cols_cell': item.cols_cell,
                        "width": item.width,
                        "height": item.height
                    }
                elif isinstance(item,DigitalMapGridItem):
                    if self.controller is None:
                        from src.common.controller.controller_manager import controller_manager
                        self.controller = controller_manager.get_controller(server_ip=item.mapModel.get_property("server_ip"))
                    listGridData[(item.row,item.col)] = {
                        "type": CommonEnum.ItemType.DIGITAL_MAP,
                        "position": (item.row,item.col),
                        "id": item.mapModel.id,
                        "row": item.row,
                        "col": item.col,
                        'rows_cell': item.rows_cell,
                        'cols_cell': item.cols_cell,
                        "width": item.width,
                        "height": item.height
                    }
                elif isinstance(item,FloorMapGridItem):
                    if self.controller is None:
                        from src.common.controller.controller_manager import controller_manager
                        self.controller = controller_manager.get_controller(server_ip=item.floorModel.get_property("server_ip"))
                    listGridData[(item.row,item.col)] = {
                        "type": CommonEnum.ItemType.FLOOR_MAP,
                        "position": (item.row,item.col),
                        "id": item.floorModel.id,
                        "row": item.row,
                        "col": item.col,
                        'rows_cell': item.rows_cell,
                        'cols_cell': item.cols_cell,
                        "width": item.width,
                        "height": item.height
                    }
                elif isinstance(item,EventGridItem):
                    if self.controller is None:
                        from src.common.controller.controller_manager import controller_manager
                        self.controller = controller_manager.get_controller(server_ip=item.eventAI.server_ip)
                    listGridData[(item.row,item.col)] = {
                        "type": CommonEnum.ItemType.EVENT,
                        "position": (item.row,item.col),
                        "id": item.eventAI.id,
                        "row": item.row,
                        "col": item.col,
                        'rows_cell': item.rows_cell,
                        'cols_cell': item.cols_cell,
                        "width": item.width,
                        "height": item.height
                    }
            data = {
                "name": view_name,
                "type": CommonEnum.TabType.VIRTUALWINDOW,
                "isShow": False,
                "currentGrid": str(currentGrid),
                "listGridData": str(listGridData),
                "listGridCustomData": "NewGrid"
            }
            def callback(data):
                if data is not None:
                    main_tree_view = main_controller.list_parent['MainTreeView']
                    main_tree_view.auto_add_virtualwindow(data,self.controller.server.data.server_ip) 
                else:
                    pass
                    # thêm notify lỗi tạo SavedView
            subThread = SubThread(parent=self, target=self.controller.create_tabmodel, args=(data,),callback=callback)
            subThread.start() 

        except Exception as e:
            logger.error(f"Error in saveSelectedItemsVirtualWindow: {e}", exc_info=True)

    def _updateIsSingleGridItem(self):
        new_value = (self._listGridItems.itemCount == 1)
        if self._isSingleGridItem != new_value:
            self._isSingleGridItem = new_value
            self.isSingleGridItemChanged.emit()

    @Property(bool, notify=isSingleGridItemChanged)
    def isSingleGridItem(self):
        return self._isSingleGridItem
