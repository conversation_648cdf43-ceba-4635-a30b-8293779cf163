from abc import abstractmethod
from http import HTTPStatus
import json
import time
from typing import Callable, Generic, List, Optional, TypeVar
import websocket
import threading
from websocket._exceptions import WebSocketBadStatusException
from .event_type import EventType
from .message_processor import MessageProcessor
from src.common.controller.controller_manager import Controller, controller_manager
from src.common.qml.models.map_controller import map_manager, MapModel
from PySide6.QtCore import QObject, Signal
import queue
import logging

logger = logging.getLogger(__name__)

THandler = TypeVar('THandler', bound=object)

class WebsocketClient(Generic[THandler]):

    ws: websocket.WebSocketApp
    url: str
    event_callback: Optional[Callable[[dict], None]]
    header: dict
    handlers: List[THandler]
    server_ip: str

    def __init__(self, url: str, header: Optional[dict] = None, 
                 server_ip: Optional[str] = None):
        self.url = url

        self.header = header or {}
        self.server_ip = server_ip
        self.handlers = []
        self.continuous = True
        self.ws = websocket.WebSocketApp(
            url=self.url,
            on_open=self.on_open,
            on_message=self.on_message,
            on_error=self.on_error,
            on_close=self.on_close,
            header=self.header
        )
        self.messageProcessor = MessageProcessor(server_ip=server_ip)
        
    def connect_background(self):
        self.thread = threading.Thread(target=self.connect)
        self.thread.daemon = True
        self.thread.start()

    def connect(self):
        self.ws.run_forever()

    def set_url(self, url: str):
        self.url = url
        self.ws.url = url

    def subscribe(self, handler: THandler):
        self.handlers.append(handler)

    def send(self, data):
        self.ws.send(data)

    def close(self):
        self.continuous = False
        self.ws.close()

    def on_open(self, ws):
        data = {"message": "You are connected"}
        event = EventType.connection_established
        message_json = {'event': event, 'data': data}
        self.__on_websocket_event(message_json)
        logger.info("Connected")

    def on_message(self, ws, message):
        try:
            message_json = json.loads(message)
            # print(f"[WebSocket] Received event: {message_json}")  # Log event
            # xử lý tính toán dữ liệu lâu
            self.messageProcessor.message_queue.put(message_json)
            # self.__on_websocket_event(message_json)
        except Exception as e:
            logger.exception("Error in on_message", exc_info=True)

    def on_error(self, ws, error: Exception):
        message = f"WebSocket error: {str(error)}"
        data = {"message": message, "error": str(error)}
        event = EventType.connection_error
        message_json = {'event': event, 'data': data}
        self.__on_websocket_event(message_json)

        if isinstance(error, WebSocketBadStatusException):
            if error.status_code == HTTPStatus.UNAUTHORIZED:
                ok = self.refresh_token()
                if not ok:
                    self.header = {'Id': '123'}
                    
    def refresh_token(self) -> bool:
        try:
            controller: Controller = controller_manager.get_controller(server_ip=self.server_ip)
            if not controller:
                return False

            refresh_result = controller.refresh_access_token()

            # Check if refresh_token has expired
            if refresh_result == "REFRESH_TOKEN_EXPIRED":
                logger.error(f"🔴 REFRESH TOKEN EXPIRED for server {self.server_ip}!")
                logger.error(f"🔴 User needs to login again - triggering re-login UI")

                # ✅ CRITICAL: Notify all camera models that tokens are expired
                # This will trigger camera_grid_item to stop video and show unauthorized state
                self._notify_token_expired()

                # Trigger re-login UI
                self._trigger_relogin_ui()
                return False

            # Check if refresh was successful
            if refresh_result and isinstance(refresh_result, str) and refresh_result != "REFRESH_TOKEN_EXPIRED":
                self.header['Authorization'] = f"Bearer {refresh_result}"

                # Notify map model of token change
                map_model: MapModel = map_manager.get_map_model(serverIp=self.server_ip)
                if map_model:
                    map_model.accessTokenChanged.emit()

                # Notify all camera models of token change
                from src.common.model.camera_model import camera_model_manager
                camera_list = camera_model_manager.get_camera_list(server_ip=self.server_ip)
                if camera_list:
                    logger.info(f"✅ Notifying {len(camera_list)} camera models of token change for server {self.server_ip}")
                    for camera_id, camera_model in camera_list.items():
                        if camera_model:
                            camera_model.accessTokenChanged.emit()
                            logger.debug(f"✅ Notified camera {camera_id} of access token change")

                            # ✅ TODO: Check if refresh_token also changed
                            # If API client detects refresh_token renewal, we should notify about that too
                            # This would be important for rotating refresh tokens or near-expiration scenarios
                            # camera_model.refreshTokenChanged.emit()  # Uncomment when needed

                logger.info(f"✅ WebSocket token refreshed successfully for server {self.server_ip}")
                return True
            return False
        except Exception as e:
            logger.error(f"Token refresh error: {e}")
            return False

    def _trigger_relogin_ui(self):
        """
        ✅ RELOGIN: Trigger re-login UI when refresh token expires

        This method is called when refresh_token has expired and user needs
        to login again to continue using the application.
        """
        try:
            logger.info(f"🔑 [RELOGIN] Triggering re-login UI for server {self.server_ip}")

            # Import here to avoid circular imports
            from PySide6.QtCore import QTimer, QMetaObject, Qt
            from PySide6.QtWidgets import QApplication

            def show_relogin_dialog():
                try:
                    # Get main window
                    app = QApplication.instance()
                    if not app:
                        logger.error("🔴 [RELOGIN] No QApplication instance found")
                        return

                    main_window = None
                    for widget in app.topLevelWidgets():
                        if hasattr(widget, 'objectName') and 'MainWindow' in str(widget.objectName()):
                            main_window = widget
                            break

                    if not main_window:
                        logger.warning("🔴 [RELOGIN] Main window not found, using active window")
                        main_window = app.activeWindow()

                    # Show re-login dialog
                    from src.common.widget.dialogs.relogin_dialog import ReLoginDialog
                    from src.utils.auth_qsettings import AuthQSettings

                    username = AuthQSettings.get_instance().settings.value("username", "")
                    dialog = ReLoginDialog(parent=main_window, username=username, server=None)

                    logger.info(f"🔑 [RELOGIN] Showing re-login dialog for user: {username}")
                    result = dialog.exec()

                    if result == dialog.Accepted:
                        logger.info(f"✅ [RELOGIN] User successfully re-logged in")
                        # Optionally restart WebSocket connection here
                    else:
                        logger.warning(f"❌ [RELOGIN] User cancelled re-login")

                except Exception as e:
                    logger.error(f"🔴 [RELOGIN] Error showing re-login dialog: {e}")
                    import traceback
                    logger.debug(f"🔴 [RELOGIN] Traceback: {traceback.format_exc()}")

            # Use QTimer to show dialog in main thread
            QTimer.singleShot(100, show_relogin_dialog)

        except Exception as e:
            logger.error(f"🔴 [RELOGIN] Error triggering re-login UI: {e}")

    def _notify_token_expired(self):
        """
        ✅ CRITICAL: Notify all camera models that tokens are expired

        This method is called when refresh_token has expired and all tokens
        are invalid. Camera models need to know this to:
        1. Stop video streaming
        2. Show unauthorized connection state
        3. Prevent further API calls with invalid tokens
        """
        try:
            logger.error(f"🔴 [TOKEN_EXPIRED] Notifying all camera models that tokens are expired for server {self.server_ip}")

            # Notify map model of token expiration
            from src.common.model.map_model import map_manager
            map_model = map_manager.get_map_model(serverIp=self.server_ip)
            if map_model:
                # Map model should handle token expiration
                logger.info(f"🔴 [TOKEN_EXPIRED] Notifying map model of token expiration")
                # map_model.tokenExpired.emit()  # If such signal exists

            # ✅ CRITICAL: Notify all camera models of token expiration
            from src.common.model.camera_model import camera_model_manager
            camera_list = camera_model_manager.get_camera_list(server_ip=self.server_ip)
            if camera_list:
                logger.error(f"🔴 [TOKEN_EXPIRED] Notifying {len(camera_list)} camera models of token expiration for server {self.server_ip}")
                for camera_id, camera_model in camera_list.items():
                    if camera_model:
                        # ✅ CRITICAL: Emit tokenExpired signal for complete token failure
                        # This is different from accessTokenChanged (which is for successful refresh)
                        # This is for complete token failure - need to stop everything
                        if hasattr(camera_model, 'tokenExpired'):
                            camera_model.tokenExpired.emit()
                            logger.debug(f"🔴 [TOKEN_EXPIRED] Emitted tokenExpired signal for camera {camera_id}")
                        else:
                            logger.warning(f"🔴 [TOKEN_EXPIRED] Camera model {camera_id} does not have tokenExpired signal")

            else:
                logger.warning(f"🔴 [TOKEN_EXPIRED] No camera models found for server {self.server_ip}")

        except Exception as e:
            logger.error(f"🔴 [TOKEN_EXPIRED] Error notifying camera models of token expiration: {e}")
            import traceback
            logger.debug(f"🔴 [TOKEN_EXPIRED] Traceback: {traceback.format_exc()}")

    def on_close(self, ws, close_status_code, close_msg):
        message = f"close_status_code = {close_status_code} close_msg = {close_msg}"
        data = {"message": message}
        event = EventType.connection_lost
        message_json = {'event': event, 'data': data}
        self.__on_websocket_event(message_json)
        logger.info(f"Trying reconnect {close_status_code} {close_msg}")
        time.sleep(1)
        if self.continuous:
            self.connect_background()

    def __on_websocket_event(self, message_json: dict):
        try:
            event = message_json['event']
            for handler in self.handlers:
                if event in handler.event_types:
                    handler.enqueue(message=message_json)
        except Exception:
            logger.exception("on_websocket_event exception error", exc_info=True)

