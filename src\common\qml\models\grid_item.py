from PySide6.QtCore import QObject, Property, Signal
from abc import abstractmethod
import logging
logger = logging.getLogger(__name__)

class GridItem(QObject):
    itemTypeChanged = Signal()
    zIndexChanged = Signal()
    coordinatesChanged = Signal()
    pixelSizeChanged = Signal(int, int)
    cellDimensionsChanged = Signal()
    rowChanged = Signal()
    colChanged = Signal()
    fullscreenChanged = Signal()
    selectedChanged = Signal()
    focusedChanged = Signal()
    occupiedPositionsChanged = Signal()
    rotationChanged = Signal()
    clickedChanged = Signal(QObject)
    unClickedChanged = Signal(QObject)
    isAnimatingChanged = Signal()

    def __init__(self,row:int = 0,col:int = 0,rows_cell:int = 1,cols_cell:int = 1):
        """Khởi tạo GridItem với các giá trị mặc định"""
        super().__init__()
        self._row: int = row
        self._col: int = col
        self._itemType: str = "base"
        self._zIndex: int = 0

        self._x: float = 0.0
        self._y: float = 0.0
        self._width: float = 100.0
        self._height: float = 100.0
        self._last_width: float = self._width
        self._last_height: float = self._height

        self._rows_cell: int = rows_cell
        self._cols_cell: int = cols_cell

        self._fullscreen: bool = False
        self._selected: bool = False
        self._focused: bool = False
        self._rotation: int = 0
        self._is_animating: bool = False

        # 🎯 OCCUPIED POSITIONS CACHE: Tránh tính toán lại occupied cells mỗi lần
        # Kịch bản: Multi-cell item (2x2) cần biết nó chiếm các vị trí nào: [(0,0), (0,1), (1,0), (1,1)]
        # Thay vì tính toán lại mỗi lần gọi getOccupiedCells() → cache kết quả
        # Chỉ clear cache khi row/col/size thay đổi → đảm bảo O(1) cho các lần gọi tiếp theo
        # VD: Item 2x2 tại (1,1) → cache = [(1,1), (1,2), (2,1), (2,2)]
        self._cached_occupied = None  # Cache danh sách (row,col) mà item này chiếm

    @Property(int, notify=rowChanged)
    def row(self):
        """Grid row property với signal notification"""
        return self._row

    @row.setter
    def row(self, value: int):
        """Set grid row và emit signals"""
        if self._row != value:
            self._row = value
            self._cached_occupied = None
            self.rowChanged.emit()
            self.occupiedPositionsChanged.emit()

    @Property(int, notify=colChanged)
    def col(self):
        """Grid column property với signal notification"""
        return self._col

    @col.setter
    def col(self, value: int):
        """Set grid column và emit signals"""
        if self._col != value:
            self._col = value
            self._cached_occupied = None
            self.colChanged.emit()
            self.occupiedPositionsChanged.emit()



    @Property(str, notify=itemTypeChanged)
    def itemType(self):
        return self._itemType

    @itemType.setter
    def itemType(self, value: str):
        if self._itemType != value:
            self._itemType = value
            self.itemTypeChanged.emit()

    @Property(float, notify=coordinatesChanged)
    def x(self):
        return self._x

    @x.setter
    def x(self, value: float):
        if self._x != value:
            self._x = value
            self.coordinatesChanged.emit()

    @Property(float, notify=coordinatesChanged)
    def y(self):
        return self._y

    @y.setter
    def y(self, value: float):
        if self._y != value:
            self._y = value
            self.coordinatesChanged.emit()

    @Property(float, notify=pixelSizeChanged)
    def width(self):
        return self._width

    @width.setter
    def width(self, value: float):
        if self._width != value:
            self._width = value
            self.pixelSizeChanged.emit(int(self._width), int(self._height))

    @Property(float, notify=pixelSizeChanged)
    def height(self):
        return self._height

    @height.setter
    def height(self, value: float):
        if self._height != value:
            self._height = value
            self.pixelSizeChanged.emit(int(self._width), int(self._height))

    @Property(int, notify=cellDimensionsChanged)
    def rows_cell(self):
        """Number of rows this item occupies"""
        return self._rows_cell

    @rows_cell.setter
    def rows_cell(self, value: int):
        if self._rows_cell != value:
            self._rows_cell = value
            self._cached_occupied = None
            self.cellDimensionsChanged.emit()
            self.occupiedPositionsChanged.emit()

    @Property(int, notify=cellDimensionsChanged)
    def cols_cell(self):
        """Number of columns this item occupies"""
        return self._cols_cell

    @cols_cell.setter
    def cols_cell(self, value: int):
        if self._cols_cell != value:
            self._cols_cell = value
            self._cached_occupied = None
            self.cellDimensionsChanged.emit()
            self.occupiedPositionsChanged.emit()

    @Property(list, notify=occupiedPositionsChanged)
    def occupied_positions(self):
        """Tính toán và cache các vị trí item chiếm"""
        if self._cached_occupied is None:
            self._cached_occupied = self.getOccupiedCells()
        return self._cached_occupied

    @Property(int, notify=zIndexChanged)
    def zIndex(self):
        """Z-index for layering (0=base, 15=maximized)"""
        return self._zIndex

    @zIndex.setter
    def zIndex(self, value: int):
        if self._zIndex != value:
            self._zIndex = value
            self.zIndexChanged.emit()
    
    # ✅ ITEM STATE: Individual item state properties
    @Property(bool, notify=fullscreenChanged)
    def fullscreen(self):
        """Fullscreen state for this item"""
        return self._fullscreen

    @fullscreen.setter
    def fullscreen(self, value: bool):
        if self._fullscreen != value:
            self._fullscreen = value
            self.fullscreenChanged.emit()

    @Property(bool, notify=selectedChanged)
    def selected(self):
        """Selection state for this item"""
        return self._selected

    @selected.setter
    def selected(self, value: bool):
        if self._selected != value:
            self._selected = value
            self.selectedChanged.emit()

    #Focus property
    @Property(bool, notify=focusedChanged)
    def focused(self):
        """Focus state for this item"""
        return self._focused

    @focused.setter
    def focused(self, value: bool):
        if self._focused != value:
            self._focused = value
            self.focusedChanged.emit()

    @Property(int, notify=rotationChanged)
    def rotation(self):
        """Rotation property in degrees (0, 90, 180, 270)"""
        return self._rotation

    @rotation.setter
    def rotation(self, value: int):
        normalized_value = value % 360

        if self._rotation != normalized_value:
            self._rotation = normalized_value
            self.rotationChanged.emit()

    @Property(bool, notify=isAnimatingChanged)
    def isAnimating(self):
        """Animation state for hiding borders during transitions"""
        return self._is_animating

    @isAnimating.setter
    def isAnimating(self, value: bool):
        if self._is_animating != value:
            self._is_animating = value
            self.isAnimatingChanged.emit()





    def getOccupiedCells(self) -> list:
        """Get list of all grid (row, col) tuples this item occupies"""
        occupied = []

        for row_offset in range(self._rows_cell):
            for col_offset in range(self._cols_cell):
                occupied_row = self._row + row_offset
                occupied_col = self._col + col_offset
                occupied.append((occupied_row, occupied_col))

        return occupied



    def wouldOccupyCellsAt(self, new_row: int, new_col: int) -> list:
        """Calculate which cells this item would occupy at a new row/col position"""
        occupied = []

        for row_offset in range(self._rows_cell):
            for col_offset in range(self._cols_cell):
                cell_row = new_row + row_offset
                cell_col = new_col + col_offset
                occupied.append((cell_row, cell_col))

        return occupied

    def canResizeTo(self, new_rows: int, new_cols: int, grid_rows: int, grid_columns: int) -> bool:
        """Check if item can be resized to new dimensions"""
        if new_rows <= 0 or new_cols <= 0:
            return False

        end_row = self._row + new_rows - 1
        end_col = self._col + new_cols - 1

        return (end_row < grid_rows and end_col < grid_columns)

    def canResizeToWithCollision(self, new_rows: int, new_cols: int,
                               grid_rows: int, grid_columns: int,
                               occupied_map: dict) -> tuple:
        """Kiểm tra resize có gây collision không với collision detection"""
        if not self.canResizeTo(new_rows, new_cols, grid_rows, grid_columns):
            return False, []

        new_positions = []
        for row_offset in range(new_rows):
            for col_offset in range(new_cols):
                pos = (self._row + row_offset, self._col + col_offset)
                new_positions.append(pos)

        conflicting_items = []
        for pos in new_positions:
            if pos in occupied_map:
                existing_item = occupied_map[pos]
                if existing_item != self and existing_item not in conflicting_items:
                    conflicting_items.append(existing_item)

        return len(conflicting_items) == 0, conflicting_items

    def wouldOccupyCellsWithSize(self, new_rows: int, new_cols: int) -> list:
        """Tính toán các cells mà item sẽ chiếm với kích thước mới"""
        occupied = []
        for row_offset in range(new_rows):
            for col_offset in range(new_cols):
                cell_row = self._row + row_offset
                cell_col = self._col + col_offset
                occupied.append((cell_row, cell_col))
        return occupied

    def resizeTo(self, new_rows: int, new_cols: int):
        """Resize item to new cell dimensions"""
        if new_rows > 0 and new_cols > 0:
            self._rows_cell = new_rows
            self._cols_cell = new_cols
            self.cellDimensionsChanged.emit()

    def canSwapWith(self, other_item: 'GridItem', grid_rows: int, grid_columns: int) -> bool:
        """Check if this item can swap positions with another item"""
        if not other_item:
            return False

        this_at_other_pos = self.wouldOccupyCellsAt(other_item.row, other_item.col)
        other_at_this_pos = other_item.wouldOccupyCellsAt(self.row, self.col)

        for row, col in this_at_other_pos + other_at_this_pos:
            if row >= grid_rows or col >= grid_columns or row < 0 or col < 0:
                return False

        return True

    def getDisplayInfo(self) -> dict:
        """Get complete display information for UI rendering"""
        return {
            "x": self._x,
            "y": self._y,
            "width": self._width,
            "height": self._height,
            "zIndex": self._zIndex,
            "rows_cell": self._rows_cell,
            "cols_cell": self._cols_cell,
            "row": self._row,
            "col": self._col,
            "itemType": self._itemType
        }

    def isValid(self) -> bool:
        """Validate grid item data"""
        return (self._row >= 0 and
                self._col >= 0 and
                self._width > 0 and
                self._height > 0 and
                self._rows_cell > 0 and
                self._cols_cell > 0 and
                self._itemType != "")

    def __str__(self):
        """String representation for debugging"""
        return f"GridItem(row={self._row}, col={self._col}, type={self._itemType}, cells={self._rows_cell}x{self._cols_cell}, pixels={self._width}x{self._height}, z={self._zIndex})"

    def __repr__(self):

        return f"GridItem(row={self._row}, col={self._col}, type={self._itemType}, cells={self._rows_cell}x{self._cols_cell}, pixels={self._width}x{self._height}, z={self._zIndex})"

    def __repr__(self):

        return f"GridItem(row={self._row}, col={self._col}, type={self._itemType}, cells={self._rows_cell}x{self._cols_cell}, pixels={self._width}x{self._height}, z={self._zIndex})"
