import threading
from src.common.camera.vlc_instance import VLCInstance
from src.styles.style import Style, Theme
from PySide6.QtCore import Qt, QPoint, QCoreApplication
from PySide6.QtGui import QIcon, QAction, QPixmap
from PySide6.QtWidgets import QWidget, QVBoxLayout, QLabel, QPushButton,QFrame,QMenu,QHBoxLayout
from src.common.server.server_info import ServerIn<PERSON>Model,server_info_model_manager
from src.presentation.server_screen.base_widget import BaseWidget
from src.presentation.server_screen.login_dialog import LoginDialog
from src.common.controller.controller_manager import Controller,controller_manager
from src.utils.camera_qsettings import camera_qsettings
from src.utils.theme_setting import theme_setting
from src.common.widget.notifications.notify import Notifications
from src.common.controller.main_controller import connect_slot,main_controller
import os
import logging
logger = logging.getLogger(__name__)

basedir = os.path.dirname(__file__)


class ServerItem(BaseWidget):
    def __init__(self,server_info: ServerInfoModel = None):
        super().__init__()

        self.server_info = server_info
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10,10,10,10)
        # main_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.main_widget = QWidget()
        
        self.main_widget.setObjectName("ServerItem")
        self.layout = QVBoxLayout(self.main_widget)
        self.layout.setContentsMargins(28,28,28,28)
        main_layout.addWidget(self.main_widget)
        self.create_top_widget()
        self.layout.addWidget(QWidget())
        self.create_content_widget()
        self.layout.setStretch(0,30)
        self.layout.setStretch(1,10)
        self.layout.setStretch(2,30)
        self.layout.setStretch(3,30)    
        self.set_style(hover=False)
        self.connect_slot()
        self.set_dynamic_stylesheet()
        self.login_dialog = None
        self.setFixedHeight(200)

    def connect_slot(self):
        connect_slot(
            (controller_manager.result_login, self.result_login),
            (controller_manager.start_login, self.start_login),
            (self.server_info.change_status_signal,self.change_status_signal),
            (self.server_info.change_server_info_signal,self.change_server_info_signal),
            (main_controller.complete_ui_update,self.complete_ui_update),
            )
        
    def enterEvent(self, event):
        if self.server_info.status:
            # Đã connect: chỉ đổi border, giữ background
            self.main_widget.setStyleSheet(f'''
                QWidget#ServerItem {{
                    background-color: {main_controller.get_theme_attribute("Color", "server_item_background_active")};
                    border-radius: 10px;
                    border: 2px solid {main_controller.get_theme_attribute("Color", "server_item_border_hoverred")};
                }}
            ''')
        else:
            # Chưa connect: đổi cả background và border
            self.main_widget.setStyleSheet(f'''
                QWidget#ServerItem {{
                    background-color: "{main_controller.get_theme_attribute("Color", "main_background")}";
                    border-radius: 10px;
                    border: 1.5px solid {main_controller.get_theme_attribute("Color", "server_item_border_hoverred")};
                }}
            ''')

    def leaveEvent(self, event):
        self.update_background_style()

    def create_top_widget(self):
        h_layout = QHBoxLayout()
        h_layout.setAlignment(Qt.AlignmentFlag.AlignBaseline|Qt.AlignmentFlag.AlignTop)
        h_layout.setContentsMargins(0,0,0,0)
        self.username = QLabel(self.server_info.data.username)
        
        right_layout = QHBoxLayout()
        right_layout.setContentsMargins(0,0,0,0)
        self.btn_edit = QPushButton()
        self.btn_edit.setFixedSize(20,28)
        self.btn_edit.clicked.connect(self.show_menu)
        
        # Thêm icon server vào đầu dòng username
        icon_server_path = main_controller.get_theme_attribute("Image", "icon_server_name")
        self.icon_server_label = QLabel()
        self.icon_server_label.setPixmap(QPixmap(icon_server_path).scaled(36, 36, Qt.KeepAspectRatio, Qt.SmoothTransformation))
        self.icon_server_label.setFixedSize(36, 36)
        # Thêm icon trạng thái online/offline cạnh username
        self.icon_state_label = QLabel()
        state_icon_path = main_controller.get_theme_attribute("Image", "icon_state_server_off")
        self.icon_state_label.setPixmap(QPixmap(state_icon_path).scaled(8, 8, Qt.KeepAspectRatio, Qt.SmoothTransformation))
        self.icon_state_label.setFixedSize(16, 16)
        # Layout ngang cho icon + username + trạng thái
        name_layout = QHBoxLayout()
        name_layout.setContentsMargins(0, 0, 0, 0)
        name_layout.setSpacing(6)
        name_layout.addWidget(self.icon_server_label)
        name_layout.addWidget(self.username)
        name_layout.addWidget(self.icon_state_label)
        name_layout.setAlignment(Qt.AlignVCenter | Qt.AlignLeft)
        h_layout.addLayout(name_layout, Qt.AlignLeft)

        # Đưa btn_edit lên góc trên bên phải
        right_layout.addWidget(self.btn_edit, alignment=Qt.AlignTop | Qt.AlignRight)
        right_layout.setAlignment(Qt.AlignTop | Qt.AlignRight)
        h_layout.addLayout(right_layout, Qt.AlignTop | Qt.AlignRight)

        self.layout.addLayout(h_layout)

    def create_content_widget(self):
        # Layout ngang: bên trái là info, bên phải là nút connect, cùng 1 dòng, căn giữa dọc
        h_layout = QHBoxLayout()
        h_layout.setContentsMargins(0, 0, 0, 0)
        h_layout.setSpacing(0)

        # Info bên trái (2 dòng text, căn giữa dọc)
        v_info = QVBoxLayout()
        v_info.setContentsMargins(0, 0, 0, 0)
        v_info.setSpacing(4)
        self.server_ip = QLabel(self.server_info.data.server_ip)
        self.server_ip.setStyleSheet("color: #b0b0b0; font-size: 16px; font-weight: 400;")
        self.server_port = QLabel(self.server_info.data.server_port)
        self.server_port.setStyleSheet("color: #b0b0b0; font-size: 16px; font-weight: 400;")
        v_info.addWidget(self.server_ip)
        v_info.addWidget(self.server_port)

        v_info_container = QWidget()
        v_info_container.setLayout(v_info)
        h_layout.addWidget(v_info_container, alignment=Qt.AlignVCenter)
        h_layout.addStretch()

        self.btn_connect = QPushButton(self.tr('Connect'))
        self.btn_connect.clicked.connect(self.btn_connect_clicked)
        self.btn_connect.setFixedSize(109,36)
        self.btn_connect.setStyleSheet(f'''
            QPushButton {{
                    color: {main_controller.get_theme_attribute("Color", "text_same_bg")};
                    }}  
            QPushButton{{
                background-color: {main_controller.get_theme_attribute("Color", "primary")};
                color: {main_controller.get_theme_attribute("Color", "text_same_bg")};
                font-size: 14px;
                border: {main_controller.get_theme_attribute("Color", "text_same_bg")};
                border-radius: 10px;
                padding: 1.5px;}}

            QPushButton::hover{{
                background-color: {main_controller.get_theme_attribute("Color", "text_pressed")};          
                font-size: 14px;
                border: 1.5px solid {main_controller.get_theme_attribute("Color", "text_same_bg")};
                border-radius: 10px;
                padding: 5px;}}
            QPushButton::pressed{{
                background-color: {main_controller.get_theme_attribute("Color", "text_pressed")};}}               
            '''
        )
            # self.m
        h_layout.addWidget(self.btn_connect, alignment=Qt.AlignVCenter)

        self.layout.addLayout(h_layout)

    #@slot 
    def change_server_info_signal(self,data):
        self.username.setText(self.server_info.data.username)
        self.server_ip.setText(self.server_info.data.server_ip)
        self.server_port.setText(self.server_info.data.server_port)

    #@slot
    def change_status_signal(self,data):
        if data:
            self.btn_edit.hide()
            self.btn_connect.setText(self.tr("Disconnect"))
            self.update_background_style()
            self.btn_connect.setStyleSheet(f'''
                    QPushButton{{
                        background-color: transparent;
                        color: {main_controller.get_theme_attribute("Color", "text")};
                        font-size: 14px;
                        border: 1.5px solid {main_controller.get_theme_attribute("Color", "text")};
                        border-radius: 10px;
                        padding: 5px;}}
                    QPushButton::hover{{
                        background-color: {main_controller.get_theme_attribute("Color", "text_pressed")};             
                        font-size: 14px;
                        border: 1.5px solid {main_controller.get_theme_attribute("Color", "text")};
                        border-radius: 10px;
                        padding: 5px;}}
                    QPushButton::pressed{{
                        background-color: {main_controller.get_theme_attribute("Color", "text_pressed")};}}              
                '''
                )
            proxy_widget = self.graphicsProxyWidget()
            proxy_widget.setOpacity(1)
            # Cập nhật icon trạng thái online/offline
            state_icon_path = main_controller.get_theme_attribute("Image", "icon_state_server_on")
            self.icon_state_label.setPixmap(QPixmap(state_icon_path).scaled(8, 8, Qt.KeepAspectRatio, Qt.SmoothTransformation))
        else:
            self.btn_edit.show()
            self.btn_connect.setText(self.tr("Connect"))
            self.update_background_style()
            self.btn_connect.setStyleSheet(f'''
                QPushButton {{
                        color: {main_controller.get_theme_attribute("Color", "text_same_bg")};
                        }}  
                QPushButton{{
                    background-color: {main_controller.get_theme_attribute("Color", "primary")};
                    color: {main_controller.get_theme_attribute("Color", "text_same_bg")};
                    font-size: 14px;
                    border: {main_controller.get_theme_attribute("Color", "text_same_bg")};
                    border-radius: 10px;
                    padding: 1.5px;}}

                QPushButton::hover{{
                    background-color: {main_controller.get_theme_attribute("Color", "text_pressed")};}}               
                    font-size: 14px;
                    border: 1.5px solid {main_controller.get_theme_attribute("Color", "text_same_bg")};
                    border-radius: 10px;
                    padding: 5px;
                QPushButton::pressed{{
                    background-color: {main_controller.get_theme_attribute("Color", "text_pressed")};}}               
                '''
            )
            proxy_widget = self.graphicsProxyWidget()
            proxy_widget.setOpacity(1)
            # Cập nhật icon trạng thái online/offline
            state_icon_path = main_controller.get_theme_attribute("Image", "icon_state_server_off")
            self.icon_state_label.setPixmap(QPixmap(state_icon_path).scaled(8, 8, Qt.KeepAspectRatio, Qt.SmoothTransformation))
    def btn_connect_clicked(self):
        logger.debug(f'btn_connect_clicked self.server_info.status = {self.server_info.status} self.server_info.is_connecting = {self.server_info.is_connecting}')
        if not self.server_info.status:
            # Connect Server
            if not self.server_info.is_connecting:
                self.server_info.is_connecting = True
                logger.debug(f"Connect to server: {self.server_info.data.server_ip}")
                controller = Controller(server= self.server_info)
                # controller_manager.start_login.emit((controller))
                self.start_login(controller)
        else:
            # Disconnect Server
            controller = controller_manager.get_controller(id = self.server_info.data.id)
            controller_manager.exit_controller(controller = controller)
            controller.server.set_status(False)
            
    def delete_server_clicked(self):
        server_info_model_manager.delete_server(server = self.server_info)

    def btn_delete_clicked(self):
        logger.debug(f"btn_delete_clicked = {self.server_info.data}")

    def user_clicked(self):
        logger.debug(f"User clicked = {self.server_info.data}")

    def change_password_clicked(self):
        if not self.server_info.status:
            if not self.server_info.is_connecting:
                # logger.debug(f"Connect to server: {self.server_info.data.server_ip}")
                controller = Controller(server= self.server_info)
                # controller_manager.start_login.emit((controller))
                login_dialog = LoginDialog(parent = main_controller.list_parent['MainScreen'],controller = controller,change_password=True)
                login_dialog.exec()

    def start_login(self,data):
        controller: Controller = data
        logger.debug(f"ServerItem start_login = {controller.server.data}")
        if controller.server.data.password is not None:
            main_controller.signal_load_animation.emit(True)
            controller.login(parent=self)
        else:
            login_dialog = LoginDialog(parent = main_controller.list_parent['MainScreen'],controller = controller)
            login_dialog.exec()

    def complete_ui_update(self,controller):
        if controller.server == self.server_info:
            self.server_info.is_connecting = False
            controller.server.set_status(True)

    def result_login(self, data):
        result, controller, deny_permission = data
        # print(f'result_login = {result, controller, deny_permission}')        

        if controller.server == self.server_info:
            if result:
                controller_manager.add_controller(controller=controller)
                camera_qsettings.set_server(json_server=controller.server.data.to_dict())
                # logger.debug(f"ServerItem result_login A = {controller.server.data.username,controller.server.data.server_ip,controller.server.data.server_port}")
                self.username.setText(controller.server.data.username)
                self.server_ip.setText(controller.server.data.server_ip)
                self.server_port.setText(controller.server.data.server_port)
                self.login_dialog = None
            else:
                self.server_info.is_connecting = False
                main_controller.signal_load_animation.emit(False)
                Notifications(parent=main_controller.list_parent['MainScreen'], title=self.tr("Can't connect to server!"),icon=Style.PrimaryImage.fail_result)
                # KHÔNG mở lại login dialog, KHÔNG add controller, KHÔNG lưu server info khi login fail
                # Chỉ hiện thông báo lỗi và giữ nguyên ở màn hình server list

            # init vlc with threading
            vlc_thread = threading.Thread(target=VLCInstance)
            vlc_thread.start()
                

    def show_menu(self):
        self.menu = QMenu()
        user_info = QAction(icon=QIcon(main_controller.get_theme_attribute("Image", "user")),
                                 text=self.tr("User information"), parent=self.menu)
        user_info.triggered.connect(self.user_clicked)

        change_password = QAction(icon=QIcon(main_controller.get_theme_attribute("Image", "lock")),
                                 text=self.tr("Change password"), parent=self.menu)
        change_password.triggered.connect(self.change_password_clicked)
        delete_server = QAction(icon=QIcon(main_controller.get_theme_attribute("Image", "lock")),
                                 text=self.tr("Delete"), parent=self.menu)
        delete_server.triggered.connect(self.delete_server_clicked)
        

        self.menu.addAction(user_info)
        self.menu.addAction(change_password)
        self.menu.addAction(delete_server)
        # self.menu.setWindowFlags(
        #     Qt.WindowType.FramelessWindowHint | Qt.WindowType.Popup)
        self.menu.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, True)
        self.menu.setStyleSheet(Style.PrimaryStyleSheet.get_context_menu_style(theme_instance=main_controller))
        position = self.btn_edit.mapToGlobal(QPoint(10, 10))
        self.menu.exec_(position)

    def mousePressEvent(self, event):
        # logger.debug(f'mousePressEvent CameraWidget = {self.server_info.data}')
        if event.button() == Qt.LeftButton:
            if not self.server_info.status:
                if not self.server_info.is_connecting:
                    self.server_info.is_connecting = True
                    # logger.debug(f"Connect to server: {self.server_info.data.server_ip}")
                    controller = Controller(server= self.server_info)
                    # controller_manager.start_login.emit((controller))
                    self.start_login(controller)
            else:
                # Disconnect Server
                pass

    def restranslate_item_server(self):
        if self.server_info.status:
            self.btn_connect.setText(QCoreApplication.translate("ServerItem", 'Disconnect'))
        else:
            self.btn_connect.setText(QCoreApplication.translate("ServerItem", 'Connect'))

    def set_dynamic_stylesheet(self):
        self.icon_server_label.setPixmap(
            QPixmap(main_controller.get_theme_attribute("Image", "icon_server_name")).scaled(36, 36, Qt.KeepAspectRatio, Qt.SmoothTransformation)
        )

        self.username.setStyleSheet(f'''                   
                    color: {main_controller.get_theme_attribute("Color", "server_item_title")};
                    font-size: 24px;
                    font-weight: bold;
                    border: none;
                    ''')
        
        self.btn_edit.setStyleSheet(f'''
                QPushButton {{
                    qproperty-icon: url({main_controller.get_theme_attribute("Image", "server_item_edit_icon")});
                    color: {main_controller.get_theme_attribute("Color", "server_item_title")};
                }}  
                QPushButton{{
                    background-color: transparent;
                    color: {main_controller.get_theme_attribute("Color", "server_item_title")};
                    font-size: 14px;
                    font-weight: bold;
                    border: none;
                    border-radius: 4px;
                    padding: 5px;}}
                QPushButton::disabled{{
                    background-color: #5c5c5c;}}
                QPushButton::hover{{
                    background-color: {main_controller.get_theme_attribute("Color", "server_item_edit_button_background_hoverred")};}}   
                QPushButton::pressed{{
                    background-color: {main_controller.get_theme_attribute("Color", "server_item_edit_button_background_hoverred")};}}              
            '''
            )
        
        self.server_ip.setStyleSheet(f'''color: {main_controller.get_theme_attribute("Color", "server_item_title")}''')
        self.server_port.setStyleSheet(f'''color: {main_controller.get_theme_attribute("Color", "server_item_title")}''')
        if self.server_info.status:
            state_icon_path = main_controller.get_theme_attribute("Image", "icon_state_server_on")
            self.main_widget.setStyleSheet(f'''
                    QWidget#ServerItem {{
                            background-color: {main_controller.get_theme_attribute("Color", "server_item_background_active")};
                            border-radius: 10px;
                            border: 1.5px solid {main_controller.get_theme_attribute("Color", "main_border")};
                            }}
            ''')
            self.btn_connect.setStyleSheet(f'''
                    QPushButton{{
                        background-color: transparent;
                        color: {main_controller.get_theme_attribute("Color", "text")};
                        font-size: 14px;
                        border: 1.5px solid {main_controller.get_theme_attribute("Color", "text")};
                        border-radius: 10px;
                        padding: 5px;}}
                    QPushButton::hover{{
                        font-size: 14px;
                        border: 1.5px solid {main_controller.get_theme_attribute("Color", "text_same_bg")};
                        border-radius: 10px;
                        padding: 5px;}}
                    QPushButton::pressed{{
                        background-color: {main_controller.get_theme_attribute("Color", "text_pressed")};}}               
                '''
                )
        else:
            state_icon_path = main_controller.get_theme_attribute("Image", "icon_state_server_off")
            self.main_widget.setStyleSheet(f'''
                    QWidget#ServerItem {{
                            background-color: {main_controller.get_theme_attribute("Color", "main_background")};
                            border-radius: 10px;
                            border: 1.5px solid {main_controller.get_theme_attribute("Color", "main_border")};
                            }}
            ''')
            self.btn_connect.setStyleSheet(f'''
                QPushButton {{
                        color: {main_controller.get_theme_attribute("Color", "text_same_bg")};
                        }}  
                QPushButton{{
                    background-color: {main_controller.get_theme_attribute("Color", "primary")};
                    color: {main_controller.get_theme_attribute("Color", "text_same_bg")};
                    font-size: 14px;
                    border: {main_controller.get_theme_attribute("Color", "text_same_bg")};
                    border-radius: 10px;
                    padding: 1.5px;}}

                QPushButton::hover{{
                    background-color: {main_controller.get_theme_attribute("Color", "text_pressed")};}}               
                    font-size: 14px;
                    border: 1.5px solid {main_controller.get_theme_attribute("Color", "text_same_bg")};
                    border-radius: 10px;
                    padding: 5px;
                QPushButton::pressed{{
                    background-color: {main_controller.get_theme_attribute("Color", "text_pressed")};}}               
                '''
            )
        self.icon_state_label.setPixmap(QPixmap(state_icon_path).scaled(8, 8, Qt.KeepAspectRatio, Qt.SmoothTransformation))

    def update_background_style(self):
        if self.server_info.status:
            self.main_widget.setStyleSheet(f'''
                    QWidget#ServerItem {{
                            background-color: {main_controller.get_theme_attribute("Color", "server_item_background_active")};
                            border-radius: 10px;
                            border: 1.5px solid {main_controller.get_theme_attribute("Color", "main_border")};
                            }}
            ''')
        else:
            self.main_widget.setStyleSheet(f'''
                    QWidget#ServerItem {{
                            background-color: {main_controller.get_theme_attribute("Color", "main_background")};
                            border-radius: 10px;
                            border: 1.5px solid {main_controller.get_theme_attribute("Color", "main_border")};
                            }}
            ''')