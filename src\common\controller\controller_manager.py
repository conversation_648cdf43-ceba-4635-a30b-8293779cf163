import concurrent.futures

from PySide6.QtCore import QObject, Signal, QThreadPool
import functools
import gc

from src.api.generic_runnable import GenericRunnable
from src.common.model.file_model import FileModel
from src.common.model.function_permission_model import FunctionMenuModel, FunctionMenu, function_menu_model_manager
from src.common.model.profile_group_model import ProfileGroupModel, profile_group_model_manager, ProfileGroup
from src.common.model.user_model import UserModel, <PERSON>, user_model_manager, UserRole
from src.common.model.user_role_model import RoleModel, Role, role_model_manager, RoleCamera, RoleGroup, RoleMenu
from src.common.onvif_api.worker_thread import WorkerThread
from src.common.model.event_data_model import EventAI, EventData, event_manager
from src.common.model.item_grid_model import ItemGridModel
from src.common.model.discovery_camera_model import DiscoveryCameraModel
from src.common.widget.notifications.listen_message_notifications import listen_show_notification
from src.utils.config import Config
from src.utils.camera_qsettings import camera_qsettings
from PySide6.QtCore import Qt,Signal,QObject
from src.api.api_client import APIClient, HTTPStatusCode, TypeRequestVMS,APIThread
from src.common.model.group_tree_model import GroupTreeModel
from src.common.model.profile import Profile
from src.common.model.aiflows_model import AiFlow,AiFlowModel,aiflow_model_manager
from src.common.model.door_model import Door,DoorModel,door_model_manager
from src.common.model.group_model import Group, GroupModel, group_model_manager
from src.common.model.camera_model import Camera,CameraModel,camera_model_manager
from src.common.model.record_model import record_model_manager,RecordModel
from src.common.server.server_info import ServerInfoModel,server_info_model_manager
from src.common.controller.main_controller import main_controller
from src.common.model.device_models import CameraType, GroupType
from src.common.key_board.shortcut_key import ShortCutKey,shortcut_key_model_manager,ShortCutKeyModel,ShortCutKey
from src.common.camera.video_player_manager import video_player_manager
from src.common.qml.models.map_controller import FloorModel,floor_manager,BuildingModel,building_manager,MapModel,map_manager
from src.common.qml.models.grid_model import GridModel
from src.presentation.camera_screen.managers.grid_manager import gridManager
from src.common.qml.models.schedule_model import schedule_manager
from PySide6.QtCore import QPoint
from typing import List, Callable
import traceback
import threading
import datetime
import logging
import time
from src.utils.utils import Utils
logger = logging.getLogger(__name__)
class Controller(QObject):
    device_item_expanded_signal = Signal()
    device_group_item_expanded_signal = Signal()
    def __init__(self,server:ServerInfoModel = None):
        super().__init__()
        self.server = server
        self.show_message_dialog = None
        self.list_aiflows = {}
        self.current_tab = 0
        self.current_tab_name = ""
        self.current_grid_model: ItemGridModel = None
        self.audio_thread = None
        self.stop_update_ai_event = False
        self.update_profiles = None
        self.list_camera_ids = []
        self.list_parent = {}
        self.open_camera_in_tab = None
        self.has_cuda_available = False
        self.has_intel_gpu_available = False
        self.message_log = None
        self.progress_dialog = None
        self.total_camera_items = 11
        self.total_group_items = 11
        self.event_selected = None
        self.key_filter = None
        self.id_event_search_camera = None
        # Device Screen parameters
        self.count = 0
        self.header_data = {}
        self.header_data_group = {}
        # camera
        self.camera_data = []
        self.device_data_filtered = []
        self.device_group_data_filtered = []
        self.current_page = 1
        self.list_state_checkbox = {}

        self.list_state_checkbox = {}
        self.list_state_checkbox_group = {}
        self.list_state_button = {}
        self.list_state_button_group = {}
        self.update_state_action_camera = None
        self.update_state_action_group = None
        self.update_state_delegate = None
        self.update_state_delegate_group = None
        self.all_checkbox = Qt.CheckState.Unchecked
        self.all_checkbox_group = Qt.CheckState.Unchecked
        self.switch_stacked_widget = None
        self.total_papes_camera = 0
        self.total_papes_group = 0
        self.list_string_sub_group = ''
        self.list_string_camera = ''

        # Permissions Screen parameters
        self.user_data = []
        self.user_data_filter = []
        self.current_user_table_page = 1
        self.total_user_items = 10

        self.roles_data = []
        self.roles_data_filter = []
        self.current_roles_table_page = 1
        self.total_role_items = 10

        self.pick_file_model = None
        #####
        self.list_camera_settings = {}
        self.camera_columns = 0
        self.create_camera_settings()
        self.update_camera_table()

        self.list_group_settings = {}
        self.group_columns = 0
        self.create_group_settings()
        self.update_group_table()
        self.list_snapshot = {}
        self.list_play_pause_state = {}
        self.main_stream_resolution = {}
        self.main_stream_fps = {}
        self.sub_stream_resolution = {}
        self.sub_stream_fps = {}
        self.record_resolution = {}
        self.websocket_event = None
        self.websocket_vms = None
        self.api_client = APIClient(server=server)
        self.threads = []
        self.thread_pool = QThreadPool()
        self.executor = concurrent.futures.ThreadPoolExecutor(max_workers=5)

    def create_camera_settings(self):
        key_value = '{name}'.format(name = 'ID')
        self.list_camera_settings.clear()
        self.list_camera_settings[CameraType().camera_name] = [Qt.CheckState.Checked,None]
        self.list_camera_settings[CameraType().camera_branch] = [Qt.CheckState.Checked,None]
        self.list_camera_settings[CameraType().ip_address] = [Qt.CheckState.Checked,None]
        self.list_camera_settings[CameraType().type] = [Qt.CheckState.Checked,None]
        self.list_camera_settings[CameraType().model] = [Qt.CheckState.Checked,None]
        self.list_camera_settings[CameraType().camera_group] = [Qt.CheckState.Checked,None]
        self.list_camera_settings[CameraType().status] = [Qt.CheckState.Checked,None]
        self.list_camera_settings[CameraType().ai_flows] = [Qt.CheckState.Checked,None]
        self.list_camera_settings[CameraType().action] = [Qt.CheckState.Checked,None]
        self.list_camera_settings[CameraType().all] = [Qt.CheckState.Unchecked,None]

    def update_camera_table(self):
        count = 1
        for key, value in self.list_camera_settings.items():
            if key != CameraType().all:
                value[1] = count
                count += 1
            else:
                value[1] = None
        self.camera_columns = count
        for key, value in self.list_camera_settings.items():
            pass

    def create_group_settings(self):
        key_value = '{name}'.format(name = 'ID')
        self.list_group_settings.clear()
        self.list_group_settings[GroupType().group_name] = [Qt.CheckState.Checked,None]
        self.list_group_settings[GroupType().sub_group] = [Qt.CheckState.Unchecked,None]
        self.list_group_settings[GroupType().number_cameras] = [Qt.CheckState.Checked,None]
        self.list_group_settings[GroupType().description] = [Qt.CheckState.Unchecked,None]
        self.list_group_settings[GroupType().ai_flows] = [Qt.CheckState.Checked,None]
        self.list_group_settings[GroupType().action] = [Qt.CheckState.Checked,None]
        self.list_group_settings[GroupType().all] = [Qt.CheckState.Unchecked,None]

    def update_group_table(self):
        count = 1
        for key, value in self.list_group_settings.items():
            if value[0] == Qt.CheckState.Checked and key != GroupType().all:
                value[1] = count
                count += 1
            else:
                value[1] = None
        self.group_columns = count
        for key, value in self.list_group_settings.items():
            pass
    def getCount(self):
        return self.count

    def clear_all(self):
        self.list_cameras = []
        self.list_camera_in_group = []
        self.list_groups = []
        self.list_aiflows = {}
        self.list_tree_group = []
        self.list_camera_non_group = []
        self.list_top_group = []
        self.list_top_group_only_name = []
        self.list_camera_widget_available = []
        self.list_ptz_onvif = []
        self.current_tab = 0
        self.current_tab_name = ""
        self.current_grid_model = None
        self.grid_item_selected = {}
        self.audio_thread = None
        self.list_camera_fullscreen = {}
        self.list_parent = {}

    def login(self,parent = None,server_ip = None,server_port = None,websocket_port = None,username = None,password = None):
        username = self.server.data.username
        password = self.server.data.password
        login_thread = APIThread(parent=parent,target=self.api_client.login,args=(username,password,self.server.data.captcha,self.server.data.captcha_id),callback=self.callback_login)
        login_thread.start()

    def callback_login(self, response):
        deny_permission = False
        if not response or response.status_code != 200:
            controller_manager.result_login.emit((False, self, deny_permission))
            return

        # Only check permissions for EMS API URLs
        if not (Config.EMS_API_URL in self.api_client.server_url or 
                Config.EMS_API_URL_BACKUP in self.api_client.server_url):
            controller_manager.result_login.emit((True, self, deny_permission))
            return

        # Check user permissions
        result = self.api_client.get_current_user_info()
        controller_manager.result_login.emit((True, self, deny_permission))

    def refresh_access_token(self):
        return self.api_client.refresh_access_token()

    def add_thread(self, target: Callable):
        thread = threading.Thread(target=target)
        self.threads.append(thread)
        thread.start()

    def get_data_from_server(self):
        def get_data():
            start_time = time.time()
            self.add_thread(target=self.get_data_tree)
            self.add_thread(target=self.get_aiflows)
            self.add_thread(target=self.get_groups)
            self.add_thread(target=self.get_recording_schedule)
            self.add_thread(target=self.get_doors)
            self.add_thread(target=self.get_tabmodel)
            self.add_thread(target=self.get_shortcut_key)
            self.add_thread(target=self.get_all_users)
            self.add_thread(target=self.get_all_roles)
            self.add_thread(target=self.get_all_menu_function)
            self.add_thread(target=self.get_all_profile_group)
            [t.join() for t in self.threads]
            main_controller.complete_fetching_data.emit(self)
            main_controller.complete_ui_update.emit(self)
            end_time = time.time()
            logger.debug(f'get_data_from_server time = {end_time-start_time}')
        threading.Thread(target=get_data).start()

    def callback_create_camera_thread(self, response):
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                if self.show_message_dialog:
                    self.show_message_dialog(
                        response, TypeRequestVMS.CREATE_CAMERA)
                camera_model = CameraModel(data=response.json())
                camera_model.set_property("server_ip",self.server.data.server_ip)
                camera_model_manager.add_camera(controller = self,camera=camera_model)
                if camera_model.get_property("cameraGroupIds") is not None and len(camera_model.get_property("cameraGroupIds")) > 0:
                    self.get_groups()
                return response.json()
            else:
                if self.show_message_dialog:
                    self.show_message_dialog(response)

    def create_camera(self,parent = None, data: Camera = None):
        self.api_client.callback_create_camera(
            parent=parent, data=data.to_dict(), callback=self.callback_create_camera_thread)

    def callback_create_camera(self, response):
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                if self.show_message_dialog:
                    self.show_message_dialog(
                        response, TypeRequestVMS.CREATE_CAMERA)
                camera_model = CameraModel(data=response.json())
                camera_model_manager.add_camera(controller = self,camera=camera_model)
                if camera_model.get_property("cameraGroupIds") is not None and len(camera_model.get_property("cameraGroupIds")) > 0:
                    self.get_camera_groups()
                listen_show_notification.listen_API_success_message_signal.emit(
                    (response, TypeRequestVMS.CREATE_CAMERA, None))
                return response.json()
            else:
                json_res = response.json()
                error_data = json_res['data']
                listen_show_notification.listen_API_fail_message_signal.emit(
                    (response, TypeRequestVMS.CREATE_CAMERA, error_data[0]))
        return None

    def add_camera_to_group(self, data = None):
        def func(data):
            response = self.api_client.add_camera_to_group(data)
            if response is not None:
                if response.status_code == HTTPStatusCode.OK.value:
                    if self.show_message_dialog:
                        self.show_message_dialog(
                            response, TypeRequestVMS.CREATE_CAMERA)
                    data = response.json()
                    logger.debug(f'add_camera_to_group = {data}')

                else:
                    json_res = response.json()
                    error_data = json_res['data']
                    listen_show_notification.listen_API_fail_message_signal.emit(
                        (response, TypeRequestVMS.CREATE_CAMERA, error_data[0]))
            return None
        threading.Thread(
            target=func, args=(data,)).start()

    def add_discovered_camera(self, data = None):
        def func(data):
            response = self.api_client.add_discovered_camera(data)
            if response is not None:
                if response.status_code == HTTPStatusCode.OK.value:
                    if self.show_message_dialog:
                        self.show_message_dialog(
                            response, TypeRequestVMS.CREATE_CAMERA)
                    data = response.json()

                    for item in data:
                        camera_model = CameraModel(data=item)
                        camera_model.set_property("server_ip",self.server.data.server_ip)
                        camera_model_manager.add_camera(controller = self,camera=camera_model)
                    listen_show_notification.listen_API_success_message_signal.emit(
                        (response, TypeRequestVMS.CREATE_CAMERA, None))
                else:
                    json_res = response.json()
                    error_data = json_res['data']
                    listen_show_notification.listen_API_fail_message_signal.emit(
                        (response, TypeRequestVMS.CREATE_CAMERA, error_data[0]))
            else:
                # loi server
                error_message = 'SERVER_DISCONNECTED'
                listen_show_notification.listen_API_fail_message_signal.emit(
                    (response, TypeRequestVMS.CREATE_CAMERA, error_message))
        threading.Thread(
            target=func, args=(data,)).start()

    def discovered_camera(self, data = None):
        def func(data):
            response = self.api_client.discovered_camera(data)
            if response is not None:
                if response.status_code == HTTPStatusCode.OK.value:
                    if self.show_message_dialog:
                        self.show_message_dialog(
                            response, TypeRequestVMS.CREATE_CAMERA)
                    json_data = response.json()
                    logger.debug(f'discovered_camera = {json_data}')
                    self.get_cameras()

                else:
                    json_res = response.json()
                    error_data = json_res['data']
                    listen_show_notification.listen_API_fail_message_signal.emit(
                        (response, TypeRequestVMS.CREATE_CAMERA, error_data[0]))

        threading.Thread(
            target=func, args=(data,)).start()

    def create_cameras(self, parent=None, listdata: List[Camera] = None):
        list_input = []
        for item in listdata:
            list_input.append(item.to_dict())
        logger.debug(f'create_cameras = {list_input}')
        self.api_client.callback_create_cameras(
            parent=parent, data=list_input, callback=self.callback_create_cameras)

    def callback_create_cameras(self, response):
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                if self.show_message_dialog:
                    self.show_message_dialog(
                        response, TypeRequestVMS.CREATE_CAMERAS)
                data = response.json()
                is_cameraGroupIds = False
                camera_list = []
                for index,item in enumerate(data):
                    camera = CameraModel(data=item)
                    camera_list.append(camera)
                    if camera.get_property("cameraGroupIds") is not None and len(camera.get_property("cameraGroupIds")) > 0:
                        is_cameraGroupIds = True
                camera_model_manager.add_cameras(camera_list=camera_list)
                if is_cameraGroupIds:
                    self.get_camera_groups()
                listen_show_notification.listen_API_success_message_signal.emit(
                    (response, TypeRequestVMS.CREATE_CAMERAS, None))
            else:
                json_res = response.json()
                error_data = json_res['data']
                listen_show_notification.listen_API_fail_message_signal.emit(
                    (response, TypeRequestVMS.CREATE_CAMERAS, error_data[0]))

    def update_camera_by_put(self, data: Camera = None):
        threading.Thread(
            target=self.update_camera_thread_by_put, args=(data,)).start()

    def update_camera_thread_by_put(self, data: dict = None):
        response = self.api_client.update_camera_by_put(data=data)
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                if self.show_message_dialog:
                    self.show_message_dialog(
                        response, TypeRequestVMS.UPDATE_CAMERA)
                camera_model = CameraModel(data=data)
                diff = []
                camera_model_origin:CameraModel = camera_model_manager.get_camera_model(id = camera_model.get_property("id",None))
                if camera_model_origin is not None:
                    diff = camera_model_origin.diff_camera_model(camera = camera_model.data)
                    if 'cameraGroupIds' in diff:
                        self.get_groups() 
                listen_show_notification.listen_API_success_message_signal.emit(
                    (response, TypeRequestVMS.UPDATE_CAMERA, None))
                return data
            else:
                json_res = response.json()
                error_data = json_res['data']
                listen_show_notification.listen_API_fail_message_signal.emit(
                    (response, TypeRequestVMS.UPDATE_CAMERA, error_data[0]))
        else:
            logger.debug("update_camera_thread_by_put error")
        return None

    def delete_camera_thread(self, data: dict = None):
        logger.debug(f'delete_camera_thread = {data}')
        response = self.api_client.delete_camera(id=data.get("id",None),urlMainstream=data.get("urlMainstream",None),serviceIds=data.get("serviceIds",None),activate=False)
        if response is not None:
            if response.status_code == HTTPStatusCode.NO_CONTENT.value or response.status_code == HTTPStatusCode.OK.value:
                listen_show_notification.listen_API_success_message_signal.emit(
                    (response, TypeRequestVMS.DELETE_CAMERA, None))
                self.get_cameras()
                self.get_groups()
            else:
                json_res = response.json()
                error_data = json_res['data']
                listen_show_notification.listen_API_fail_message_signal.emit(
                    (response, TypeRequestVMS.DELETE_CAMERA, error_data[0]))

    def delete_camera(self, data: Camera = None):
        threading.Thread(target=self.delete_camera_thread,
                         args=(data,)).start()

    def get_cameras(self):
        response = self.api_client.get_cameras()
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                data = response.json()
                # logger.info(f'get_cameras = {data}')
                camera_model_manager.complete_fetching_data.emit((data,self))
                return data
            else:
                return None
        return None


    def get_groups(self,ids = None):
        response = self.api_client.get_groups(ids = ids)
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                if ids is None:

                    data = response.json()
                    data_list = []
                    for item in data:
                        # if item['type'] == "THIRD_PARTY_SERVER":
                        #     logger.debug(f'get_groups item = {item}')
                        group = GroupModel(data=item)
                        group.set_property("server_ip",self.server.data.server_ip)
                        data_list.append(group)
                    group_list = group_model_manager.get_group_list(server_ip=self.server.data.server_ip)
                    if group_list:
                        if len(group_list) == len(data_list):
                            # sử dụng get_camera_groups() trong trường hợp update group
                            group_model_manager.update_group_list(controller = self,group_list=data_list)
                        else:
                            # sử dụng get_camera_groups trong trường hợp delete group
                            group_model_manager.find_group_deleted(controller = self, group_list=data_list)
                            # sau khi xóa Group thì cần cập nhật camera từ server
                            # self.get_cameras()
                    else:
                        group_model_manager.add_group_list(controller=self, group_list=data_list)
                    return data_list
                else:
                    # do BE không trả được DTO của Group khi update AiFlow lên cần case get ids của group để xử lý riêng AiFlow
                    data = response.json()
                    data_list = []
                    for item in data:
                        item["server_ip"] = self.server.data.server_ip
                        group_model:GroupModel = group_model_manager.get_group_model(id=item["id"])
                        if group_model is not None:
                            group_model.diff_group_model(group=item)
                            data_list.append(item)
                    return data_list
            else:
                pass
                # if self.show_message_dialog:
                #     self.show_message_dialog(response)
        return None
    
    def get_stream_url_thread(self, cameraId=None, streamIndex=None, callback=None):
        """Get stream URL in background thread to avoid UI blocking"""
        def async_get_stream_url():
            response, returned_stream_index = self.api_client.get_stream_url(cameraId=cameraId, streamIndex=streamIndex)
            if response is not None:
                if response.status_code == HTTPStatusCode.OK.value:
                    if callback is not None:
                        callback(response, returned_stream_index)
                    return response
            return None

        # 🔧 FIX: Run in thread to prevent UI blocking
        import threading
        threading.Thread(target=async_get_stream_url, daemon=True).start()

    def get_stream_url(self,cameraId = None):
        self.executor.submit(self.get_stream_url_thread, cameraId)
        # threading.Thread(target=self.get_stream_url_thread,args=(cameraId,)).start()
        pass

    def get_ai_stream_url_thread(self,aiFlowId = None,callback = None):
        # logger.debug(f'get_ai_stream_url_thread = {aiFlowId}')
        response = self.api_client.get_ai_stream_url(aiFlowId=aiFlowId)
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                logger.debug(f'get_ai_stream_url_thread = {response.text}')
                # camera_model:CameraModel = camera_model_manager.get_camera_model(id = cameraId)
                # camera_model.add_rtmp_signal.emit(response.text)
                if callback is not None:
                    callback(response)
                return response
        return None

    def get_ai_stream_url(self,aiFlowId = None):
        self.executor.submit(self.get_ai_stream_url_thread, aiFlowId)
        # threading.Thread(target=self.get_stream_url_thread,args=(cameraId,)).start()
        pass

    def get_camera(self, ids=None, name=None, status=True, urlMainstream=None, cameraGroupIds=None):
        response = self.api_client.get_camera(
            ids=ids)
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                data = response.json()
                for item in data:
                    camera = CameraModel(data=item)
                    return camera
        return None

    def get_camera_groups(self, id=None, name=None, description=None, cameraNames=None, childGroupNames=None, parentGroupNames=None):
        response = self.api_client.get_groups(id=id, name=name, description=description,
                                              cameraNames=cameraNames, childGroupNames=childGroupNames, parentGroupNames=parentGroupNames)
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                data = response.json()
                data_list = []
                for item in data:
                    group = GroupModel(data=item)
                    data_list.append(group)
                group_list = group_model_manager.get_group_list()
                if group_list:
                    if len(group_list) == len(data_list):
                        # sử dụng get_camera_groups() trong trường hợp update group
                        group_model_manager.update_group_list(controller=self,group_list=data_list)
                    else:
                        # sử dụng get_camera_groups trong trường hợp delete group
                        group_model_manager.find_group_deleted(group_list=data_list)
                        # sau khi xóa Group thì cần cập nhật camera từ server
                        self.get_cameras()
                else:
                    group_model_manager.add_group_list(controller = self,group_list=data_list)
                return data_list
            else:
                if self.show_message_dialog:
                    self.show_message_dialog(response)
        else:
            logger.debug(f"response get_camera_groups= {response.text}")
        return None

    def create_camera_group(self, parent=None, data: Group = None, is_insert_group=False):
        # Create a partial function with additional arguments
        partial_callback = functools.partial(self.callback_create_camera_group, is_insert_group=is_insert_group)
        self.api_client.callback_create_group(
            parent=parent, data=data.to_dict(), callback=partial_callback)

    def callback_create_camera_group(self, response, is_insert_group=False):
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                data = response.json()
                group_model = GroupModel(data=data)
                group_model.set_property("server_ip",self.server.data.server_ip)
                group_model_manager.add_group(group=group_model)
                if group_model.get_property("cameraIds") is not None:
                    if len(group_model.get_property("cameraIds")) > 0:
                        self.get_cameras()

                listen_show_notification.listen_API_success_message_signal.emit(
                    (response, TypeRequestVMS.CREATE_CAMERA_GROUP, None))
                return data
            else:
                json_res = response.json()
                error_data = json_res['data']
                listen_show_notification.listen_API_fail_message_signal.emit(
                    (response, TypeRequestVMS.CREATE_CAMERA_GROUP, error_data[0]))
        else:
            error_message = 'SERVER_DISCONNECTED'
            listen_show_notification.listen_API_fail_message_signal.emit(
                (response, TypeRequestVMS.CREATE_CAMERA_GROUP, error_message))
        return None

    def update_camera_group_thread_py_put(self, data: Group = None):
        response = self.api_client.update_group_py_put(data=data)
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                data = response.json()
                group_model:GroupModel = group_model_manager.get_group_model(id = data["id"])
                diff = []
                if group_model is not None:
                    diff = group_model.diff_group_model(group = data)
                self.get_cameras()
                listen_show_notification.listen_API_success_message_signal.emit((response, TypeRequestVMS.UPDATE_CAMERA_GROUP, None))
                return data
            else:
                # response, type_request, error_data
                json_res = response.json()
                error_data = json_res['data']
                listen_show_notification.listen_API_fail_message_signal.emit((response, TypeRequestVMS.UPDATE_CAMERA_GROUP, error_data[0]))
        else:
            # loi server
            error_message = 'SERVER_DISCONNECTED'
            listen_show_notification.listen_API_fail_message_signal.emit(
                (response, TypeRequestVMS.CREATE_CAMERA_GROUP, error_message))
        return None

    def update_camera_group_by_put(self, data:dict = None):
        threading.Thread(
            target=self.update_camera_group_thread_py_put, args=(data,)).start()

    def delete_camera_group_thread(self, data: dict = None):
        pass
        response = self.api_client.delete_group(
            id=data["id"], names=data["name"], descriptions=data["description"])
        if response is not None:
            if response.status_code == HTTPStatusCode.NO_CONTENT.value or response.status_code == HTTPStatusCode.OK.value:
                self.get_groups()
                self.get_cameras()
                listen_show_notification.listen_API_success_message_signal.emit(
                    (response, TypeRequestVMS.DELETE_CAMERA_GROUP, None))
            else:
                json_res = response.json()
                error_data = json_res['data']
                listen_show_notification.listen_API_fail_message_signal.emit(
                    (response, TypeRequestVMS.DELETE_CAMERA_GROUP, error_data[0]))

    def delete_camera_group(self, data: dict = None):
        threading.Thread(target=self.delete_camera_group_thread,
                         args=(data,)).start()

    def create_aiflows(self, data: AiFlow):
        response = self.api_client.create_aiflows(data=data.to_dict())
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                data = AiFlow.from_dict(response.json())
                data.server_ip = self.server.data.server_ip
                aiflow_model_manager.add_aiflow(controller=self,aiflow = AiFlowModel(aiflow=data))
                if data.cameraId is not None and len(data.cameraId) > 0:
                    camera_model:CameraModel = camera_model_manager.get_camera_model(id=data.cameraId)
                    # xử lý việc add AIFlow vào ButtonAiflowCamera khi tạo AIflow mới
                    # camera_model.add_ai_flow.emit((data.id,camera_model))
                    camera = Camera.from_dict(data.cameraDTO)
                    # xử lý việc cập nhật thông tin số lượng bài AI lên CameraItem
                    camera_model.diff_camera_model(camera = response.json())
                # xử lý việc cập nhật thông tin bật tắt AI của group chứa camera này
                if data.cameraDTO is not None:
                    for group_id in data.cameraDTO['cameraGroupIds']:
                        threading.Thread(target=self.get_groups,args=(group_id,)).start()
                listen_show_notification.listen_API_success_message_signal.emit(
                    (response, TypeRequestVMS.CREATE_AIFLOWS, None))
                return data
            else:
                json_res = response.json()
                error_data = json_res['data']
                listen_show_notification.listen_API_fail_message_signal.emit(
                    (response, TypeRequestVMS.CREATE_AIFLOWS, error_data[0]))

        return None

    def apply_ai_flow(self, data = None):
        def func(data):
            response = self.api_client.apply_ai_flow(data)
            if response is not None:
                if response.status_code == HTTPStatusCode.OK.value:
                    pass
                    group:dict = response.json()
                    group["server_ip"] = self.server.data.server_ip

                    logger.debug(f"Success callback_apply_ai_flow")
                    group_model:GroupModel = group_model_manager.get_group_model(id = group["id"])
                    if group_model is not None:
                        # xử lý cập nhật trạng thái bật tắt AI trên Group/AIBox
                        group_model.diff_group_model(group = group)
                    # xử lý cập nhật trạng thái bật tắt tắt từng bài AI trên camera
                    threading.Thread(target=self.get_aiflows).start()
                    # self.get_aiflows()
                    # xử lý cập nhật số lượng bài human/vehicle đang được bật
                    if group_model is not None:
                        for item in group_model.get_property("cameraDTOs"):
                            camera_model:CameraModel = camera_model_manager.get_camera_model(id = item["id"])
                            if camera_model is not None:
                                camera_model.diff_camera_model(camera=item)
                    listen_show_notification.listen_API_success_message_signal.emit(
                        (response, TypeRequestVMS.UPDATE_AIFLOWS, None))
                else:
                    json_res = response.json()
                    error_data = json_res.get("data",None)
                    logger.debug(f'error_data = {error_data}')
                    if error_data is not None and len(error_data) > 0:
                        listen_show_notification.listen_API_fail_message_signal.emit(
                            (response, TypeRequestVMS.UPDATE_AIFLOWS, error_data[0]))
                    else:
                        listen_show_notification.listen_API_fail_message_signal.emit(
                            (response, TypeRequestVMS.UPDATE_AIFLOWS, 'SERVER_DISCONNECTED'))

            else:
                logger.debug(f"Fail callback_apply_ai_flow = {response}")

        threading.Thread(
            target=func, args=(data,)).start()

    def apply_ai_flow_camera(self, data = None):
        def func(data):
            response = self.api_client.apply_ai_flow_camera(data)
            if response is not None:
                logger.debug(f'apply_ai_flow_camera = {response}')
                if response.status_code == HTTPStatusCode.OK.value:
                    logger.debug(f'response.text: {response.text}')
                    if response.text == '':
                        listen_show_notification.listen_API_success_message_signal.emit(
                            (response, TypeRequestVMS.WARNING_AIFLOWS, None))
                    else: 
                        camera = response.json()
                        camera_model:CameraModel = camera_model_manager.get_camera_model(id = camera["id"])
                        if camera_model is not None:
                            camera_model.diff_camera_model(camera=camera)
                            for item in camera_model.get_property("cameraGroupDTOList"):
                                group_model:GroupModel = group_model_manager.get_group_model(id = item["id"])
                                group_model.diff_group_model(group = item)
                        listen_show_notification.listen_API_success_message_signal.emit(
                            (response, TypeRequestVMS.UPDATE_AIFLOWS, None))
                else:
                    json_res = response.json()
                    logger.debug(f'json_res = {json_res}')
                    error_data = json_res['data']
                    logger.debug(f'error_data = {error_data}')
                    listen_show_notification.listen_API_fail_message_signal.emit(
                        (response, TypeRequestVMS.UPDATE_AIFLOWS, error_data[0]))

            else:
                logger.debug(f"Fail callback_apply_ai_flow_camera = {response}")

        threading.Thread(
            target=func, args=(data,)).start()

    def get_aiflows(self,id=None, cameraIds=None,cameraGroupIds = None):
        response = self.api_client.get_aiflows(id=id, cameraIds=cameraIds,cameraGroupIds = cameraGroupIds)
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                data = response.json()
                data_list = []
                for index, item in enumerate(data):
                    aiflow = AiFlow.from_dict(item)
                    aiflow.server_ip = self.server.data.server_ip
                    aiflow_model = AiFlowModel(aiflow=aiflow)
                    data_list.append(aiflow_model)
                aiflows = aiflow_model_manager.get_aiflows(server_ip=self.server.data.server_ip)
                if aiflows:
                    if len(aiflows) == len(data_list):
                        aiflow_model_manager.update_aiflows(aiflows=data_list)
                    elif len(aiflows) > len(data_list):
                        pass
                        # sử dụng get_cameras() trong trường hợp delete camera
                        # camera_model_manager.find_camera_deleted(controller = self,camera_list=data_list)
                        # sau khi xóa Camera thì cần cập nhật Group từ server
                        # self.get_cameras()

                    else:
                        pass
                        # sử dụng get_cameras() trong trường hợp add camera
                        # su dung trong websocket
                        # camera_list_added:CameraModel = camera_model_manager.find_camera_added(camera_list=data_list)
                        # is_cameraGroupIds = False
                        # camera_list = []
                        # for camera in camera_list_added:
                        #     if camera.data.cameraGroupIds is not None and len(camera.data.cameraGroupIds) > 0:
                        #         is_cameraGroupIds = True
                        # if is_cameraGroupIds:
                        #     self.get_groups()

                else:
                    # sử dụng get_cameras() trong trường hợp init app
                    aiflow_model_manager.add_aiflows(aiflows=data_list,controller=self)
                return data_list
            else:
                return None
        return None

    def get_ai_flow_and_type(self, camera_id, type, callback = None):
        logger.debug(f"get_ai_flow_and_type = {camera_id} - {type}")
        threading.Thread(
            target=self.get_ai_flow_and_type_thread, args=(camera_id, type, callback)).start()
        
    def get_ai_flow_and_type_thread(self, camera_id, type, callback = None):
        response = self.api_client.get_ai_flow_and_type(cameraId = camera_id,type=type)
        logger.debug(f"get_ai_flow_and_type_thread = {response}")
        if response is not None:
            logger.debug(f"get_ai_flow_and_type_thread = {response.status_code} - {response.json()}")
            data = AiFlow.from_dict(response.json())
            data.server_ip = self.server.data.server_ip
            aiflow_model = AiFlowModel(aiflow=data)
            aiflow_model_manager.add_aiflow(controller=self,aiflow = aiflow_model)
            if data.cameraId is not None and len(data.cameraId) > 0:
                camera_model:CameraModel = camera_model_manager.get_camera_model(id=data.cameraId)
                # xử lý việc add AIFlow vào ButtonAiflowCamera khi tạo AIflow mới
                # camera_model.add_ai_flow.emit((data.id,camera_model))
                camera = data.cameraDTO
                # xử lý việc cập nhật thông tin số lượng bài AI lên CameraItem
                camera_model.diff_camera_model(camera = data.cameraDTO)
            if aiflow_model.data.cameraDTO is not None:
                for group_id in aiflow_model.data.cameraDTO['cameraGroupIds']:
                    threading.Thread(target=self.get_groups,args=(group_id,)).start()
            if callback is not None:
                callback(aiflow_model)
            return aiflow_model
        else:
            logger.debug("update_camera_thread_by_put error")
        return None

    def get_videos(self,parent = None, ids = None, dateFrom = None, dateTo = None,cameraIds = None,detail = False, callback = None):  
        self.api_client.get_videos_thread(parent=parent, ids=ids,dateFrom= dateFrom,dateTo=dateTo,cameraIds=cameraIds,detail=detail,callback=callback)
    
    def get_video_encoder_configurations(self, index = 0, cameraId = None):
        response = self.api_client.get_video_encoder_configurations(index=index, cameraId=cameraId)
        if response is not None:
            logger.debug(f"get_video_encoder_configurations = {response.json()}")
            return response.json()
        return None
    
    def get_recording_schedule(self, ids = None, cameraIds = None):
        response = self.api_client.get_recording_schedule(ids=ids, cameraIds=cameraIds)
        if response is not None:
            try:
                schedule_list = response.json()
                schedule_manager.add_schedule_list(schedule_list)
                return schedule_list
            except Exception as e:
                logger.error(f"get_recording_schedule = {e}")
        return None
        
    def create_recording_schedule(self, data = None):
        response = self.api_client.create_recording_schedule(data=data)
        if response is not None:
            logger.debug(f"create_recording_schedule = {response.json()}")
            schedule_manager.add_schedule(response.json())
            

    def start_subcribe(self, data = None):
        response = self.api_client.start_subcribe(data=data)
        if response is not None:
            return response

    def update_recording_schedule_by_patch(self, data = None):
        response = self.api_client.update_recording_schedule_by_patch(data=data)
        if response is not None:
            logger.debug(f"update_recording_schedule_by_patch = {response.json()}")
            schedule_manager.add_schedule(response.json())
            return response.json()

    def get_video_from_id(self, ids = None):
        response, dateFrom, dateTo = data = self.api_client.get_videos(ids=ids, dateFrom=None, dateTo=None, cameraIds=None, detail=False)
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                data = response.json()
                data_list = []
                for index, item in enumerate(data):
                    video = Video.from_dict(item)
                    data_list.append(video)
                return data_list
            else:
                if self.show_message_dialog:
                    self.show_message_dialog(response)
        return None
    
    def get_thread_video_from_id(self, parent, ids, callback):
        thread = APIThread(
            parent=parent, target=self.get_video_from_id, callback=callback, args=(ids,))
        thread.start()

    def callback_get_videos(self, data):
        response,dateFrom,dateTo = data
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                logger.debug(f'callback_get_videos = {response.json()}')
                data = response.json()
                record_model_manager.add_videos(video_list = data,dateFrom = dateFrom,dateTo = dateTo)
                return data
        record_model_manager.add_videos(video_list = [],dateFrom = dateFrom,dateTo = dateTo)
        return None

    def get_video_encoder_configurations(self, index = 0, cameraId = None):
        response = self.api_client.get_video_encoder_configurations(index=index, cameraId=cameraId)
        if response is not None:
            logger.debug(f"get_video_encoder_configurations = {response.json()}")
            return response.json()
        return None

    def get_recording_schedule(self, ids = None, cameraIds = None):
        response = self.api_client.get_recording_schedule(ids=ids, cameraIds=cameraIds)
        if response is not None:
            # logger.debug(f"get_recording_schedule = {response.json()}")
            return response.json()
        return None

    def create_recording_schedule(self, data = None):
        response = self.api_client.create_recording_schedule(data=data)
        if response is not None:
            logger.debug(f"get_recording_schedule = {response.json()}")

    def start_subcribe(self, data = None):
        response = self.api_client.start_subcribe(data=data)
        if response is not None:
            return response

    def update_recording_schedule_by_patch(self, data = None):
        response = self.api_client.update_recording_schedule_by_patch(data=data)
        if response is not None:
            pass
            # logger.debug(f"update_recording_schedule_by_patch = {response.json()}")

    def get_video_from_id(self, ids = None):
        response, dateFrom, dateTo = data = self.api_client.get_videos(ids=ids, dateFrom=None, dateTo=None, cameraIds=None, detail=False)
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                data = response.json()
                data_list = []
                for index, item in enumerate(data):
                    video = RecordModel.from_dict(item)
                    data_list.append(video)
                return data_list
            else:
                if self.show_message_dialog:
                    self.show_message_dialog(response)
        return None

    def get_thread_video_from_id(self, parent, ids, callback):
        thread = APIThread(
            parent=parent, target=self.get_video_from_id, callback=callback, args=(ids,))
        thread.start()

    def update_aiflows(self, data: AiFlow):
        def func(data):
            start_time = time.time()
            response = self.api_client.update_aiflows(data=data.to_dict())
            if response is not None:
                if response.status_code == HTTPStatusCode.OK.value:
                    data = AiFlow.from_dict(response.json())
                    aiflow_model:AiFlowModel= aiflow_model_manager.get_aiflow_model(id = data.id)
                    if aiflow_model is not None:
                        # xử lý việc cập nhật thông tin bật tắt AI trên camera chứa AiFlow này
                        aiflow_model.diff_aiflow_model(aiflow=data)
                        # xử lý việc cập nhật thông tin số bài toán Ai trên camera chứa AiFlow này
                        if aiflow_model.data.cameraDTO is not None:
                            camera = aiflow_model.data.cameraDTO
                            camera_model:CameraModel = camera_model_manager.get_camera_model(id = camera.id)
                            if camera_model is not None:
                                camera_model.diff_camera_model(camera=aiflow_model.data.cameraDTO)
                    # xử lý việc cập nhật thông tin bật tắt AI của group chứa camera này
                    if aiflow_model.data.cameraDTO is not None:
                        for group_id in aiflow_model.data.cameraDTO['cameraGroupIds']:
                            threading.Thread(target=self.get_groups,args=(group_id,)).start()
                    end_time = time.time()
                    listen_show_notification.listen_API_success_message_signal.emit(
                        (response, TypeRequestVMS.UPDATE_AIFLOWS, None))
                    return data
                else:
                    json_res = response.json()
                    error_data = json_res['data']
                    listen_show_notification.listen_API_fail_message_signal.emit(
                        (response, TypeRequestVMS.UPDATE_AIFLOWS, error_data[0]))
        threading.Thread(target=func,args=(data,)).start()

    def delete_aiflows(self, data: AiFlow):
        if data is None:
            return
        response = self.api_client.delete_aiflows(ids=[data.id])
        if response is not None:
            if response.status_code == HTTPStatusCode.NO_CONTENT.value or response.status_code == HTTPStatusCode.OK.value:
                listen_show_notification.listen_API_success_message_signal.emit(
                    (response, TypeRequestVMS.DELETE_AIFLOWS, None))
                return data
            else:
                json_res = response.json()
                error_data = json_res['data']
                listen_show_notification.listen_API_fail_message_signal.emit(
                    (response, TypeRequestVMS.DELETE_AIFLOWS, error_data[0]))

        return None

    def get_length_list_group(self):
        if self.list_groups is None:
            return 0
        return len(self.list_groups)


    def get_camera_model_in_group(self, group_id):
        response = self.api_client.get_all_camera_in_group(group_id)
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                data = response.json()
                list_camera_in_group = []
                for item in data:
                    camera = CameraModel(data=item)
                    list_camera_in_group.append(camera)
                self.list_camera_in_group = list_camera_in_group
                return list_camera_in_group
            else:
                if self.show_message_dialog:
                    self.show_message_dialog(response)
        return None

    def get_events(self, parent=None, dateFrom=None, dateTo=None, page=None, size=None, cameraId=None, cameraIds=None, groupId=None,groupCameraIds = None,name = None,isWarningConfig = 0, type = None,status = None):

        self.api_client.get_events_callback(parent=parent, dateFrom=dateFrom, dateTo=dateTo, page=page,
                                            size=size, cameraId=cameraId, cameraIds=cameraIds, groupId=groupId,groupCameraIds=groupCameraIds,name=name,isWarningConfig = isWarningConfig,type = type, status = status, callback=self.callback_events)

    def callback_events(self, response):
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                data = response.json()
                events = EventData.from_dict(data)
                event_manager.add_event_list(events)

    def get_zones(self, ids: List[str] = None, type=None, aiFlowIds=None):
        response = self.api_client.get_zones(
            ids=ids, type=type, aiFlowIds=aiFlowIds)
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                data = response.json()
                return data
            else:
                return response
        return None

    def create_zones(self, data, parent, callback):
        APIThread(parent=parent, target=self.api_client.create_zones, args=(data,), callback=callback).start()

    def update_zones(self, data, parent, callback):
        APIThread(parent=parent, target=self.api_client.update_zones, args=(data,), callback=callback).start()

    def delete_zones(self, id=None):
        response = self.api_client.delete_zones(id=id)
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value or response.status_code == HTTPStatusCode.NO_CONTENT.value:
                return response
        return None

    # create threading gc.collect
    def gc_collect(self, parent):
        # use QThread
        WorkerThread(parent=parent, target=self.gc_collect_thread).start()

    def gc_collect_thread(self):
        gc.collect()


    def check_cuda_availability(self) -> bool:
        # state = False
        # try:
        #     pynvml.nvmlInit()
        #     # Get the number of available GPUs
        #     device_count = pynvml.nvmlDeviceGetCount()

        #     if device_count > 0:
        #         print(f"CUDA is available on your system. {device_count} GPU(s) found.")
        #         for i in range(device_count):
        #             handle = pynvml.nvmlDeviceGetHandleByIndex(i)
        #             name = pynvml.nvmlDeviceGetName(handle)
        #             print(f"GPU {i + 1}: {name}")
        #         state = True
        #     else:
        #         print("CUDA is not available on your system.")
        #         state = False
        #     pynvml.nvmlShutdown()
        # except Exception as e:
        #     print(f"Error checking CUDA availability: {e}")
        #     state = False
        # finally:
        #     self.has_cuda_available = state
        #     logger.debug(f'check_cuda_availability = {state}')
        # return state
        pass

    def search_camera(self, parent=None, data=None):
        logger.debug(f"search_camera = {data}")
        self.id_response_search_camera = None
        self.api_client.search_camera_thread(parent=parent, data=data, callback=self.callback_search_camera)

    def callback_search_camera(self, response):
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                data = response.json()
                self.id_event_search_camera = data['id']
                logger.debug(f'callback_search_camera {data}')
                return data
            else:
                logger.debug(f'callback_search_camera1 {response.text}')
                # listen_show_notification.signal_listen_message.emit((response, None, None))
        else:
            logger.debug(f'callback_search_camera2 {response}')
        return None

    # API Onvif
    def get_onvif_profiles(self,parent = None, cameraId = None):
        thread = APIThread(
            parent=parent, target=self.api_client.get_onvif_profiles, callback=self.callback_get_onvif_profiles, args=(cameraId,))
        thread.start()

    def callback_get_onvif_profiles(self, response):
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                data = response.json()
                logger.debug(f'callback_get_profiles = {data}')

        else:
            logger.debug(f"response = {response}")

    def ptz_get_status(self,parent = None, cameraId = None):
        response = self.api_client.ptz_get_status(cameraId=cameraId)
        if response is not None:
            return response['data']['PTZStatus']

        else:
            return None

    def get_onvif_stream_url(self,parent = None, cameraId = None):
        thread = APIThread(
            parent=parent, target=self.api_client.get_onvif_stream_url, callback=self.callback_get_onvif_stream_url, args=(cameraId,))
        thread.start()

    def callback_get_onvif_stream_url(self, response):
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                logger.debug(f'callback_get_onvif_stream_url = {response.text}')
        else:
            logger.debug(f"response = {response}")

    def ptz_relative_move(self,parent = None, cameraId = None,x = 0, y = 0, zoom = 0):
        response = self.api_client.ptz_relative_move(cameraId = cameraId,x = x, y = y,zoom = zoom)
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                data = response.json()
                print("dât ptz", data)

        else:
            logger.debug(f"response = {response}")

    def set_drag_to_zoom(self, parent=None, cameraId=None, start_point: QPoint=None, end_point: QPoint=None, width=None, height=None, calib_data=None):
        def process():
            # Fallback: if calib_data is None, get default by manufacturer
            if calib_data is None:
                # Try to get camera model by cameraId
                from src.common.model.camera_model import camera_model_manager
                camera_model = camera_model_manager.get_camera_model(id=cameraId)
                manufacturer = None
                if camera_model is not None:
                    # Try to get manufacturer from camera_model
                    manufacturer = getattr(camera_model, 'manufacturer', None)
                    if not manufacturer:
                        # Try from data dict
                        manufacturer = getattr(getattr(camera_model, 'data', None), 'cameraBranch', None)
                if not manufacturer:
                    manufacturer = 'Dahua'  # Default fallback
                from src.common.onvif_api.calib_data import Manufacturer, CalibData
                # Map string to Manufacturer enum if needed
                manufacturer_enum = getattr(Manufacturer, manufacturer, Manufacturer.Invalid)
                calib_data_local = CalibData.get_data(type=manufacturer_enum)
            else:
                calib_data_local = calib_data

            current_status = self.ptz_get_status(cameraId=cameraId)
            coordinate = CalibData.get_coordinate_drag_to_zoom(
                current_status=current_status,
                start_point=start_point,
                end_point=end_point,
                width=width,
                height=height,
                calib_data=calib_data_local
            )
            try:
                self.ptz_relative_move(cameraId=cameraId, x=coordinate['pan'], y=coordinate['tilt'], zoom=coordinate['zoom'])
            except Exception as e:
                logger.debug(f'set_drag_to_zoom error: {e}')
        thread = APIThread(parent=parent, target=process)
        thread.start()

    def ptz_stop(self, cameraId = None):
        threading.Thread(target=self.api_client.ptz_stop,args=(cameraId,)).start()

    def ptz_continuous_move(self,parent = None,cameraId = None,x = 0,y = 0,zoom = 0):
        threading.Thread(target=self.api_client.ptz_continuous_move,args=(cameraId,x,y,zoom,)).start()

    # API Virtual window
    def get_tabmodel(self,parent = None, ids = None,names = None,types = None,currentGrids = None,listGridIds = None,listGridCustomDatas = None):
        response = self.api_client.get_tabmodel(ids = ids,names = names,types = types,currentGrids = currentGrids,listGridIds = listGridIds,listGridCustomDatas = listGridCustomDatas)
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                data = response.json()
                data_list = []
                for item in data:
                    listGridCustomData = item.get("listGridCustomData", None)
                    if listGridCustomData is not None and listGridCustomData == "NewGrid":
                        item["server_ip"] = self.server.data.server_ip
                        data_list.append(item)
                if len(data_list) > 0:
                    gridManager.addGridModelListChanged.emit((self,data_list))
        else:
            logger.debug(f"response get_tabmodel= {response}")

    def delete_tabmodel(self,parent = None, ids = None,names = None,types = None):
        delete_tabmodel_thread = APIThread(
            parent=parent, target=self.api_client.delete_tabmodel, callback=self.callback_delete_tabmodel, args=(ids, names, types))
        delete_tabmodel_thread.start()

    def callback_delete_tabmodel(self, response):
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                logger.debug(f"Success delete_tabmodel")
        else:
            logger.debug(f"response = {response}")

    def create_tabmodel(self, tab = None):
        response = self.api_client.create_tabmodel(tab=tab)
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                return response.json()
        else:
            logger.debug(f"response = {response}")
        return None
        # create_tabmodel_thread = APIThread(
        #     parent=parent, target=self.api_client.create_tabmodel, callback=self.callback_create_tabmodel, args=(tab,))
        # create_tabmodel_thread.start()

    def callback_create_tabmodel(self, response):
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                pass
        else:
            logger.debug(f"response = {response}")

    def update_tabmodel_by_put(self,tab = None):
        logger.debug(f'update_tabmodel_by_put = {tab}')
        response = self.api_client.update_tabmodel_by_put(tab=tab)
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                return response.json()
        else:
            logger.debug(f"response = {response}")
        return None
    
        # update_tabmodel_thread = APIThread(
        #     parent=parent, target=self.api_client.update_tabmodel_by_put, callback=self.callback_update_tabmodel, args=(tab,))
        # update_tabmodel_thread.start()

    def callback_update_tabmodel(self, response):
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                pass
        else:
            logger.debug(f"response = {response}")

    def get_shortcut_key(self,parent = None, ids = None,startKeys = None,shortcutIds = None,names = None,treeTypes = None):
        response = self.api_client.get_shortcut_key(ids = ids,startKeys = startKeys,shortcutIds = shortcutIds,names = names,treeTypes = treeTypes)
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                data = response.json()
                data_list = []
                for item in data:
                    shortcut_key = ShortCutKey.from_dict(item)
                    data_list.append(ShortCutKeyModel(shortcut_key = shortcut_key))
                if len(data_list) > 0:
                    shortcut_key_model_manager.add_shortcut_key_list(shortcut_key_list = data_list)
        else:
            logger.debug(f"response = {response}")

    def callback_shortcut_key(self, response):
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                data = response.json()
                for item in data:
                    shortcut_key = ShortCutKey.from_dict(item)
        else:
            logger.debug(f"response = {response}")

    def delete_shortcut_key(self,parent = None, ids = None,startKeys = None,shortcutIds = None,names = None,treeTypes = None):
        delete_shortcut_key_thread = APIThread(
            parent=parent, target=self.api_client.delete_shortcut_key, callback=self.callback_delete_shortcut_key, args=(ids, startKeys, shortcutIds,names,treeTypes,))
        delete_shortcut_key_thread.start()

    def callback_delete_shortcut_key(self, response):
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                listen_show_notification.listen_API_success_message_signal.emit(
                    (response, TypeRequestVMS.DELETE_SHORTCUTID, None))
            else:
                json_res = response.json()
                error_data = json_res['data']
                listen_show_notification.listen_API_fail_message_signal.emit(
                    (response, TypeRequestVMS.DELETE_SHORTCUTID, error_data[0]))
        else:
            # loi server
            error_message = 'SERVER_DISCONNECTED'
            listen_show_notification.listen_API_fail_message_signal.emit(
                (response, TypeRequestVMS.DELETE_SHORTCUTID, error_message))

    def create_shortcut_key(self,parent = None, shortcut_key = None):
        create_shortcut_key_thread = APIThread(
            parent=parent, target=self.api_client.create_shortcut_key, callback=self.callback_create_shortcut_key, args=(shortcut_key.to_dict(),))
        create_shortcut_key_thread.start()

    def callback_create_shortcut_key(self, response):
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                shortcut_key = ShortCutKey.from_dict(response.json())
                shortcut_key_model = ShortCutKeyModel(shortcut_key=shortcut_key)
                shortcut_key_model_manager.add_shortcut_key(shortcut_key=shortcut_key_model)
                listen_show_notification.listen_API_success_message_signal.emit(
                    (response, TypeRequestVMS.CREATE_SHORTCUTID, None))
            else:
                json_res = response.json()
                error_data = json_res['data']
                listen_show_notification.listen_API_fail_message_signal.emit(
                    (response, TypeRequestVMS.CREATE_SHORTCUTID, error_data[0]))
        else:
            # loi server
            error_message = 'SERVER_DISCONNECTED'
            listen_show_notification.listen_API_fail_message_signal.emit(
                (response, TypeRequestVMS.CREATE_SHORTCUTID, error_message))

    def update_shortcut_key_by_put(self,parent = None, shortcut_key = None):
        update_shortcut_key_thread = APIThread(
            parent=parent, target=self.api_client.update_shortcut_key_by_put, callback=self.callback_update_shortcut_key, args=(shortcut_key.to_dict(),))
        update_shortcut_key_thread.start()

    def callback_update_shortcut_key(self, response):
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                listen_show_notification.listen_API_success_message_signal.emit(
                    (response, TypeRequestVMS.UPDATE_SHORTCUTID, None))
            else:
                json_res = response.json()
                error_data = json_res['data']
                listen_show_notification.listen_API_fail_message_signal.emit(
                    (response, TypeRequestVMS.UPDATE_SHORTCUTID, error_data[0]))
        else:
            error_message = 'SERVER_DISCONNECTED'
            listen_show_notification.listen_API_fail_message_signal.emit(
                (response, TypeRequestVMS.UPDATE_SHORTCUTID, error_message))
    def get_data_tree(self):
        result = self.get_cameras()
        result = self.get_floors()
        # time.sleep(1)
        result = self.getBuildings()
        result = self.get_map()

    def get_map(self):
        response = self.api_client.get_map()
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                data = response.json()
                map_manager.addMapList.emit((data,self))
                return data
        else:
            logger.debug(f"response = {response}") 
        return None
    
    def add_camera_to_map(self,id = None,ids = None):
        response = self.api_client.add_camera_to_map(id = id,ids = ids)
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                data = response.json()
                return data
        else:
            logger.debug(f"response = {response}") 

    def remove_camera_from_map(self,id = None,ids = None):
        response = self.api_client.remove_camera_from_map(id = id,ids = ids)
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                data = response.json()
                return data
        else:
            logger.debug(f"response = {response}") 

    def add_building_to_map(self,id = None,ids = None, callback = None):
        response = self.api_client.add_building_to_map(id = id,ids = ids)
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                data = response.json()
                return data
        else:
            logger.debug(f"response = {response}") 

    def update_map_by_patch(self,id = None,cameraIds = None,buildingIds = None,callback = None):
        logger.debug(f'update_map_by_patch = {buildingIds}')
        response = self.api_client.update_map_by_patch(id = id,cameraIds = cameraIds,buildingIds = buildingIds)
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                data1 = response.json()
                logger.debug(f'update_map_by_patch1 = {data1["buildingIds"]}')
                callback(data1)
        else:
            logger.debug(f"response = {response}") 

    def delete_floor(self,ids = None):
        response = self.api_client.delete_floor(ids = ids)
        if response is not None:
            if response.status_code == 204:
                return True
                # logger.debug(f'delete_building = {response.json()}')
            else:
                logger.info(f'delete_floor error')
                return False

        else:
            logger.debug(f"response = {response}")  
        return False
    
    def getBuildings(self,ids = None,names = None):
        response = self.api_client.getBuildings(ids = ids,names = names)
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                data = response.json()
                building_manager.addBuildingList.emit((data,self))
                return data
        else:
            logger.debug(f"response = {response}")  
        return None
    
    def create_building(self,data = None):
        response = self.api_client.create_building(data = data)
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                data = response.json()
                return data

        else:
            logger.debug(f"response = {response}") 
        return None
    
    def update_building_by_put(self,data = None):
        logger.debug(f'update_building_by_put = {data}')
        response = self.api_client.update_building_by_put(data = data)
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                return response.json()
        else:
            logger.debug(f"response = {response}")  

    def update_building_by_patch(self,data = None):
        response = self.api_client.update_building_by_patch(data = data)
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                return response.json()
        else:
            logger.debug(f"response = {response}") 

    def delete_building(self,ids = None):
        response = self.api_client.delete_building(ids = ids)
        if response is not None:
            if response.status_code == 204:
                return True
                # logger.debug(f'delete_building = {response.json()}')

        else:
            logger.debug(f"response = {response}")  
        return False
    
    def add_floor_to_building(self,id = None,ids = None):
        response = self.api_client.add_floor_to_building(id = id,ids = ids)
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                data = response.json()
                return data
        else:
            logger.debug(f"response = {response}") 
        return None
    
    def get_floors(self,ids = None,names = None,fileLinks = None,cameraIds = None, buildingIds = None):
        response = self.api_client.get_floors(ids = ids,names = names,fileLinks = fileLinks,cameraIds = cameraIds, buildingIds = buildingIds)
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                data = response.json()
                floor_manager.addFloorList.emit((data,self))
                return data
        else:
            logger.debug(f"response = {response}")  
        return None
    
    def create_floor(self,data = None):
        response = self.api_client.create_floor(data = data)
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                data = response.json()
                # callback(data)
                return data
        else:
            logger.info(f"response = {response}") 
        return None
    
    def add_camera_on_floor(self,data = None):
        response = self.api_client.add_camera_on_floor(data = data)
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                data = response.json()
                return data
            else:
                return None
        else:
            logger.debug(f"response = {response}") 
            return None
    
    def remove_camera_on_floor(self,data = None):
        response = self.api_client.remove_camera_on_floor(data = data)
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                data = response.json()
                return data
        else:
            logger.debug(f"response = {response}") 

    def update_list_cameras_by_put(self,data = None):
        # logger.info(f'update_list_cameras_by_put = {data}')
        response = self.api_client.update_list_cameras_by_put(data = data)
        if response is not None:
            # logger.info(f'update_list_cameras_by_put = {response}')
            if response.status_code == HTTPStatusCode.OK.value:
                return response.json()
        else:
            logger.info(f"response error= {response}") 

    def update_file(self,filePath = None):
        response = self.api_client.update_file(filePath = filePath)
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                # data = response.json()
                # logger.debug(f'update_file = {response.text}')
                return response.text
        else:
            logger.debug(f"response = {response}")  
        return None
    
    # def add_camera_to_map(self,camera = None, id = None):
    #     # update_map_thread = APIThread(
    #     #     parent=parent, target=self.api_client.update_map_by_put, callback=self.callback_update_map, args=(map,))
    #     # update_map_thread.start()
    #     camera_qsettings.add_camera_to_floor(floor_id = id, camera_data = camera)

    def update_map_2d(self, camera_list = None, id = None):
        camera_qsettings.replace_camera_list_in_floor(floor_id = id, camera_data = camera_list)

    def callback_update_map(self, response):
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                logger.debug(f"Success callback_update_map")
        else:
            logger.debug(f"response = {response}")

    # Permissions
    def get_all_users(self):
        response = self.api_client.get_all_users()
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                data = response.json()
                data_list = []
                for item in data['data']['content']:
                    user = UserModel(user=Users.from_dict(item))
                    user.data.server_ip = self.server.data.server_ip
                    data_list.append(user)
                # Step 2: Parallelize fetching detailed role information
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future_to_role = {executor.submit(self.get_user_roles, user.data.id): user for user in
                                      data_list}
                    # Step 3: Wait for all futures to complete and update the roles
                    for future in concurrent.futures.as_completed(future_to_role):
                        user = future_to_role[future]
                        # try:
                        detailed_role = future.result()  # This is the detailed role information
                        self.update_user_with_user_roles(user, detailed_role)  # Update role with detailed data
                        # except Exception as exc:
                        #     print(f"Error fetching details for role {user.data.id}: {exc}")

                user_list = user_model_manager.get_user_list(server_ip=self.server.data.server_ip)
                if user_list:
                    if len(user_list) == len(data_list):
                        # sử dụng get_all_users() trong trường hợp update user
                        user_model_manager.update_user_list(controller=self, user_list=data_list)
                    elif len(user_list) > len(data_list):
                        # sử dụng get_all_users() trong trường hợp delete user
                        user_model_manager.find_user_deleted(controller=self, user_list=data_list)
                        # sau khi xóa Camera thì cần cập nhật Group từ server
                        # self.get_all_users()
                    else:
                        # sử dụng get_all_users() trong trường hợp add camera
                        # su dung trong websocket
                        user_list_added: UserModel = user_model_manager.find_user_added(user_list=data_list)
                        # is_cameraGroupIds = False
                        # for user in user_list_added:
                        #     if user.data.cameraGroupIds is not None and len(user.data.cameraGroupIds) > 0:
                        #         is_cameraGroupIds = True
                        # if is_cameraGroupIds:
                        #     self.get_groups()

                else:
                    # sử dụng get_all_users() trong trường hợp init app
                    # logger.debug(f'get_cameras2 = {camera_list}')
                    user_model_manager.add_user_list(user_list=data_list, controller=self)
                return data_list
            else:
                if self.show_message_dialog:
                    self.show_message_dialog(response)
        return None

    def get_user_roles(self, user_id):
        response = self.api_client.get_user_roles_api(user_id)
        if response is not None and response.status_code == HTTPStatusCode.OK.value:
            return response.json()

    def update_user_with_user_roles(self, user, detailed_data):
        if detailed_data is not None:
            list_value = detailed_data["data"]
            user.data.userRoles = [UserRole.from_dict(user_role) for user_role in list_value]

    def get_all_roles(self):
        response = self.api_client.get_all_roles()
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                data = response.json()
                data_list = []
                for item in data['data']['content']:
                    role = RoleModel(role=Role.from_dict(item))
                    role.data.server_ip = self.server.data.server_ip
                    data_list.append(role)

                # Step 2: Parallelize fetching detailed role information
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future_to_role = {executor.submit(self.get_all_infor_group, role.data.id): role for role in data_list}
                    # Step 3: Wait for all futures to complete and update the roles
                    for future in concurrent.futures.as_completed(future_to_role):
                        role = future_to_role[future]
                        # try:
                        detailed_role = future.result()  # This is the detailed role information
                        self.update_role_with_detailed_info(role, detailed_role)  # Update role with detailed data
                        # except Exception as exc:
                        #     print(f"Error fetching details for role {role.data.id}: {exc}")

                role_list = role_model_manager.get_role_list(server_ip=self.server.data.server_ip)
                if role_list:
                    # logger.debug(f'get_all_roles = {camera_list}')
                    if len(role_list) == len(data_list):
                        # sử dụng get_all_roles() trong trường hợp update role
                        role_model_manager.update_role_list(controller=self, role_list=data_list)
                    else:
                        # sử dụng get_all_roles() trong trường hợp delete role
                        role_model_manager.find_role_deleted(controller=self, role_list=data_list)
                        '''?????????'''
                        # self.get_all_users()
                else:
                    role_model_manager.add_role_list(role_list=data_list, controller=self)
                return data_list
            else:
                if self.show_message_dialog:
                    self.show_message_dialog(response)
        return None

    def get_all_infor_group(self, role_id):
        response = self.api_client.get_role_infor(role_id)
        if response is not None and response.status_code == HTTPStatusCode.OK.value:
            return response.json()

    def update_role_with_detailed_info(self, role, detailed_data):
        if detailed_data is not None:
            dict_value = detailed_data["data"]
            role.data.roleCameras = [RoleCamera.from_dict(cam) for cam in dict_value.get('roleCameras', [])]
            role.data.roleGroups = [RoleGroup.from_dict(group) for group in dict_value.get('roleGroups', [])]
            role.data.roleMenus = [RoleMenu.from_dict(menu) for menu in dict_value.get('roleMenus', [])]
            role.data.userRoles = [UserRole.from_dict(user_role) for user_role in dict_value.get('userRoles', [])]

    def get_all_menu_function(self):
        response = self.api_client.get_all_menu_function()
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                data = response.json()
                data_list = []
                for item in data['data']:
                    role = FunctionMenuModel(menu=FunctionMenu.from_dict(item))
                    role.data.server_ip = self.server.data.server_ip
                    data_list.append(role)

                role_list = function_menu_model_manager.get_function_menu_list(server_ip=self.server.data.server_ip)
                if role_list:
                    print(f"ROLES ARE EXISTING")
                else:
                    function_menu_model_manager.add_function_menu_list(menu_list=data_list, controller=self)
                return data_list
            else:
                if self.show_message_dialog:
                    self.show_message_dialog(response)
        return None

    def get_all_profile_group(self):
        response = self.api_client.get_all_profile_group()
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                data = response.json()
                data_list = []
                for item in data['data']:
                    profile_group = ProfileGroupModel(profile_group=ProfileGroup.from_dict(item))
                    profile_group.data.server_ip = self.server.data.server_ip
                    data_list.append(profile_group)
                profile_group_list = profile_group_model_manager.get_profile_group_list(
                    server_ip=self.server.data.server_ip)
                if profile_group_list:
                    print(f"PROFILE GROUP LIST ARE EXISTING")
                else:
                    profile_group_model_manager.add_profile_group_list(profile_list=data_list, controller=self)
                return data_list
            else:
                if self.show_message_dialog:
                    self.show_message_dialog(response)
        return None

    def get_users_in_role(self, role_id):
        response = self.api_client.get_user_in_role(role_id)
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                data = response.json()
                data_list = []
                if data['data'] is not None:
                    for item in data['data']:
                        user_model = UserModel(user=Users.from_dict(item))
                        user_model.data.server_ip = self.server.data.server_ip
                        data_list.append(user_model)
                return data_list
            else:
                if self.show_message_dialog:
                    self.show_message_dialog(response)
        return None

    def create_new_user(self, parent, user_data: Users):
        # self.api_client.create_new_user_thread(
        #     parent=parent, data=user_data.to_dict(), callback=self.callback_create_new_user)
        worker = GenericRunnable(target=self.api_client.create_new_user_api, callback=self.callback_create_new_user, data=user_data.to_dict())
        self.thread_pool.start(worker)

    def callback_create_new_user(self, response):
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                if self.show_message_dialog:
                    self.show_message_dialog(
                        response, TypeRequestVMS.CREATE_CAMERA)
                data = Users.from_dict(response.json()['data'])
                user_model = UserModel(user=data)
                user_model.data.server_ip = self.server.data.server_ip
                user_model_manager.add_user(user=user_model)
                if len(user_model.data.userRoles) > 0:
                    self.get_all_roles()
                return data
            elif response.status_code == HTTPStatusCode.NOT_FOUND.value and response.json()['message'] == 'Username existed':

                return None
            else:
                if self.show_message_dialog:
                    self.show_message_dialog(response)
        return None

    def update_user_information_by_put(self, parent, user_data: Users):
        # self.api_client.update_user_information_thread(parent=parent, data=user_data.to_dict(), callback=self.callback_update_user_information)
        worker = GenericRunnable(target=self.api_client.update_user_information_api, callback=self.callback_update_user_information,
                                 data=user_data.to_dict())
        self.thread_pool.start(worker)

    def callback_update_user_information(self, response):
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                data = Users.from_dict(response.json()['data'])
                user_model: UserModel = user_model_manager.get_user_model(user_id=data.id)
                diff = []
                if user_model is not None:
                    diff = user_model.diff_user_model(user=data)
                if 'userRoles' in diff:
                    self.get_all_roles()
                return data
            else:
                # Show notification here
                pass
        return None

    def upload_file_to_minio(self, parent, file):
        # self.api_client.upload_file_thread(parent=parent, callback=self.callback_upload_file, data=file)
        worker = GenericRunnable(target=self.api_client.upload_file_service,
                                 callback=self.callback_upload_file,
                                 file=file)
        self.thread_pool.start(worker)

    def callback_upload_file(self, response):
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                data = response.json()['data']
                file_model = FileModel.from_dict(data)
                self.pick_file_model = file_model
                return file_model
            else:
                pass
        return None

    def delete_single_user(self, user_id):
        # threading.Thread(
        #     target=self.callback_delete_single_user, args=(user_id,)).start()
        worker = GenericRunnable(target=self.api_client.delete_single_user_api,
                                 callback=self.callback_delete_single_user,
                                 user_id=user_id)
        self.thread_pool.start(worker)

    def callback_delete_single_user(self, response):
        # response = self.api_client.delete_single_user_api(user_id)
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                json = response.json()
                data = json['message']
                self.get_all_roles()
                self.get_all_users()
                return data
            else:
                if self.show_message_dialog:
                    self.show_message_dialog(response)
        else:
            logger.debug("callback_delete_single_user ERROR")
        return None

    def create_new_role(self, data: Role):
        worker = GenericRunnable(target=self.api_client.create_new_role_api,
                                 callback=self.callback_create_new_role,
                                 data=data.to_dict())
        self.thread_pool.start(worker)

    def update_role_information_by_put(self, data: Role):
        worker = GenericRunnable(target=self.api_client.update_role_information_api,
                                 callback=self.callback_update_role_information,
                                 data=data.to_dict())
        self.thread_pool.start(worker)

    def delete_single_role(self, role_id):
        worker = GenericRunnable(target=self.api_client.delete_single_role_api,
                                 callback=self.callback_delete_single_role,
                                 role_id=role_id)
        self.thread_pool.start(worker)

    def callback_create_new_role(self, response):
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                data = Role.from_dict(response.json()['data'])
                role_model = RoleModel(role=data)
                role_model.data.server_ip = self.server.data.server_ip
                role_model_manager.add_role(role=role_model)
                if len(role_model.data.userRoles) > 0:
                    self.get_all_users()
                listen_show_notification.listen_API_success_message_signal((response, TypeRequestVMS.CREATE_NEW_ROLE, None))
                return data
            else:
                listen_show_notification.listen_API_success_message_signal(response)
        return None

    def callback_update_role_information(self, response):
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                data = Role.from_dict(response.json()['data'])
                role_model: RoleModel = role_model_manager.get_role_model(role_id=data.id)
                diff = []
                if role_model is not None:
                    diff = role_model.diff_role_model(role=data)
                if 'userRoles' or 'name' in diff:
                    self.get_all_users()
                return data
            else:
                # Show notification here
                pass
        return None

    def callback_delete_single_role(self, response):
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                result_json = response.json()
                data = result_json['message']
                self.get_all_roles()
                self.get_all_users()
                return data
            else:
                if self.show_message_dialog:
                    self.show_message_dialog(response)
        else:
            logger.debug("callback_delete_single_role ERROR")
        return None

    def check_user_information(self, type_check, data):
        worker = GenericRunnable(target=self.api_client.check_user_information_service,
                                 callback=lambda response: self.callback_check_user_information(type_check, response),
                                 data=data)
        self.thread_pool.start(worker)

    def callback_check_user_information(self, type_check, response):
        if response.status_code == 200:
            if response.json()['code'] == 200:
                main_controller.signal_check_username.emit((type_check, "GOOD"))
            else:
                main_controller.signal_check_username.emit((type_check, response.json()['message']))
        else:
            main_controller.signal_check_username.emit((type_check, "BAD"))


    # API AI Box
    def create_discovery_in_group(self,parent = None, data = None):
        thread = APIThread(
            parent=parent, target=self.api_client.create_discovery_in_group, callback=self.callback_create_discovery_in_group, args=(data,))
        thread.start()

    def callback_create_discovery_in_group(self, response):
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                pass
                # logger.debug(f"Success callback_create_discovery_in_group = {response.json()}")
        else:
            logger.debug(f"response = {response}")

    def get_discovery_in_group(self,parent = None, cameraGroupId = None):
        thread = APIThread(
            parent=parent, target=self.api_client.get_discovery_in_group, callback=self.callback_get_discovery_in_group, args=(cameraGroupId,))
        thread.start()

    def callback_get_discovery_in_group(self, response):
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                data = response.json()
                result = DiscoveryCameraModel.from_dict(data)
                # logger.debug(f"Success callback_get_discovery_in_group = {data}")
                main_controller.discovery_in_group_signal.emit((result))
        else:
            logger.debug(f"response = {response}")

    def create_camera_in_group(self,parent = None, data = None):
        thread = APIThread(
            parent=parent, target=self.api_client.create_camera_in_group, callback=self.callback_create_camera_in_group, args=(data,))
        thread.start()

    def callback_create_camera_in_group(self, response):
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                pass
                logger.debug(f"Success callback_create_camera_in_group = {response.json()}")
        else:
            logger.debug(f"response = {response}")

    # API Gate
    def create_door(self, data = None):
        def func(data):
            response = self.api_client.create_door(data=data)
            if response is not None:
                if response.status_code == HTTPStatusCode.OK.value:
                    door = Door.from_dict(response.json())
                    door_model = DoorModel(door=door)
                    door_model_manager.add_door(controller = self,door=door_model)
                    logger.debug(f"Success create_door = {response.json()}")
            else:
                logger.debug(f"response = {response}")
        threading.Thread(target=func,args=(data,)).start()

    def get_doors(self,ids=None):
        response = self.api_client.get_doors(ids=ids)
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                data = response.json()
                # logger.debug(f'get_aiflows = {data}')
                data_list = []
                for index, item in enumerate(data):
                    door = Door.from_dict(item)
                    door.server_ip = self.server.data.server_ip
                    door_model = DoorModel(door=door)
                    data_list.append(door_model)
                doors = door_model_manager.get_doors(server_ip=self.server.data.server_ip)
                if doors:
                    if len(doors) == len(data_list):
                        pass
                    elif len(doors) > len(data_list):
                        pass
                        # sử dụng get_cameras() trong trường hợp delete camera
                        # camera_model_manager.find_camera_deleted(controller = self,camera_list=data_list)
                        # sau khi xóa Camera thì cần cập nhật Group từ server
                        # self.get_cameras()

                    else:
                        pass
                        # sử dụng get_cameras() trong trường hợp add camera
                        # su dung trong websocket
                        # camera_list_added:CameraModel = camera_model_manager.find_camera_added(camera_list=data_list)
                        # is_cameraGroupIds = False
                        # camera_list = []
                        # for camera in camera_list_added:
                        #     if camera.data.cameraGroupIds is not None and len(camera.data.cameraGroupIds) > 0:
                        #         is_cameraGroupIds = True
                        # if is_cameraGroupIds:
                        #     self.get_groups()

                else:
                    # sử dụng get_cameras() trong trường hợp init app
                    # logger.debug(f'get_cameras2 = {camera_list}')
                    door_model_manager.add_doors(doors=data_list,controller=self)
                return data_list
            else:
                return None
        return None

    def update_door_by_put(self, data: Door = None):
        def func(data):
            response = self.api_client.update_door_by_put(data=data.to_dict())
            if response is not None:
                logger.debug(f"update_camera_thread_by_put = {response.status_code}")
                if response.status_code == HTTPStatusCode.OK.value:
                    # if self.show_message_dialog:
                    #     self.show_message_dialog(
                    #         response, TYPE_REQUEST_VMS.UPDATE_CAMERA)
                    data = Door.from_dict(response.json())
                    door_model:DoorModel = door_model_manager.get_door_model(id = data.id)
                    diff = []
                    if door_model is not None:
                        diff = door_model.diff_door_model(door = data)

                    return data
                else:
                    if self.show_message_dialog:
                        self.show_message_dialog(response)
            else:
                logger.debug("update_camera_thread_by_put error")
            return None
        threading.Thread(target=func,args=(data,)).start()

    def delete_door(self, data: Door = None):
        def func(data):
            response = self.api_client.delete_door(ids=data.id)
            if response is not None:
                # logger.debug(f"delete_door done = {response.json()}")
                if response.status_code == HTTPStatusCode.NO_CONTENT.value or response.status_code == HTTPStatusCode.OK.value:
                    # if self.show_message_dialog:
                    #     self.show_message_dialog(
                    #         response, TYPE_REQUEST_VMS.DELETE_CAMERA)
                    pass
                    # self.get_cameras()
                    # self.get_groups()
                else:
                    if self.show_message_dialog:
                        self.show_message_dialog(response)
        threading.Thread(target=func,args=(data,)).start()

    # API Avigilon
    def forward_avigilon_ptz(self, cameraId = None, endPoint = "/camera/commands/pan-tilt-zoom",requestData = None):
        def func(cameraId,endPoint,requestData):
            # logger.debug(f'forward_avigilon_ptz = cameraId = {cameraId} endPoint = {endPoint} requestData = {requestData}')
            response = self.api_client.forward_avigilon_ptz(cameraId=cameraId,endPoint=endPoint,requestData=requestData)
            if response is not None:
                if response.status_code == HTTPStatusCode.OK.value:
                    data = response.json()
                    logger.debug(f'Success forward_avigilon_ptz = {data}')

            else:
                logger.debug(f"response = {response}")
        threading.Thread(target=func,args=(cameraId,endPoint,requestData,)).start()

    def get_address_by_coord_thread(self, lon, lat, callback = None):
        print(f'get_address_by_coord_thread = {lon} : {lat}')
        response = self.api_client.get_address_from_coord(lon=lon, lat=lat)
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                if callback is not None:
                    callback(response)
                return response
        return None

    def get_address_by_coord(self, lon, lat, callback = None):
        self.executor.submit(self.get_address_by_coord_thread, lon, lat, callback)

    def get_coord_by_address_thread(self, detail_address, callback = None):
        print(f'get_coord_by_address_thread = {detail_address}')
        response = self.api_client.get_map_info_by_keyword(detail_address, False)
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                if callback is not None:
                    callback(response)
                return response
        return None

    def get_coord_by_address(self, detail_address, callback = None):
        self.executor.submit(self.get_coord_by_address_thread, detail_address, callback)

    def get_map_suggestions_thread(self, keyword, callback = None):
        print(f'get_map_suggestions_thread = {keyword}')
        response = self.api_client.get_map_info_by_keyword(keyword, True)
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                if callback is not None:
                    callback(response)
                return response
        return None

    def get_map_suggestions(self, keyword, callback = None):
        self.executor.submit(self.get_map_suggestions_thread, keyword, callback)
            
    def process_zone_aiflow_batch(self, zones_data, aiflow_id, camera_id, aiflow_type="RECOGNITION", ai_settings=None):
        """Process batch creation of zones and apply them to an AI flow.
        
        Args:
            zones_data: List of ZoneModel objects
            aiflow_id: str (AI flow ID)
            camera_id: str (Camera ID)
            aiflow_type: str (Type of AI flow, default "RECOGNITION")
            
        Returns:
            bool: True if successful, False otherwise
        """
        # try:
        logger.debug(f"Starting batch processing with:")
        logger.debug(f"AI Flow ID: {aiflow_id}")
        logger.debug(f"Camera ID: {camera_id}")
        logger.debug(f"AI Flow Type: {aiflow_type}")
        logger.debug(f"Number of zones to process: {len(zones_data)}")

        # Get existing zones for this AI flow
        server_zones = self.get_zones(aiFlowIds=[aiflow_id])
        server_zones_dict = {zone['id']: zone for zone in server_zones} if server_zones else {}

        # Step 1: Process all zones (create new ones, update existing ones)
        zone_ids = []
        for zone_model in zones_data:
            logger.debug(f"Processing zone: {zone_model.name}")
            # Convert ZoneModel to dict for API call - exclude aiFlowId
            zone_data = {
                "name": zone_model.name,
                "type": zone_model.type,
                "polygon": zone_model.polygon,
                "active": zone_model.active,
                "objects": zone_model.objects,
            }
            if zone_model.line is not None:
                zone_data["line"] = zone_model.line
            if zone_model.color is not None:
                zone_data["color"] = zone_model.color
            if zone_model.colorLine is not None:
                zone_data["colorLine"] = zone_model.colorLine
            
            logger.debug(f"Zone data to send: {zone_data}")

            if zone_model.id and zone_model.id in server_zones_dict:
                # Check if zone needs update by comparing with server version
                server_zone = server_zones_dict[zone_model.id]
                zone_data = {
                    "name": zone_model.name,
                    "type": zone_model.type,
                    "polygon": zone_model.polygon,
                    "active": zone_model.active,
                    "objects": zone_model.objects,
                }
                if zone_model.line is not None:
                    zone_data["line"] = zone_model.line
                if zone_model.color is not None:
                    zone_data["color"] = zone_model.color
                if zone_model.colorLine is not None:
                    zone_data["colorLine"] = zone_model.colorLine
                
                # Compare values to see if update is needed
                needs_update = False
                for key, value in zone_data.items():
                    if key in server_zone and server_zone[key] != value:
                        needs_update = True
                        break
                
                if needs_update:
                    # Update existing zone
                    zone_data["id"] = zone_model.id
                    logger.debug(f"Updating zone {zone_model.id} as values have changed")
                    response = self.api_client.update_zones(zone_data)
                    if response.status_code == HTTPStatusCode.OK.value:
                        zone_ids.append(zone_model.id)
                        logger.debug(f"Successfully updated zone with ID: {zone_model.id}")
                    else:
                        logger.error(f"Failed to update zone. Response: {response.text}")
                        listen_show_notification.listen_API_fail_message_signal.emit(
                            (None, TypeRequestVMS.APPLY_AIFLOW, "Failed to update zone")
                        )
                        return False
                else:
                    # No update needed, just add to zone_ids
                    logger.debug(f"Zone {zone_model.id} unchanged, skipping update")
                    zone_ids.append(zone_model.id)
            else:
                # Create new zone
                logger.debug(f"Create new zone: {zone_data}")
                response = self.api_client.create_zones(zone_data)
                if response.status_code == HTTPStatusCode.OK.value:
                    response_data = response.json()
                    logger.debug(f"Zone creation response: {response_data}")
                    zone_id = response_data.get("id")
                    if zone_id:
                        zone_ids.append(zone_id)
                        logger.debug(f"Successfully created zone with ID: {zone_id}")
                    else:
                        logger.error("Zone created but no ID returned")
                        listen_show_notification.listen_API_fail_message_signal.emit(
                            (None, TypeRequestVMS.APPLY_AIFLOW, "Zone created but no ID returned")
                        )
                        return False
                else:
                    logger.error(f"Failed to create zone. Response: {response.text}")
                    listen_show_notification.listen_API_fail_message_signal.emit(
                        (None, TypeRequestVMS.APPLY_AIFLOW, "Failed to create zone")
                    )
                    return False

        # Delete zones that exist on server but not in zones_data
        new_zone_ids = {zone.id for zone in zones_data if zone.id}
        zones_to_delete = [server_zone['id'] for server_zone in server_zones or [] if server_zone['id'] not in new_zone_ids]
        if zones_to_delete:
            response = self.api_client.delete_zones(id=zones_to_delete)
            if response and response.status_code not in [HTTPStatusCode.OK.value, HTTPStatusCode.NO_CONTENT.value]:
                logger.error(f"Failed to delete zones {zones_to_delete}")

        # Step 2: Add zones to AI flow only if we have zones to add
        if zone_ids:
            logger.debug(f"Adding zones {zone_ids} to AI flow {aiflow_id}")
            add_zones_data = {
                "id": aiflow_id,
                "ids": zone_ids
            }
            response = self.api_client.add_zones_to_aiflow(add_zones_data)
            if response.status_code != HTTPStatusCode.OK.value:
                logger.error(f"Failed to add zones to AI flow. Response: {response.text}")
                listen_show_notification.listen_API_fail_message_signal.emit(
                    (None, TypeRequestVMS.APPLY_AIFLOW, "Failed to add zones to AI flow")
                )
                return False
            logger.debug(f"Successfully added zones to AI flow - {response}")

            # # Step 3: Apply AI flow to camera only after successfully adding zones
            # logger.debug(f"Applying AI flow {aiflow_id} to camera {camera_id}")
            # apply_data = {
            #     "id": camera_id,
            #     "apply": True,
            #     "type": aiflow_type
            # }
            # response = self.api_client.apply_aiflow_to_camera(apply_data)
            # if response.status_code != HTTPStatusCode.OK.value:
            #     logger.error(f"Failed to apply AI flow to camera. Response: {response.text}")
            #     listen_show_notification.listen_API_fail_message_signal.emit(
            #         (None, TypeRequestVMS.APPLY_AIFLOW, "Failed to apply AI flow to camera")
            #     )
            #     return False
            
            logger.debug("Successfully completed batch processing")
            listen_show_notification.listen_API_success_message_signal.emit(
                (None, TypeRequestVMS.APPLY_AIFLOW, "Successfully completed batch processing")
            )

            # Step 4: Update new AI flow 
            if ai_settings is not None:
                # get ai flow by id
                ai_flow = aiflow_model_manager.get_aiflow_model(id=aiflow_id)
                logger.debug(f"ai_flow = {ai_flow}")
                if ai_flow is not None:
                    # không cần cập nhật apply - để người dùng chủ động
                    # ai_flow.data.apply = ai_flow.data.apply
                    ai_flow.data.type = aiflow_type
                    ai_flow.data.threshold = ai_settings.get("threshold")
                    ai_flow.data.interval = ai_settings.get("interval")
                    logger.debug(f'ai_settings: {ai_settings}')
                    if ai_settings.get("stopDuration") is not None:
                        ai_flow.data.stopDuration = ai_settings.get("stopDuration")
                    if ai_settings.get("disappearDuration") is not None:
                        ai_flow.data.disappearDuration = ai_settings.get("disappearDuration")
                    if ai_settings.get("appearanceCount") is not None:
                        ai_flow.data.appearanceCount = ai_settings.get("appearanceCount")
                    if ai_settings.get("trackingDuration") is not None:
                        ai_flow.data.trackingDuration = ai_settings.get("trackingDuration")
                    
                    # update ai flow
                    response = self.api_client.update_aiflows(data=ai_flow.data.to_dict())
                    if response.status_code == HTTPStatusCode.OK.value:
                        logger.debug(f"Successfully updated ai flow {aiflow_id}")
                    elif response.status_code == HTTPStatusCode.INTERNAL_SERVER_ERROR.value:
                        listen_show_notification.listen_API_fail_message_signal.emit(
                            (response, TypeRequestVMS.APPLY_AIFLOW, None)
                        )
                        return False
                    else:
                        logger.error(f"Failed to update ai flow {aiflow_id}")
                        listen_show_notification.listen_API_fail_message_signal.emit(
                            (None, TypeRequestVMS.APPLY_AIFLOW, "Failed to update ai flow")
                        )
                        return False
            return True
        else:
            logger.error("No zones were created successfully")
            listen_show_notification.listen_API_fail_message_signal.emit(
                (None, TypeRequestVMS.APPLY_AIFLOW, "No zones were created successfully")
            )
            return False

        # except Exception as e:
        #     logger.error(f"Error in process_zone_aiflow_batch: {str(e)}")
        #     if Config.DEBUG:
        #         traceback.print_exc()
        #     error_message = 'SERVER_DISCONNECTED'
        #     listen_show_notification.listen_API_fail_message_signal.emit(
        #         (None, TypeRequestVMS.APPLY_AIFLOW, error_message))
        #     return False

    def process_zone_aiflow_batch_async(self, parent, zones_data, aiflow_id, camera_id, aiflow_type="RECOGNITION", callback=None, ai_settings=None):
        """Asynchronously process a batch of zone and AI flow operations.
        
        Args:
            parent: QObject parent for the thread
            zones_data: List of dictionaries containing zone data
            aiflow_id: str (AI flow ID)
            camera_id: str (Camera ID)
            aiflow_type: str (Type of AI flow, default "RECOGNITION")
            callback: Function to call with result (success: bool)
        """
        def process():
            success = self.process_zone_aiflow_batch(zones_data, aiflow_id, camera_id, aiflow_type, ai_settings)
            if callback:
                callback(success)
            
        process_thread = APIThread(
            parent=parent,
            target=process
        )
        process_thread.start()

class ControllerManager(QObject):
    result_login = Signal(tuple)
    start_login = Signal(tuple)
    add_controller_signal = Signal(tuple)
    exit_controller_signal = Signal(tuple)
    __instance = None
    def __init__(self):
        super().__init__()
        self.data = {}

    @staticmethod
    def get_instance():
        if ControllerManager.__instance is None:
            ControllerManager.__instance = ControllerManager()
        return ControllerManager.__instance

    def add_controller(self,controller:Controller = None):
        if len(self.data) == 0:
            # add tab here
            main_controller.add_all_tab_signal.emit()

        self.data[controller.server.data.id] = controller
        server_info_model_manager.add_server(server = controller.server)
        controller.get_data_from_server()

    def exit_controller(self,controller:Controller = None):
        self.exit_controller_signal.emit((controller))
        camera_model_manager.delete_server(controller.server.data.server_ip)
        group_model_manager.delete_server(controller.server.data.server_ip)
        aiflow_model_manager.delete_server(server_ip=controller.server.data.server_ip)
        door_model_manager.delete_server(server_ip=controller.server.data.server_ip)
        video_player_manager.remove_player(server=controller.server)
        user_model_manager.delete_server(server_ip=controller.server.data.server_ip)
        role_model_manager.delete_server(server_ip=controller.server.data.server_ip)
        function_menu_model_manager.delete_server(server_ip=controller.server.data.server_ip)
        profile_group_model_manager.delete_server(server_ip=controller.server.data.server_ip)
        map_manager.delete_server(serverIp=controller.server.data.server_ip)
        building_manager.delete_server(serverIp=controller.server.data.server_ip)
        floor_manager.delete_server(serverIp=controller.server.data.server_ip)
        if controller.websocket_vms is not None:
            controller.websocket_vms.messageProcessor.message_queue.put(None)
            controller.websocket_vms.close()
            controller.websocket_vms = None
        if controller.websocket_event is not None:
            controller.websocket_event.messageProcessor.message_queue.put(None)
            controller.websocket_event.close()
            controller.websocket_event = None
        del self.data[controller.server.data.id]
        # remove tab here
        if len(self.data) == 0:
            # add tab here
            main_controller.remove_all_tab_signal.emit()

    def get_controller(self,id = None,server_ip = None,model = None,model_id = None):
        if id is not None:
            return self.data.get(id,None)
        elif server_ip is not None:
            for id,controller in self.data.items():
                if controller.server.data.server_ip == server_ip:
                    return controller
        elif isinstance(model,CameraModel):
            server_ip = model.get_property('server_ip')
            for id,controller in self.data.items():
                if controller.server.data.server_ip == server_ip:
                    return controller
        elif isinstance(model,GroupModel):
            server_ip = model.get_property('server_ip')
            for id,controller in self.data.items():
                if controller.server.data.server_ip == server_ip:
                    return controller
        elif model_id is not None:
            server_ip = model.data.server_ip
            for id,controller in self.data.items():
                if controller.server.data.server_ip == server_ip:
                    return controller
        return None


controller_manager = ControllerManager.get_instance()
