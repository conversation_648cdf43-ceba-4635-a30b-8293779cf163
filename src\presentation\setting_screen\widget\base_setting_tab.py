from PySide6.QtCore import Qt, Signal, Slot, QEvent
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                               QSpacerItem, QSizePolicy, QFrame)
import logging
from src.common.controller.main_controller import main_controller
from src.utils.utils import Utils

logger = logging.getLogger(__name__)


class BaseSettingTab(QWidget):
    """Base class for all setting tabs with common functionality"""
    
    # Common signals
    setting_changed = Signal(str, object)  # (setting_name, value)
    apply_settings = Signal()
    reset_settings = Signal()

    def __init__(self, parent=None):
        """Initialize the base setting tab

        Args:
            parent: Parent widget
        """
        super().__init__(parent)
        # Set object name and transparent background
        self.setObjectName("base_setting_tab")
        self.setStyleSheet("""
            QWidget {
                background-color: transparent;
            }
        """)
        self.setup_ui()
        # Apply styling after UI setup
        self.apply_styling()

    def setup_ui(self):
        """Setup the main UI structure"""
        # Create main layout
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(20, 10, 20, 20)  # Reduced top margin
        self.main_layout.setSpacing(15)  # Reduced spacing
        self.main_layout.setAlignment(Qt.AlignTop)  # Align content to top

    def add_separator(self):
        """Add a horizontal separator line"""
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setFixedHeight(1)
        self.main_layout.addWidget(separator)

    def create_section_layout(self, title, subtitle):
        """Create a horizontal section layout with title and subtitle

        Args:
            title: Section title text
            subtitle: Section subtitle text

        Returns:
            tuple: (section_layout, labels_layout, controls_layout)
        """
        # Create horizontal layout for the entire section
        section_layout = QHBoxLayout()

        # Left side: Labels
        labels_layout = QVBoxLayout()
        labels_layout.setSpacing(4)

        title_label = self.create_label(title, "title")
        subtitle_label = self.create_label(subtitle, "subtitle")

        # Set ObjectName for translation - use sanitized title as base
        sanitized_title = title.lower().replace(" ", "_").replace("&", "").replace(":", "")
        title_label.setObjectName(f"section_title_{sanitized_title}")
        subtitle_label.setObjectName(f"section_subtitle_{sanitized_title}")

        labels_layout.addWidget(title_label)
        labels_layout.addWidget(subtitle_label)

        # Right side: Controls
        controls_layout = QVBoxLayout()
        controls_layout.setSpacing(8)

        # Add both sides to section layout
        section_layout.addLayout(labels_layout)
        section_layout.addLayout(controls_layout)
        section_layout.setStretch(0, 1)  # Labels take 1 part
        section_layout.setStretch(1, 2)  # Controls take 2 parts

        return section_layout, labels_layout, controls_layout

    def add_section(self, title, subtitle):
        """Add a new section to the main layout
        
        Args:
            title: Section title
            subtitle: Section subtitle
            
        Returns:
            QVBoxLayout: Controls layout for adding widgets
        """
        section_layout, _, controls_layout = self.create_section_layout(title, subtitle)
        
        # Add to main layout
        self.main_layout.addLayout(section_layout)

        return controls_layout

    def add_bottom_spacer(self):
        """Add spacer at the bottom to push content to top"""
        from PySide6.QtWidgets import QSpacerItem, QSizePolicy
        self.main_layout.addItem(QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding))

    def create_label(self, text, style_class="normal"):
        """Create a label with proper transparent styling

        Args:
            text: Label text
            style_class: Style class - "normal", "title", "subtitle", "gray"

        Returns:
            QLabel: Styled label with transparent background
        """
        label = QLabel(self.tr(text))

        if style_class == "title":
            label.setStyleSheet("QLabel { font-weight: bold; font-size: 16px; background-color: transparent; }")
        elif style_class == "subtitle":
            label.setStyleSheet("QLabel { color: gray; font-size: 12px; background-color: transparent; }")
        elif style_class == "gray":
            label.setStyleSheet("QLabel { color: gray; background-color: transparent; }")
        else:  # normal
            label.setStyleSheet("QLabel { background-color: transparent; }")

        return label

    def apply_styling(self):
        """Apply styling - calls set_dynamic_stylesheet for backward compatibility"""
        self.set_dynamic_stylesheet()

    def set_dynamic_stylesheet(self):
        """Apply dynamic theme-based styling to the widget"""
        text_color = main_controller.get_theme_attribute("Color", "text_color_all_app")
        primary_color = main_controller.get_theme_attribute("Color", "primary")
        border_color = main_controller.get_theme_attribute("Color", "border_color")
        
        # Apply styling to child widgets, keeping main widget transparent
        self.setStyleSheet(f"""
            /* Force all widgets to be transparent by default */
            QWidget {{
                color: {text_color};
                background-color: transparent;
            }}

            /* Main widget stays transparent */
            QWidget[objectName="base_setting_tab"] {{
                background-color: transparent;
            }}
            
            QLabel {{
                color: {text_color};
                background-color: transparent;
                border: none;
            }}
            
            QCheckBox {{
                color: {text_color};
                background-color: transparent;
                spacing: 8px;
                font-size: 14px;
            }}
            
            QCheckBox:hover {{
                color: {primary_color};
            }}
            
            QComboBox {{
                background-color: transparent;
                color: {text_color};
                border: 2px solid {border_color};
                border-radius: 4px;
                padding: 4px 8px;
                font-size: 14px;
                min-width: 120px;
            }}

            QComboBox:hover {{
                border-color: {primary_color};
            }}

            QComboBox:focus {{
                border-color: {primary_color};
            }}
            
            QComboBox::drop-down {{
                border: none;
                width: 20px;
            }}
            
            QComboBox::down-arrow {{
                image: url({main_controller.get_theme_attribute("Image", "arrow_combobox")});
                width: 12px;
                height: 12px;
            }}
            
            QSpinBox {{
                background-color: transparent;
                color: {text_color};
                border: 2px solid {border_color};
                border-radius: 4px;
                padding: 4px 8px;
                font-size: 14px;
                min-width: 80px;
            }}

            QSpinBox:hover {{
                border-color: {primary_color};
            }}

            QSpinBox:focus {{
                border-color: {primary_color};
            }}

            QSpinBox::up-button {{
                border: none;
                width: 16px;
                height: 16px;
            }}

            QSpinBox::down-button {{
                border: none;
                width: 16px;
                height: 16px;
            }}

            QSpinBox::up-arrow {{
                image: url({main_controller.get_theme_attribute("Image", "up_arrow_spinbox")});
                width: 10px;
                height: 10px;
            }}

            QSpinBox::down-arrow {{
                image: url({main_controller.get_theme_attribute("Image", "down_arrow_spinbox")});
                width: 10px;
                height: 10px;
            }}
            
            QSlider::groove:horizontal {{
                border: 1px solid {border_color};
                height: 6px;
                background: transparent;
                border-radius: 3px;
            }}
            
            QSlider::handle:horizontal {{
                background: {primary_color};
                border: 1px solid {border_color};
                width: 16px;
                height: 16px;
                border-radius: 8px;
                margin: -6px 0;
            }}
            
            QSlider::handle:horizontal:hover {{
                background: {primary_color};
                border: 2px solid {primary_color};
            }}
            
            QPushButton {{
                background-color: {primary_color};
                color: white;
                border: none;
                border-radius: 6px;
                font-size: 14px;
                font-weight: bold;
                padding: 8px 16px;
                min-width: 100px;
            }}
            
            QPushButton:hover {{
                background-color: {primary_color};
                opacity: 0.8;
            }}
            
            QFrame {{
                background-color: {border_color};
            }}
        """)

    def emit_setting_changed(self, setting_name, value):
        """Emit setting changed signal
        
        Args:
            setting_name: Name of the setting that changed
            value: New value of the setting
        """
        logger.debug(f"Setting changed: {setting_name} = {value}")
        self.setting_changed.emit(setting_name, value)

    def translate_ui(self):
        """Update UI text translations - to be implemented by subclasses"""
        pass

    def auto_translate_all_widgets(self, context_name):
        """
        Universal translation method that automatically translates ALL widgets with text.
        This ensures 100% coverage without missing any widget.

        Args:
            context_name: Translation context (e.g., "GeneralSettingTab")
        """
        from PySide6.QtCore import QCoreApplication
        from PySide6.QtWidgets import QComboBox, QLabel, QCheckBox, QPushButton, QRadioButton

        logger.debug(f"🔄 auto_translate_all_widgets({context_name}) - Starting comprehensive translation")

        # Store original texts for translation mapping
        translation_map = self._build_translation_map(context_name)

        # 1. Translate all QLabel widgets
        for label in self.findChildren(QLabel):
            if hasattr(label, 'setText') and hasattr(label, 'text'):
                original_text = label.text().strip()
                if original_text and original_text in translation_map:
                    label.setText(QCoreApplication.translate(context_name, translation_map[original_text], None))

        # 2. Translate all QCheckBox widgets
        for checkbox in self.findChildren(QCheckBox):
            if hasattr(checkbox, 'setText') and hasattr(checkbox, 'text'):
                original_text = checkbox.text().strip()
                if original_text and original_text in translation_map:
                    checkbox.setText(QCoreApplication.translate(context_name, translation_map[original_text], None))

        # 3. Translate all QPushButton widgets
        for button in self.findChildren(QPushButton):
            if hasattr(button, 'setText') and hasattr(button, 'text'):
                original_text = button.text().strip()
                if original_text and original_text in translation_map:
                    button.setText(QCoreApplication.translate(context_name, translation_map[original_text], None))

        # 4. Translate all QRadioButton widgets
        for radio in self.findChildren(QRadioButton):
            if hasattr(radio, 'setText') and hasattr(radio, 'text'):
                original_text = radio.text().strip()
                if original_text and original_text in translation_map:
                    radio.setText(QCoreApplication.translate(context_name, translation_map[original_text], None))

        # 5. Translate QComboBox items
        for combo in self.findChildren(QComboBox):
            self._translate_combo_box(combo, context_name, translation_map)

        logger.debug(f"✅ auto_translate_all_widgets({context_name}) - All widgets translated")

    def _build_translation_map(self, context_name):
        """Build translation mapping for the specific tab context"""
        # This will be overridden by each tab to provide their specific translations
        return {}

    def apply_combobox_style(self, combobox):
        """Apply consistent styling to combo boxes"""
        from src.common.controller.main_controller import main_controller

        combobox.setStyleSheet(f'''
            QComboBox {{
                background-color: {main_controller.get_theme_attribute("Color", "main_background")};
                color: {main_controller.get_theme_attribute("Color", "text_color_all_app")};
                border: 1px solid {main_controller.get_theme_attribute("Color", "common_border")};
                border-radius: 4px;
                padding: 6px 12px;
                font-size: 14px;
                min-height: 20px;
                min-width: 120px;
            }}
            QComboBox:focus {{
                border: 1px solid {main_controller.get_theme_attribute("Color", "primary")};
            }}
            QComboBox:disabled {{
                border: 1px solid {main_controller.get_theme_attribute("Color", "text_disable")};
                color: {main_controller.get_theme_attribute("Color", "text_disable")};
                background-color: {main_controller.get_theme_attribute("Color", "main_background", 0.5)};
            }}
            QComboBox::drop-down {{
                border: none;
                width: 30px;
                background: transparent;
            }}
            QComboBox::down-arrow {{
                image: url({main_controller.get_theme_attribute("Image", "down_arrow_linedit")});
                width: 16px;
                height: 16px;
            }}
            QComboBox QAbstractItemView {{
                background-color: {main_controller.get_theme_attribute("Color", "main_background")};
                color: {main_controller.get_theme_attribute("Color", "text_color_all_app")};
                border: 1px solid {main_controller.get_theme_attribute("Color", "common_border")};
                border-radius: 4px;
                selection-background-color: {main_controller.get_theme_attribute("Color", "primary", 0.2)};
                selection-color: {main_controller.get_theme_attribute("Color", "text_color_all_app")};
                padding: 4px;
            }}
            QComboBox QAbstractItemView::item {{
                padding: 6px 12px;
                border: none;
                border-radius: 2px;
                min-height: 20px;
            }}
            QComboBox QAbstractItemView::item:selected {{
                background-color: {main_controller.get_theme_attribute("Color", "primary", 0.2)};
                color: {main_controller.get_theme_attribute("Color", "text_color_all_app")};
            }}
            QComboBox QAbstractItemView::item:hover {{
                background-color: {main_controller.get_theme_attribute("Color", "primary", 0.1)};
            }}
        ''')

    def _translate_combo_box(self, combo, context_name, translation_map):
        """Translate combo box items while preserving selection"""
        from PySide6.QtCore import QCoreApplication

        if combo.count() == 0:
            return

        current_index = combo.currentIndex()
        items_translated = False

        # Check if any items need translation
        for i in range(combo.count()):
            item_text = combo.itemText(i).strip()
            if item_text in translation_map:
                items_translated = True
                break

        if items_translated:
            # Store all items
            items = []
            for i in range(combo.count()):
                item_text = combo.itemText(i).strip()
                if item_text in translation_map:
                    items.append(QCoreApplication.translate(context_name, translation_map[item_text], None))
                else:
                    items.append(item_text)  # Keep technical values as-is

            # Update combo box
            combo.clear()
            combo.addItems(items)

            # Restore selection
            if current_index < combo.count():
                combo.setCurrentIndex(current_index)

    def changeEvent(self, event):
        """Handle change events, particularly language changes"""
        if event.type() == QEvent.Type.LanguageChange:
            logger.debug(f"Language change event received in {self.__class__.__name__}")
            self.translate_ui()
        super().changeEvent(event)

    def load_settings(self):
        """Load settings from storage - to be implemented by subclasses"""
        pass

    def save_settings(self):
        """Save settings to storage - to be implemented by subclasses"""
        pass
