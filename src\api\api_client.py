from http import HTTPStatus
import inspect
import os
import enum
import traceback
from typing import List
from src.api.error_type_api import ErrorType
import requests
from PySide6 import QtCore
import json

from src.utils.auth_qsettings import AuthQSettings
from src.utils.config import Config
from src.common.server.server_info import ServerInfoModel
import mimetypes
import logging
from ipaddress import ip_address
logger = logging.getLogger(__name__)


class APIThread(QtCore.QThread):
    on_request_done = QtCore.Signal(requests.Response)

    def __init__(self, parent=None, target=None, callback=None, args=()):
        super(APIThread, self).__init__(parent)
        self.daemon = True
        self.callback = callback
        self._target = target
        self._args = args
        self.on_request_done.connect(self.callback)

    def run(self):
        # logger.debug("APIThread run")
        # try:
        if self._target:
            response = self._target(*self._args)
            self.on_request_done.emit(response)

        # except Exception as e:
        #     if Config.DEBUG:
        #         traceback.print_exc()
        #     logger.error(f"APIThread error: {e}")
        #     response = requests.Response()
        #     response.status_code = requests.codes.internal_server_error
        #     response.reason = str(e)
        #     self.on_request_done.emit(response)

        # finally:
        #     # Avoid a refcycle if the thread is running a function with
        #     # an argument that has a member that points to the thread.
        #     del self._target, self._args


class APIClientSingleton(type):
    """
    Define an Instance operation that lets clients access its unique
    instance.
    """

    def __init__(cls, name, bases, attrs, **kwargs):
        # logger.debug("APIClientSingleton __init________________________")
        super().__init__(name, bases, attrs)
        cls._instance = None

    def __call__(cls, *args, **kwargs):
        # logger.debug("APIClientSingleton __call________________________")
        if cls._instance is None:
            cls._instance = super().__call__(*args, **kwargs)
        return cls._instance

    # 204
    # No Content
    # 201
    # Created
    # 401
    # Unauthorized
    # 403
    # Forbidden
    # 404
    # Not Found

VmsService = '/vms-service'
EmsService = '/ems-service'
UserService = '/user-service'
AUTHORIZATION_SERVICE = '/authorization-service'
AUTHORIZATION_VALUE_GPSTECH = 'Basic Vk1TOkNiR3RvR1khNzZkb1lOcUY2dHV1eg=='
AUTHORIZATION_VALUE_CYGATE = 'Basic Vk1TOjVjQmpnSjBlMXhsTHUhQ0RRWW1IYw=='

class APIClient(object):
    def __init__(self,server:ServerInfoModel = None):
        self.access_token = None
        self.user_id = None
        self.clientId = None
        self.refresh_token = None
        self.is_ip_address = True
        self.server = server
        ip_server = self.refector_server_gpstech(self.server.data.server_ip)
        self.server_ip = ip_server
        self.server_port = self.server.data.server_port # default port for server
        self.websocket_port = self.server.data.websocket_port  # default port for websocket
        self.path_websocket = Config.SERVER_EVENT_PATH_DEFAULT
        self.path_websocket_1 = Config.SERVER_EVENT_PATH_DEFAULT_1
        self.permissions_url = None
        self.generate_url()

    def generate_url(self):
        # tạo các url api cho server
        try:
            # Try to parse as IP address
            ip_address(self.server_ip)
            self.is_ip_address = True
            # For IP address format, use port
            self.server_url = f"http://{self.server_ip}:{self.server_port}"
            self.server_event = f"http://{self.server_ip}:{self.websocket_port}"
            self.websocket_url = f"ws://{self.server_ip}:{self.websocket_port}/socket"
            self.permissions_url = f'http://{self.server_ip}:{self.server_port}/user-service'
            logger.debug(f'server_ip: {self.server_ip} is an IP address, use port')
            logger.debug(f'Server url: {self.server_url}')
            logger.debug(f'Server event: {self.server_event}')
            logger.debug(f'Websocket url: {self.websocket_url}')
            logger.debug(f'Permissions url: {self.permissions_url}')
        except ValueError as e:
            logger.debug(f'server_ip: {self.server_ip} is not an IP address, treat as gateway/domain \nError: {e}')
            # Not an IP address, treat as gateway/domain
            self.is_ip_address = False
            # Remove any existing protocol from server_ip
            server_ip = self.server_ip.replace('https://', '').replace('http://', '')
            # For gateway format, use directly without port
            self.server_url = f"https://{server_ip}"
            self.server_event = f"https://{server_ip}"
            self.websocket_url = f"wss://{server_ip}/socket"
            self.permissions_url = f"https://{server_ip}/user-service"
            logger.debug(f'Server url: {self.server_url}')
            logger.debug(f'Server event: {self.server_event}')
            logger.debug(f'Websocket url: {self.websocket_url}')
            logger.debug(f'Permissions url: {self.permissions_url}')
    def get_url(self,endpoint = ''):
        if self.is_ip_address:
            url = self.server_url + endpoint
        else:
            url = self.server_url + VmsService + endpoint
        return url
    
    def get_headers(self):
        if self.is_ip_address:
            return {
                'Content-Type': 'application/json',
                'Accept': '*/*',
                'Id': '123',
                'Authorities': 'VMS_ADMIN',
            }
        else:
            # print(f'access_token={self.access_token}; refresh_token={self.refresh_token}')
            return {
                'Content-Type': 'application/json',
                'Accept': '*/*',
                'Authorization': f'Bearer {self.access_token}',
                'Cookie': f'access_token={self.access_token}; refresh_token={self.refresh_token}',
                # 'Authorities': 'VMS_ADMIN',
                }
        
    def refresh_access_token(self):
        try:
            logger.debug(f'🔄 [REFRESH] Starting token refresh for server: {self.server_ip}')
            logger.debug(f'🔄 [REFRESH] Current access_token: {self.access_token[:20] + "..." if self.access_token else "None"}')
            logger.debug(f'🔄 [REFRESH] Current refresh_token: {self.refresh_token[:20] + "..." if self.refresh_token else "None"}')

            # Check if we have refresh token
            if not self.refresh_token:
                logger.error(f'🔴 [REFRESH] No refresh token available for server: {self.server_ip}')
                return None

            # Check server URL and decide the service URL for token refresh
            # Handle both with and without protocol prefix
            server_domain = self.server_url.replace('https://', '').replace('http://', '')

            if Config.EMS_API_URL in self.server_url or server_domain == Config.EMS_API_URL:
                refresh_url = self.server_url + "/authorization-service/oauth/refresh-token"
                headers = {
                    'Authorization': AUTHORIZATION_VALUE_GPSTECH,
                    'Cookie': f'access_token={self.access_token}; refresh_token={self.refresh_token}'
                }
                # For gpstech server, send empty payload
                payload = {}
                logger.debug(f'🔄 [REFRESH] Using EMS_API_URL endpoint for {server_domain}')
            elif Config.CYGATE_API_URL in self.server_url or server_domain == Config.CYGATE_API_URL:
                refresh_url = self.server_url + '/user-service/oauth/refresh-token'
                headers = {
                    'Authorization': AUTHORIZATION_VALUE_CYGATE,
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'Cookie': f'access_token={self.access_token}; refresh_token={self.refresh_token}'
                }
                # For other servers, send refresh token as form data
                payload = self.refresh_token
                logger.debug(f'🔄 [REFRESH] Using CYGATE_API_URL endpoint for {server_domain}')
            else:
                logger.error(f'🔴 [REFRESH] Unknown server URL: {self.server_url}')
                logger.error(f'🔴 [REFRESH] Server domain: {server_domain}')
                logger.error(f'🔴 [REFRESH] Config.EMS_API_URL: {Config.EMS_API_URL}')
                logger.error(f'🔴 [REFRESH] Config.CYGATE_API_URL: {Config.CYGATE_API_URL}')
                return None

            logger.debug(f'🔄 [REFRESH] Refresh URL: {refresh_url}')

            # Send request to refresh the token
            response = requests.post(refresh_url, headers=headers, data=payload)
            logger.debug(f'🔄 [REFRESH] Response status: {response.status_code}')
            logger.debug(f'🔄 [REFRESH] Response text: {response.text}')

            if response.status_code == HTTPStatus.OK.value:
                json_data = response.json()
                # logger.debug(f'response: {json_data}')

                # Check if response contains error code
                if json_data.get('code') == '400':
                    logger.error(f"Token refresh failed: {json_data.get('message')}")
                    return None

                data = json_data.get("data")
                if data:
                    self.refresh_token = data.get("refreshToken")
                    self.access_token = data.get("accessToken") 
                    self.clientId = data.get("clientId")
                    self.user_id = data.get('id')

                    # Save the new access token
                    AuthQSettings.get_instance().save_access_token(self.access_token)

                    logger.debug(f"Tokens refreshed successfully. New access token: {self.access_token}")
                    return self.access_token

            # logger.error(f"refresh_access_token: Failed to refresh token. Status: {response.status_code}, Response: {response.text}")

        except Exception as e:
            logger.error(f"🔴 [REFRESH] Exception during token refresh: {str(e)} - {type(e)}")
            logger.error(f"🔴 [REFRESH] Server: {self.server_ip}")
            if Config.DEBUG:
                traceback.print_exc()

        logger.error(f'🔴 [REFRESH] Token refresh failed for server: {self.server_ip}')
        return None

    def get_recordings(self):
        try:
            # get_recordings = self.server_url + "/api/recording"
            if self.is_ip_address:
                get_recordings = self.server_url + "/api/recording"
            else:
                get_recordings = self.server_url + VmsService + "/api/recording"
            headers = self.get_headers()
            response = requests.request("GET", get_recordings,headers=headers)
            if response.status_code == 200:
                logger.debug(f'get_recordings = {response.json()}')
        except Exception as e:
            logger.error(f'get_recordings error = {e}')
        
    def get_stream_url(self,cameraId = None,format = "FLV",isPublic = True,streamIndex = 0):
        try:
            if self.is_ip_address:
                get_rtmp = self.server_url + "/api/stream"
            else:
                get_rtmp = self.server_url + VmsService + "/api/stream"

            # First attempt with current token
            headers = self.get_headers()
            query_params = {}
            if cameraId is not None:
                query_params["cameraId"] = cameraId
            if format is not None:
                query_params["format"] = format
            if isPublic is not None:
                query_params["isPublic"] = isPublic

            logger.debug(f'get_stream_url query_params = {query_params}')

            # Use helper method for token retry logic
            response = self._retry_request_with_token_refresh(
                method="GET",
                url=get_rtmp,
                headers=headers,
                params=query_params,
                context=f"get_stream_url for camera {cameraId}"
            )

            return (response, streamIndex)

        except Exception as e:
            logger.error(f'get_stream_url error = {e}')
        return (None, streamIndex)
    
    def get_ai_stream_url(self,aiFlowId = None,format = "FLV",isPublic = True):
        try:
            if self.is_ip_address:
                url = self.server_url + "/api/stream/url-ai-flow"
            else:
                url = self.server_url + VmsService + "/api/stream/url-ai-flow"

            # First attempt with current token
            headers = self.get_headers()
            query_params = {}
            if aiFlowId is not None:
                query_params["aiFlowId"] = aiFlowId
            if format is not None:
                query_params["format"] = format
            if isPublic is not None:
                query_params["isPublic"] = isPublic

            # Use helper method for token retry logic
            response = self._retry_request_with_token_refresh(
                method="GET",
                url=url,
                headers=headers,
                params=query_params,
                context=f"get_ai_stream_url for aiFlow {aiFlowId}"
            )

            return response

        except Exception as e:
            logger.error(f'get_ai_stream_url error = {e}')
        return None

    def _retry_request_with_token_refresh(self, method, url, headers, params=None, data=None, context="API call"):
        """
        ✅ HELPER: Retry API requests with automatic token refresh on 401 errors

        This method implements the token refresh retry pattern:
        1. Make initial request with current token
        2. If 401 Unauthorized, attempt token refresh
        3. Retry request with new token if refresh successful

        Args:
            method (str): HTTP method (GET, POST, etc.)
            url (str): Request URL
            headers (dict): Request headers
            params (dict, optional): Query parameters
            data (any, optional): Request body data
            context (str): Context description for logging

        Returns:
            requests.Response or None: Response object or None on error
        """
        try:
            # First attempt with current token
            response = requests.request(method, url, headers=headers, params=params, data=data)

            # Check if token expired (401 Unauthorized)
            if response.status_code == 401 and not self.is_ip_address:
                logger.warning(f'🔑 [TOKEN_RETRY] {context} got 401, attempting token refresh')
                logger.debug(f'🔑 [TOKEN_RETRY] Current access_token: {self.access_token[:20] + "..." if self.access_token else "None"}')
                logger.debug(f'🔑 [TOKEN_RETRY] Current refresh_token: {self.refresh_token[:20] + "..." if self.refresh_token else "None"}')

                # Check if we have refresh token before attempting refresh
                if not self.refresh_token:
                    logger.error(f'🔴 [TOKEN_RETRY] No refresh token available for {context}')
                    return response  # Return original 401 response

                # Attempt to refresh token
                refresh_result = self.refresh_access_token()
                logger.debug(f'🔑 [TOKEN_RETRY] Refresh result: {refresh_result}')

                if refresh_result == "REFRESH_TOKEN_EXPIRED":
                    logger.error(f'🔴 [TOKEN_RETRY] Refresh token expired for {context}')
                    return response  # Return original 401 response

                if refresh_result and isinstance(refresh_result, str) and refresh_result != "REFRESH_TOKEN_EXPIRED":
                    logger.info(f'✅ [TOKEN_RETRY] Token refreshed successfully, retrying {context}')
                    logger.debug(f'✅ [TOKEN_RETRY] New access_token: {self.access_token[:20] + "..." if self.access_token else "None"}')

                    # Retry with new token
                    updated_headers = self.get_headers()  # Get updated headers with new token
                    retry_response = requests.request(method, url, headers=updated_headers, params=params, data=data)
                    logger.debug(f'{context} retry response = {retry_response.text}')
                    return retry_response
                else:
                    logger.error(f'❌ [TOKEN_RETRY] Token refresh failed for {context}')
                    logger.error(f'❌ [TOKEN_RETRY] Refresh result was: {refresh_result} (type: {type(refresh_result)})')
                    return response  # Return original 401 response

            return response

        except Exception as e:
            logger.error(f'{context} error = {e}')
            return None

    def login(self, username, password, captcha=None, captcha_id=None):
        try:
            if not self.is_ip_address:
                data = {}  # Initialize data dictionary to avoid UnboundLocalError
                logger.debug(f'server_url = {self.server_url}')
                if Config.EMS_API_URL in self.server_url:
                    # trường hợp là gpstech server
                    log_in_url = f"{self.server_url}/authorization-service/oauth/token"
                    headers = {
                        'Authorization': AUTHORIZATION_VALUE_GPSTECH,
                        'Content-Type': 'application/x-www-form-urlencoded'
                    }
                    data = {
                        'username': username,
                        'password': password,
                    }
                elif Config.CYGATE_API_URL in self.server_url:
                    # trường hợp là cygate server có captcha
                    log_in_url = f"{self.server_url}/user-service/oauth/token"
                    headers = {
                        'Authorization': AUTHORIZATION_VALUE_CYGATE,
                        'Content-Type': 'application/x-www-form-urlencoded'
                    }
                    data = {
                        'username': username,
                        'password': password,
                        'captcha': captcha,
                        'captchaId': captcha_id
                    }
                else:
                    log_in_url = f"{self.server_url}/authorization-service/oauth/token"
                    # trường hợp không phải 1 trong 2 server trên -> không gửi auth vào header
                    headers = {
                        'Content-Type': 'application/x-www-form-urlencoded'
                    }
                    data = {
                        'username': username,
                        'password': password,
                    }

                logger.debug(f'Attempting login: URL={log_in_url}, Headers={headers}, Data={data}')
                response = requests.post(url=log_in_url, headers=headers, data=data, timeout=2)

                if response.status_code == 200:
                    json_data = response.json()
                    logger.debug(f'json_data = {json_data}')
                    data = json_data.get("data")
                    logger.debug(f'data = {data}')
                    self.refresh_token = data.get("refreshToken")
                    self.access_token = data.get("accessToken")
                    self.clientId = data.get("clientId")
                    self.user_id = data.get("id")
                    AuthQSettings.get_instance().save_access_token(self.access_token)
                    logger.debug(f'Successful login. Tokens received. \n \
                                Refresh token: {self.refresh_token} \n \
                                Access token: {self.access_token} \n \
                                User ID: {self.user_id}')
                else:
                    logger.error(f'Login failed: {response.status_code} - {response.text}')

                return response

            else:  # For server IP case - no login required
                response = requests.Response()
                response.status_code = 200
                return response

        except Exception as e:
            if Config.DEBUG:
                traceback.print_exc()

            logger.error(f"Login error: {e} - {type(e)}")
            if isinstance(e, UnboundLocalError):
                logger.debug(f"UnboundLocalError details: {traceback.format_exc()}")
            elif isinstance(e, AttributeError):
                logger.debug(f"Attribute error details: {traceback.format_exc()}")

            response = requests.Response()
            response.status_code = requests.codes.internal_server_error
            response.reason = str(e)
            return response
        
    def refector_server_gpstech(self, ip_server):
        if Config.EMS_API_URL_BACKUP in ip_server:
            # replace ems.gpstech.vn with api.gpstech.vn
            ip_server = ip_server.replace(Config.EMS_API_URL_BACKUP, Config.EMS_API_URL)
        return ip_server
    
    def set_server_info(self, ip_server=Config.SERVER_IP_ADDRESS_DEFAULT, port_server=Config.SERVER_VMS_PORT_DEFAULT, port_websocket=Config.SERVER_EVENT_PORT_DEFAULT):
        ip_server = self.refector_server_gpstech(ip_server)
        self.server_ip = ip_server
        self.server_port = int(port_server)
        self.websocket_port = int(port_websocket)
        self.server_url = f"http://{self.server_ip}:{self.server_port}"
        self.server_event = f"http://{self.server_ip}:{self.websocket_port}"
        self.websocket_url = f"ws://{self.server_ip}:{self.websocket_port}"
        logger.debug(f"Setting server {self.server_url} {self.server_event} {self.websocket_url}")

    # get camera list
    # GET /api/cameras
    # Support get camera list with params
    # id: camera id
    # name: camera name
    # status: camera status
    # urlMainstream: camera url
    # cameraGroupName: camera group name
    # use one or many params to get camera list
    
    def get_cameras(self,activates = True):
        try:
            if self.is_ip_address:
                get_cameras = self.server_url + "/api/cameras"
            else:
                get_cameras = self.server_url + VmsService + "/api/cameras"
            # logger.debug(f"url get_cameras = {get_cameras}")
            headers = self.get_headers()
            query_params = {}
            if activates is not None:
                query_params["activates"] = activates
            # logger.debug(f"url get_cameras1 = {get_cameras, headers, query_params}")
            response = requests.request("GET", get_cameras,headers=headers,params=query_params)
            return response

        except Exception as e:
            logger.error(f'get_cameras error = {e}')
        return None
    
    def get_devices_tree(self):
        try:
            if self.is_ip_address:
                url = self.server_url + "/api/cameragroups/devices-tree"
            else:
                url = self.server_url + VmsService + "/api/cameragroups/devices-tree"
            # logger.debug(f"url get_cameras = {get_cameras}")
            headers = self.get_headers()
            response = requests.request("GET", url,headers=headers)
            # logger.debug(f'get_cameras = {response.text}')
            return response

        except Exception as e:
            logger.error(f'get_cameras error = {e}')
        return None
    
    def get_camera(self,ids = None):
        try:
            if self.is_ip_address:
                get_camera = self.server_url + "/api/cameras"
            else:
                get_camera = self.server_url + VmsService + "/api/cameras"
            headers = self.get_headers()
            query_params = {}
            if ids is not None:
                query_params["ids"] = ids
            response = requests.request("GET", get_camera,headers=headers,params=query_params)
            return response

        except Exception as e:
            logger.debug(f'get_cameras error = {e}')
        return None
    

    # create camera
    # POST /api/cameras
    # Support create camera with params
    # data: data from Camera Model.to_dict()
    def create_camera(self, data):
        try:
            if self.is_ip_address:
                create_camera = self.server_url + "/api/cameras"
            else:
                create_camera = self.server_url + VmsService + "/api/cameras"
            headers = self.get_headers()
            logger.debug(f"create_camera = {data}")
            json_data = json.dumps(data)
            response = requests.request("POST", create_camera,headers=headers, data=json_data)
            logger.debug(f'create_camera response = {response.text}')
            return response
        except Exception as e:
            logger.error(f'create_camera error = {e}')
        return None
    
    def add_camera_to_group(self, data):
        try:
            if self.is_ip_address:
                url = self.server_url + "/api/cameragroups/add-cameras"
            else:
                url = self.server_url + VmsService + "/api/cameragroups/add-cameras"
            headers = self.get_headers()
            logger.debug(f"add_camera_to_group = {data}")
            json_data = json.dumps(data)
            response = requests.request("POST", url,headers=headers, data=json_data)
            logger.debug(f'add_camera_to_group response = {response.text}')
            return response
        except Exception as e:
            logger.error(f'add_camera_to_group error = {e}')
        return None

    def add_discovered_camera(self, data):
        list_ids = [camera["id"] for camera in data]
        try:
            if self.is_ip_address:
                url = self.server_url + "/api/cameras/discovery/add"
            else:
                url = self.server_url + VmsService + "/api/cameras/discovery/add"
            headers = self.get_headers()
            json_data = {"ids": list_ids}
            response = requests.request("POST", url,headers=headers, json=json_data)
            return response
            # print(f'add_discovered_camera = {data}')
            # return None
        except Exception as e:
            logger.error(f'add_discovered_camera error = {e}')
        return None
    
    def discovered_camera(self, data):
        try:
            if self.is_ip_address:
                url = self.server_url + "/api/discovered-camera"
            else:
                url = self.server_url + VmsService + "/api/discovered-camera"
            headers = self.get_headers()
            logger.debug(f"discovered_camera = {data}")
            json_data = json.dumps(data)
            response = requests.request("PATCH", url,headers=headers, data=json_data)
            logger.debug(f'discovered_camera response = {response.text}')
            return response
        except Exception as e:
            logger.error(f'create_camera error = {e}')
        return None
    
    def callback_create_camera(self, parent=None, callback=None, data=None):
        thread_create_camera = APIThread(
            parent=parent, target=self.create_camera, callback=callback, args=(data,))
        thread_create_camera.start()
    # create camera
    # POST /api/cameras
    # Support create camera with params
    # data: data from Camera Model.to_dict()

    def create_cameras(self, data):
        if self.is_ip_address:
            create_cameras = self.server_url + "/api/cameras/create-cameras"
        else:
            create_cameras = self.server_url + VmsService + "/api/cameras/create-cameras"
        # logger.debug("create_camera: ", create_cameras, " - data: ", data)
        json_data = json.dumps(data)
        headers = self.get_headers()
        response = requests.request("POST", create_cameras,headers=headers, data=json_data)
        return response

    def callback_create_cameras(self, parent=None, callback=None, data=None):
        thread_create_cameras = APIThread(
            parent=parent, target=self.create_cameras, callback=callback, args=(data,))
        thread_create_cameras.start()
    # delete camera list
    # DELETE /api/cameras
    # Support delete camera list with params
    # id: camera id
    # name: camera name
    # status: camera status
    # urlMainstream: camera url
    # cameraGroupName: camera group name
    # use one or many params to delete camera list

    def delete_camera(
            self, id=None, urlMainstream=None, serviceIds=None, activate=None
    ):
        try:
            if self.is_ip_address:
                url = self.server_url + "/api/cameras"
            else:
                url = self.server_url + VmsService + "/api/cameras"
            headers = self.get_headers()
            # data = {}
            query_params = {}
            if id is not None:
                query_params["ids"] = id
            logger.info(f"delete_camera = {url,headers,query_params}")
            response = requests.request("DELETE", url,headers=headers,params=query_params)
            logger.info(f"delete_camera = {response.text}")
            if response.status_code == 204:
                # logger.debug(f'delete_camera = {response.json()}')
                return response
            else:
                return None
        except Exception as e:
            logger.error(f"delete_camera error = {e}")
    

    # update camera
    # PUT /api/cameras
    # Support update camera with params
    # data: data from Camera Model.to_dict()

    def update_camera_by_put(self, data):
        if data is None:
            return
        if self.is_ip_address:
            update_camera_by_put = self.server_url + "/api/cameras"
        else:
            update_camera_by_put = self.server_url + VmsService + "/api/cameras"
        headers = self.get_headers()
        
        response = requests.request(
            method="PUT", url=update_camera_by_put, headers=headers, json=data)
        return response 
    

    # create group
    # POST /api/cameragroups
    # Support create group with params
    # data: data from CameraGroup Model.to_dict()

    def create_group(self, data):
        if data is None:
            return

        if self.is_ip_address:
            create_group = self.server_url + "/api/cameragroups"
        else:
            create_group = self.server_url + VmsService + "/api/cameragroups"
        headers = self.get_headers()
        json_data = json.dumps(data)
        logger.debug(f"create_group: {json_data} - headers: {headers}")
        response = requests.request("POST", create_group,headers=headers, data=json_data)
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                return response
        # logger.debug(f"create_group: {response.text}")
        return None

    def callback_create_group(self, parent=None, callback=None, data=None):
        # logger.debug(f'callback_create_group = {data}')
        thread_create_group = APIThread(
            parent=parent, target=self.create_group, callback=callback, args=(data,))
        thread_create_group.start()

    def get_groups(self,ids = None):
        try:
            if self.is_ip_address:
                get_groups = self.server_url + "/api/cameragroups"
            else:
                get_groups = self.server_url + VmsService + "/api/cameragroups"
            headers = self.get_headers()
            query_params = {}
            if ids is not None:
                query_params["ids"] = ids
            response = requests.request("GET", get_groups,headers=headers,params=query_params,timeout=2)
            # logger.debug(f'get_groups ={get_groups} {response.status_code}')
            if response.status_code == 200:
                pass
                # logger.debug(f'get_groups = {response.json()}')
            return response

        except Exception as e:
            logger.error(f'get_groups error = {e}')
            return None

    def delete_group(
            self,
            id=None,
            names=None,
            descriptions=None,
    ):

        if self.is_ip_address:
            delete_group = self.server_url + "/api/cameragroups"
        else:
            delete_group = self.server_url + VmsService + "/api/cameragroups"
        query_params = {}
        if id is not None:
            query_params["ids"] = id
        if names is not None:
            query_params["names"] = names
        if descriptions is not None:
            query_params["descriptions"] = descriptions
        
        logger.debug(f"delete_group: {delete_group} - data: {query_params}")
        headers = self.get_headers()
        response = requests.request("DELETE", delete_group,headers=headers,params=query_params)
        logger.debug(f"delete_group: {response}")
        return response

    def update_group_py_put(self, data):
        if data is None:
            return
        if self.is_ip_address:
            update_group = self.server_url + "/api/cameragroups"
        else:
            update_group = self.server_url + VmsService + "/api/cameragroups"
        headers = self.get_headers()
        
        response = requests.request(
            method="PUT", url=update_group, headers=headers, json=data)
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                return response
        return None

    def create_aiflows(self, data):
        if self.is_ip_address:
            create_aiflows = self.server_url + "/api/aiflows"
        else:
            create_aiflows = self.server_url + VmsService + "/api/aiflows"
        # convert data to json
        logger.debug(f"\n\n create_aiflows: {data}\n\n")
        if data is None:
            return
        headers = self.get_headers()
        json_data = json.dumps(data)
        response = requests.request("POST", create_aiflows,headers=headers, data=json_data)
        # logger.debug(f"response create_aiflows = {response.text}")
        return response
    
    def apply_ai_flow(self, data):
        if self.is_ip_address:
            url = self.server_url + "/api/cameragroups/apply-ai-flow"
        else:
            url = self.server_url + VmsService + "/api/cameragroups/apply-ai-flow"
        # convert data to json
        
        if data is None:
            return
        headers = self.get_headers()
        json_data = json.dumps(data)
        logger.debug(f"apply_ai_flow: {url} - header: {headers} - data: {json_data} \n\n")
        response = requests.request("POST", url,headers=headers, data=json_data)
        logger.debug(f"response apply_ai_flow1 = {response.text}")
        return response
    
    def apply_ai_flow_camera(self, data):
        if self.is_ip_address:
            url = self.server_url + "/api/cameras/apply-ai-flow"
        else:
            url = self.server_url + VmsService + "/api/cameras/apply-ai-flow"
        # convert data to json
        
        if data is None:
            return
        headers = self.get_headers()
        json_data = json.dumps(data)
        logger.debug(f"apply_ai_flow_camera: {url} - header: {headers} - data: {json_data} \n\n")
        response = requests.request("POST", url,headers=headers, data=json_data)
        logger.debug(f"response apply_ai_flow_camera = {response.text}")
        return response
    
    # get aiflows list
    # GET /api/aiflows
    # Support get aiflows list with params
    # id: camera id
    # use one or many params to get aiflows list
    def get_aiflows(self,id = None,cameraIds = None,cameraGroupIds = None, applies = None):

        try:
            if self.is_ip_address:
                get_aiflows = self.server_url + "/api/aiflows"
            else:
                get_aiflows = self.server_url + VmsService + "/api/aiflows"
            query_params = {}
            if id is not None:
                query_params["ids"] = id
            if cameraIds is not None:
                query_params["cameraIds"] = cameraIds
            if cameraGroupIds is not None:
                query_params["cameraGroupIds"] = cameraGroupIds
            if applies is not None:
                query_params["applies"] = applies
            headers = self.get_headers()
            # logger.debug(f"Get aiflows = {query_params}")
            # json_data = json.dumps(query_params)
            logger.debug(f'get_aiflows: {get_aiflows} - headers: {headers} - query_params: {query_params}')
            response = requests.request("GET", get_aiflows, headers=headers, params=query_params,timeout=10)
            if response.status_code == 200:
                pass
                # logger.debug(f'get_aiflows = {response.json()}')
            # logger.debug(f"response = {response.text}")
            return response
        
        except Exception as e:
            logger.error(f'get_aiflows error = {e}')

    def get_ai_flow_and_type(self, cameraId = None, type = None):
        try:
            if self.is_ip_address:
                get_ai_flow_and_type = self.server_url + "/api/aiflows/get-by-camera-and-type"
            else:
                get_ai_flow_and_type = self.server_url + VmsService + "/api/aiflows/get-by-camera-and-type"
            query_params = {}
            if cameraId is not None:
                query_params["cameraId"] = cameraId
            if type is not None:
                query_params["type"] = type
            headers = self.get_headers()
            logger.debug(f"get_ai_flow_and_type= {query_params} - {get_ai_flow_and_type} - \n {headers}")
            response = requests.request("GET", get_ai_flow_and_type, headers=headers, params=query_params)
            # print(f"response.text= {response.text}")
            if response.status_code == 200:
                pass
            return response
        except Exception as e:
            logger.error(f'get_ai_flow_and_type error = {e}')
            return None
    # VideoPlayBack
    def get_videos(self, ids: str = None, dateFrom: str= None, dateTo: str= None, cameraIds: str= None):
        try:
            logger.info(f"get_videos= {cameraIds}")
            get_videos = self.get_url("/api/recording")
            query_params = {}
            if ids is not None:
                query_params["ids"] = ids
            if dateFrom is not None:
                query_params["dateFrom"] = dateFrom
            if dateTo is not None:
                query_params["dateTo"] = dateTo
            if cameraIds is not None:
                query_params["cameraIds"] = cameraIds
            # if detail is not None:
            #     query_params["detail"] = detail
            headers = self.get_headers()
            logger.info(f"get_videos1= {query_params} - {get_videos} - {headers}")
            # json_data = json.dumps(query_params)
            response = requests.request("GET", get_videos,headers=headers,params=query_params)
            # logger.info(f"response get_videos = {response.text}")
            return response,dateFrom,dateTo
        
        except Exception as e:
            logger.info(f'get_aiflows error = {e}')
            return None,dateFrom,dateTo
        
    def export_video(self, data = None):
        # data["start"] = "2025-07-07T03:05:31.191Z"
        # data["end"] = "2025-07-07T03:05:50.913Z"
        url = self.get_url("/api/recording/export")
        json_data = json.dumps(data)
        headers = self.get_headers()
        # logger.debug(f'export_video = {url,headers,json_data}')
        try:
            with requests.post(url, headers=headers, data=json_data, stream=True) as resp:
                logger.debug(f'export_video = {resp.status_code}')

                if resp.status_code != 200:
                    return None
                for line in resp.iter_lines():
                    if line:
                        decoded = line.decode('utf-8')
                        if decoded.startswith("data:"):
                            json_str = decoded[len("data:"):]  # Bỏ prefix "data:"
                            data_dict = json.loads(json_str)
                            return data_dict
                return None
                        
        except Exception as e:
            logger.error(f'start_subcribe exception = {e}')
            
            return None
        
    def get_video_encoder_configurations(self, index = 0, cameraId = None):
        try:
            url = self.get_url("/api/onvif/media-service/get-video-encoder-configurations")
            query_params = {}
            if index is not None:
                query_params["index"] = index
            if cameraId is not None:
                query_params["cameraId"] = cameraId
            json_data = json.dumps(query_params)
            headers = self.get_headers()
            logger.debug(f'get_video_encoder_configurations = {url,headers,query_params}')
            # response = requests.request("GET", url,headers=headers,data=json_data)
            try:
                with requests.get(url, headers=headers, data=json_data, stream=True, timeout=30) as resp:
                    logger.debug(f'start_subcribe response = {resp.status_code}')

                    if resp.status_code != 200:
                        return None
                    for line in resp.iter_lines():
                        if line:
                            decoded = line.decode('utf-8')
                            if decoded.startswith("data:"):
                                json_str = decoded[len("data:"):]  # Bỏ prefix "data:"
                                data_dict = json.loads(json_str)
                                return data_dict
                            # logger.debug(f"Received line: {decoded}")
                            
                            
            except Exception as e:
                logger.error(f'start_subcribe exception = {e}')
                return None

        
        except Exception as e:
            logger.debug(f'get_aiflows error = {e}')
            return None  
        
    def set_video_encoder_configurations(self, data):

        url = self.get_url("/api/onvif/media-service/set-video-encoder-configuration")
        json_data = json.dumps(data)
        headers = self.get_headers()
        logger.debug(f'set_video_encoder_configurations = {url,headers,json_data}')
        try:
            with requests.post(url, headers=headers, data=json_data, stream=True, timeout=30) as resp:
                logger.debug(f'set_video_encoder_configurations = {resp.status_code}')

                if resp.status_code != 200:
                    return None
                for line in resp.iter_lines():
                    if line:
                        logger.debug(f"Received line: {line}")
                        
                        
        except Exception as e:
            logger.error(f'start_subcribe exception = {e}')
            return None

         
    def get_recording_schedule(self, ids = None, cameraIds = None):
        try:
            url = self.get_url("/api/camera-record-setting")
            query_params = {}
            if ids is not None:
                query_params["ids"] = ids
            if cameraIds is not None:
                query_params["cameraIds"] = cameraIds
            headers = self.get_headers()
            response = requests.request("GET", url,headers=headers,params=query_params)
            return response
        
        except Exception as e:
            logger.debug(f'get_aiflows error = {e}')
            return None
        
    def update_recording_schedule_by_patch(self,data = None):
        url = self.get_url("/api/camera-record-setting")
        payload = json.dumps(data)
        headers = self.get_headers()
        logger.debug(f"update_recording_schedule_by_patch {data}")
        response = requests.request("PATCH", url, headers=headers, data=payload)
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                data = response.json()
                return response
            else:
                logger.debug(f"update_recording_schedule_by_patch error {response.text}")
        return None
    
    def create_recording_schedule(self, data = None):
        url = self.get_url("/api/camera-record-setting")
        # convert data to json
        logger.debug(f"\n\n create_recording_schedule: {data}\n\n")
        headers = self.get_headers()
        json_data = json.dumps(data)

        response = requests.request("POST", url,headers=headers, data=json_data)

        return response

    def start_subcribe(self, data=None,callback = None):
        url = self.get_url("/api/recording/start-subcribe")
        headers = {
            'Content-Type': 'application/json',
            'Accept': '*/*',
            'Id': '123',
            'Cookie': f'access_token={self.access_token}; refresh_token={self.refresh_token}',
        }
        json_data = json.dumps(data)

        logger.debug(f'start_subcribe = {url, headers, data}')

        try:
            with requests.post(url, headers=headers, data=json_data, stream=True, timeout=30) as resp:
                logger.debug(f'start_subcribe response = {resp.status_code}')

                if resp.status_code != 200:
                    logger.debug(f"start_subcribe failed: {resp.status_code}")
                    return self.retry_subcribe(data)
                logger.debug(f'start_subcribe response = {resp.iter_lines()}')
                for line in resp.iter_lines():
                    if line:
                        decoded = line.decode('utf-8')
                        logger.debug(f"Received line: {decoded}")
                        
                        
        except Exception as e:
            logger.error(f'start_subcribe exception = {e}')
            # return self.retry_subcribe(data)

    def retry_subcribe(self, data, delay=1):
        import time
        logger.debug(f"Retrying start_subcribe in {delay} seconds...")
        time.sleep(delay)
        self.start_subcribe(data)

    def stop_subcribe(self, data = None):
        url = self.get_url("/api/recording/stop-subcribe")
        headers = self.get_headers()
        json_data = json.dumps(data)
        headers = {
                'Content-Type': 'application/json',
                'Accept': '*/*',
                'Id': '123',
                'Cookie': f'access_token={self.access_token}; refresh_token={self.refresh_token}',
                }

        logger.debug(f"start_subcribe: {url,headers,json_data}")
        response = requests.request("POST", url,headers=headers, data=json_data)
        logger.debug(f"get_recording_schedule1 = {response.text}")
    #     # return response

    def get_videos_thread(self,parent = None, ids = None, dateFrom = None, dateTo = None,cameraIds = None,detail = False,callback = None):
        get_videos_thread = APIThread(
            parent=parent, target=self.get_videos, callback=callback, args=(ids,dateFrom,dateTo,cameraIds,detail,))
        get_videos_thread.start()

    # update camera
    # PUT /api/aiflows
    # Support update aiflows with params
    # data: data from AiFlows Model.to_dict()

    def update_aiflows(self, data):
        if data is None:
            return None
        if self.is_ip_address:
            update_aiflows = self.server_url + "/api/aiflows"
        else:
            update_aiflows = self.server_url + VmsService + "/api/aiflows"
        headers = self.get_headers()
        json_data = json.dumps(data)
        response = requests.request("PATCH", update_aiflows,headers=headers,data=json_data)
        if response.status_code == 200:
            pass
        logger.debug(f'response update_aiflows = {response.text}')
        return response

    def delete_aiflows(
            self, ids = None):
        if self.is_ip_address:
            delete_aiflows = self.server_url + "/api/aiflows"
        else:
            delete_aiflows = self.server_url + VmsService + "/api/aiflows"
        logger.debug(f"delete_aiflows: {delete_aiflows} - ids: {ids}")
        data = {}
        if ids is not None:
            data["ids"] = ids 
        headers = self.get_headers()
        response = requests.request("DELETE", delete_aiflows,headers=headers,params=data)
        return response

    # get event list
    # start_time
    # end_time
    # /api/cctv/get/
    def get_events(self, dateFrom: str, dateTo: str, page: int, size: int, cameraId: str,cameraIds: List[str], groupId: str, groupCameraIds: List[str],name:str,isWarningConfig:int,type:str,status:str):
        try:
            # get_events = self.server_event + "/api/events"
            url = self.server_url + '/ems-service/event'
            # logger.debug(f'get_events: url = {url}')
            query_params = {}
            # if start_time is not None:
            #     query_params["dateFrom"] = start_time
            # if end_time is not None:
            #     query_params["dateTo"] = end_time
            if dateFrom is not None:
                query_params["dateFrom"] = dateFrom
            if dateTo is not None:
                query_params["dateTo"] = dateTo
            if page is not None:
                query_params["page"] = page
            if size is not None:
                query_params["size"] = size
            if cameraId is not None:
                query_params["cameraId"] = cameraId
            if cameraIds is not None:
                query_params["cameraIds"] = cameraIds
            if groupId is not None:
                query_params["groupId"] = groupId
            if groupCameraIds is not None:
                query_params["groupCameraIds"] = groupCameraIds
            if name is not None:
                query_params["name"] = name
            if isWarningConfig is not None:
                query_params["isWarningConfig"] = isWarningConfig
            if type is not None:
                query_params["type"] = type
            if status is not None:
                query_params["status"] = status
            headers = self.get_headers()
            response = requests.get(url, headers=headers, params=query_params)

            if response is not None:
                if response.status_code == HTTPStatusCode.OK.value:
                    return response
                else:
                    logger.debug(f"get_events {response.text}")
            return None
        except Exception as e:
            if Config.DEBUG:
                traceback.print_exc()
            logger.error(f"get_events error: {e}")
            response = requests.Response()
            response.status_code = requests.codes.internal_server_error
            response.reason = str(e)
            return response

    # Thread get cameras
    def get_events_callback(self,parent= None, dateFrom: str = None, dateTo: str = None, page: int = None, size: int = None, cameraId: str = None,cameraIds: List[str] = None, groupId: str = None, groupCameraIds: List[str] = None,name:str = None,isWarningConfig:int = None, type: str = None, status: str = None, callback = None):
        get_events_thread = APIThread(
            parent=parent, target=self.get_events, callback=callback, args=(dateFrom, dateTo, page, size, cameraId, cameraIds, groupId, groupCameraIds,name,isWarningConfig,type,status))
        get_events_thread.start()

    # ZONE API
    # get /api/polygons

    def get_zones(self, ids: List[str] = None, type=None, aiFlowIds:List[int]=None):
        if self.is_ip_address:
            get_zones = self.server_url + "/api/polygons"
        else:
            get_zones = self.server_url + VmsService + "/api/polygons"
        query_params = {}
        if aiFlowIds is not None:
            query_params["aiFlowIds"] = aiFlowIds
        if ids is not None:
            query_params["ids"] = ids
        if type is not None:
            query_params["type"] = type
        headers = self.get_headers()
        logger.debug(f"get_zones = {query_params}")
        response = requests.request("GET", get_zones,headers=headers,params=query_params)
        return response
    
    def update_zones(self, data):
        if data is None:
            return
        if self.is_ip_address:
            update_zones = self.server_url + "/api/polygons"
        else:
            update_zones = self.server_url + VmsService + "/api/polygons"
        headers = self.get_headers()
        logger.debug(f"update_zones = {data}")
        response = requests.request(
            method="PATCH", url=update_zones, headers=headers, json=data)
        return response

    def create_zones(self, data):
        if data is None:
            return
        if self.is_ip_address:
            create_zones = self.server_url + "/api/polygons"
        else:
            create_zones = self.server_url + VmsService + "/api/polygons"
        headers = self.get_headers()
        json_data = json.dumps(data)
        logger.debug(f"create_zones = {json_data}")
        response = requests.request("POST", create_zones,headers=headers, data=json_data)
        return response
    
    def add_polygons_to_aiflow(self, data):
        """Add polygons to an AI flow.
        
        Args:
            data: Dictionary containing:
                - id: str (AI flow ID)
                - ids: List[str] (List of polygon IDs)
        """
        if self.is_ip_address:
            url = self.server_url + "/api/aiflows/add-polygons"
        else:
            url = self.server_url + VmsService + "/api/aiflows/add-polygons"
        
        headers = self.get_headers()
        json_data = json.dumps(data)
        logger.debug(f"add_polygons_to_aiflow: {json_data}")
        response = requests.request("POST", url, headers=headers, data=json_data)
        return response

    def delete_zones(self, id=None, cameraIds=None, type=None):
        if self.is_ip_address:
            delete_zones = self.server_url + "/api/polygons"
        else:
            delete_zones = self.server_url + VmsService + "/api/polygons"
        data = {}
        if id is not None:
            data["ids"] = id
        if cameraIds is not None:
            data["cameraIds"] = cameraIds
        if type is not None:
            data["type"] = type
        headers = self.get_headers()
        logger.debug(f"delete_zones = {data}")
        response = requests.request("DELETE", delete_zones,headers=headers,params=data)
        return response
    
    def search_single(self, data=None, signal = None):
        url = self.get_url("/api/cameras/discovery/single")
        headers = self.get_headers()
        # headers = {
        #     'Content-Type': 'application/json',
        #     'Accept': '*/*',
        #     'Id': '123',
        #     'Cookie': f'access_token={self.access_token}; refresh_token={self.refresh_token}',
        # }
        json_data = json.dumps(data)

        logger.info(f'search_single = {url, headers, data}')

        try:
            with requests.post(url, headers=headers, data=json_data, stream=True) as resp:
                logger.debug(f'search_single response = {resp.status_code}')

                if resp.status_code != 200:
                    logger.debug(f"search_single failed: {resp.status_code}")
                
                for line in resp.iter_lines():
                    if line:
                        logger.debug(f'search_single response = {line}')
                        decoded = line.decode('utf-8')
                        if decoded.startswith("data:"):
                            json_str = decoded[len("data:"):]  # Bỏ prefix "data:"
                            data_dict = json.loads(json_str)
                            signal.emit(data_dict)
                        
        except Exception as e:
            logger.error(f'search_single exception = {e}')
            # return self.retry_subcribe(data)
        logger.debug(f'search_single done')
        signal.emit("Done")
        
    def search_camera_thread(self, parent=None, callback=None, data=None):
        get_groups_thread = APIThread(
            parent=parent, target=self.search_cameras_to_add, callback=callback, args=(data,))
        get_groups_thread.start()

    def search_cameras_to_add(self, data=None):
        if data is None:
            return
        # search_camera_url = 'http://192.168.1.145:58080/api/cameras/discovery'
        # search_camera_url = self.get_url("/api/cameras/discovery")
        url = self.get_url("/api/cameras/discovery/single")
        headers = self.get_headers()
        logger.debug(f"search_cameras_to_add = {url,headers}")
        json_data = json.dumps(data)

        response = requests.request("POST", url, headers=headers, data=json_data)
        return response

    # API Onvif
    def get_onvif_profiles(self,cameraId = None):
        try:
            if self.is_ip_address:
                url = self.server_url + "/api/onvif/media-service/get-profiles"
            else:
                url = self.server_url + VmsService + "/api/onvif/media-service/get-profiles"
            logger.debug(f"get_onvif_profiles = {url}")
            query_params = {}
            if cameraId is not None:
                query_params["cameraId"] = cameraId
            headers = self.get_headers()
            response = requests.get(url, headers=headers, params=query_params)    
            if response is not None:
                if response.status_code == HTTPStatusCode.OK.value:
                    return response
                else:
                    return None
            return None
        
        except Exception as e:
            logger.debug(f"Exception = {e}")

    def ptz_stop(self,cameraId = None):
        try:
            if self.is_ip_address:
                url = self.server_url + "/api/onvif/ptz-service/stop"
            else:
                url = self.server_url + VmsService + "/api/onvif/ptz-service/stop"
            query_params = {}
            if cameraId is not None:
                query_params["cameraId"] = cameraId
            query_params["pantilt"] = True
            query_params["zoom"] = True
            headers = self.get_headers()
            data = json.dumps(query_params)
            response = requests.request("POST",url, headers=headers, data=data)  
            if response is not None:
                if response.status_code == HTTPStatusCode.OK.value:
                    return response
                else:
                    return None
            return None
        
        except Exception as e:
            logger.debug(f"Exception = {e}")

    def ptz_relative_move(self,cameraId = None,x = 0, y = 0, zoom = 0, speedX = 1, speedY = 1, speedZoom = 1):
        try:
            if self.is_ip_address:
                url = self.server_url + "/api/onvif/ptz-service/relative-move"
            else:
                url = self.server_url + VmsService + "/api/onvif/ptz-service/relative-move"
            # logger.debug(f"ptz_relative_move = {url}")
            query_params = {}
            if cameraId is not None:
                query_params["cameraId"] = cameraId
            if x is not None:
                query_params["x"] = x
            if y is not None:
                query_params["y"] = y
            if zoom is not None:
                query_params["zoom"] = zoom
            if speedX is not None:
                query_params["speedX"] = speedX
            if speedY is not None:
                query_params["speedY"] = speedY
            if speedZoom is not None:
                query_params["speedZoom"] = speedZoom
            headers = self.get_headers()
            data = json.dumps(query_params)
            response = requests.request("POST",url, headers=headers, data=data)    
            if response is not None:
                if response.status_code == HTTPStatusCode.OK.value:
                    return response
                else:
                    return None
            return None
        
        except Exception as e:
            logger.debug(f"Exception = {e}")

    def ptz_continuous_move(self,cameraId = None,x = 0,y = 0,zoom = 0):
        try:
            if self.is_ip_address:
                url = self.server_url + "/api/onvif/ptz-service/continuous-move"
            else:
                url = self.server_url + VmsService + "/api/onvif/ptz-service/continuous-move"
            # logger.debug(f"ptz_continuous_move = {url}")
            query_params = {}
            if cameraId is not None:
                query_params["cameraId"] = cameraId
            if x is not None:
                query_params["x"] = x
            if y is not None:
                query_params["y"] = y
            if zoom is not None:
                query_params["zoom"] = zoom  
            query_params["timeout"] = "PT20S"     
            headers = self.get_headers()
            data = json.dumps(query_params)
            # logger.debug(f"ptz_continuous_move = {url,data}")
            response = requests.request("POST",url, headers=headers, data=data,timeout=1)    
        except Exception as e:
            logger.debug(f"Exception = {e}")  

    def ptz_get_status(self,cameraId = None):
        try:
            if self.is_ip_address:
                url = self.server_url + "/api/onvif/ptz-service/get-status"
            else:
                url = self.server_url + VmsService + "/api/onvif/ptz-service/get-status"
            query_params = {}
            if cameraId is not None:
                query_params["cameraId"] = cameraId
            headers = self.get_headers()
            # logger.debug(f'ptz_get_status = {url,headers,query_params}') 

            try:
                with requests.get(url, headers=headers, data=json.dumps(query_params), timeout=30) as resp:
                    logger.debug(f'start_subcribe response = {resp.status_code}')

                    if resp.status_code != 200:
                        logger.debug(f"start_subcribe failed: {resp.status_code}")
                        # return self.retry_subcribe(data)
                        return None
                    logger.debug(f'start_subcribe response = {resp.iter_lines()}')
                    for line in resp.iter_lines():
                        if line:
                            decoded = line.decode('utf-8')
                            if decoded.startswith("data:"):
                                json_str = decoded[len("data:"):]  # Bỏ prefix "data:"
                                data_dict = json.loads(json_str)
                                return data_dict
            except Exception as e:
                logger.error(f'start_subcribe exception = {e}')

        
        except Exception as e:
            logger.debug(f"Exception = {e}")

    def get_onvif_stream_url(self,cameraId = None):
        try:
            url = self.get_url("/api/onvif/media-service/get-stream-url")
            logger.debug(f"get_onvif_stream_url = {url}")
            query_params = {}
            if cameraId is not None:
                query_params["cameraId"] = cameraId
            headers = self.get_headers()
            response = requests.get(url, headers=headers, params=query_params)    
            if response is not None:
                if response.status_code == HTTPStatusCode.OK.value:
                    return response
                else:
                    return None
            return None
        
        except Exception as e:
            logger.debug(f"Exception = {e}")

    # API Virtual Window
    def get_tabmodel(self,ids = None,names = None,types = None,currentGrids = None,listGridIds = None,listGridCustomDatas = None):
        try:
            url = self.get_url("/api/tab")
            query_params = {}
            if ids is not None:
                query_params["ids"] = ids
            if names is not None:
                query_params["names"] = names
            if types is not None:
                query_params["types"] = types
            if currentGrids is not None:
                query_params["currentGrids"] = currentGrids
            if listGridIds is not None:
                query_params["listGridIds"] = listGridIds
            if listGridCustomDatas is not None:
                query_params["listGridCustomDatas"] = listGridCustomDatas
            headers = self.get_headers()
            response = requests.get(url, headers=headers, params=query_params,timeout=2)    
            if response is not None:
                if response.status_code == HTTPStatusCode.OK.value:
                    return response
                else:
                    return None
            return None
        
        except Exception as e:
            logger.debug(f"Exception = {e}")

    def delete_tabmodel(self,ids = None,names = None,types = None):
        try:
            url = self.get_url("/api/tab")
            query_params = {}
            if ids is not None:
                query_params["ids"] = ids
            if names is not None:
                query_params["names"] = names
            if types is not None:
                query_params["types"] = types
            headers = self.get_headers()
            response = requests.request("DELETE", url,headers=headers,params=query_params) 
            if response is not None:
                if response.status_code == HTTPStatusCode.OK.value:
                    return response
                else:
                    return None
            return None
        
        except Exception as e:
            logger.debug(f"Exception = {e}")

    def create_tabmodel(self,tab = None):
        url = self.get_url("/api/tab")
        payload = json.dumps(tab)
        headers = self.get_headers()
        response = requests.request("POST", url, headers=headers, data=payload)
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                data = response.json()
                logger.info(f"create_tabmodel1 {data}")
                return response
            else:
                logger.info(f"create_tabmodel2 {response.text}")
        return None

    def update_tabmodel_by_put(self,tab = None):
        logger.debug(f"update_tabmodel_by_put {tab}")
        url = self.get_url("/api/tab")

        payload = json.dumps(tab)
        headers = self.get_headers()

        response = requests.request("PUT", url, headers=headers, data=payload)
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                data = response.json()
                logger.debug(f"update_tabmodel_by_put {data}")
                return response
            else:
                logger.debug(f"update_tabmodel_by_put {response.text}")
        return None

    def get_shortcut_key(self,ids = None,startKeys = None,shortcutIds = None,names = None,treeTypes = None):
        try:
            url = self.get_url("/api/shortcut-key")
            query_params = {}
            if ids is not None:
                query_params["ids"] = ids
            if startKeys is not None:
                query_params["startKeys"] = startKeys
            if shortcutIds is not None:
                query_params["shortcutIds"] = shortcutIds
            if names is not None:
                query_params["names"] = names
            if treeTypes is not None:
                query_params["treeTypes"] = treeTypes
            headers = self.get_headers()
            response = requests.get(url, headers=headers, params=query_params,timeout=2)    
            
            if response is not None:
                if response.status_code == HTTPStatusCode.OK.value:
                    return response
                else:
                    return None
            return None
        
        except Exception as e:
            logger.debug(f"Exception = {e}")

    def delete_shortcut_key(self,ids = None,startKeys = None,shortcutIds = None,names = None,treeTypes = None):
        try:
            url = self.get_url("/api/shortcut-key")
            query_params = {}
            if ids is not None:
                query_params["ids"] = ids
            if startKeys is not None:
                query_params["startKeys"] = startKeys
            if shortcutIds is not None:
                query_params["shortcutIds"] = shortcutIds
            if names is not None:
                query_params["names"] = names
            if treeTypes is not None:
                query_params["treeTypes"] = treeTypes
            headers = self.get_headers()
            response = requests.request("DELETE", url,headers=headers,params=query_params) 
            if response is not None:
                if response.status_code == HTTPStatusCode.OK.value:
                    return response
                else:
                    return None
            return None
        
        except Exception as e:
            logger.debug(f"Exception = {e}")

    def create_shortcut_key(self,shortcut_key = None):
        url = self.get_url("/api/shortcut-key")

        payload = json.dumps(shortcut_key)
        headers = self.get_headers()
        response = requests.request("POST", url, headers=headers, data=payload)
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                # data = response.json()
                # logger.debug(f"get_events {data}")
                # if page_idx == data['pageable']['pageNumber']:
                #     return response
                # else:
                #     return None
                return response
            else:
                logger.debug(f"get_events {response.text}")
        return None

    def update_shortcut_key_by_put(self,shortcut_key = None):
        url = self.get_url("/api/shortcut-key")
        payload = json.dumps(shortcut_key)
        headers = self.get_headers()

        response = requests.request("PUT", url, headers=headers, data=payload)
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                # data = response.json()
                # logger.debug(f"get_events {data}")
                # if page_idx == data['pageable']['pageNumber']:
                #     return response
                # else:
                #     return None
                return response
            else:
                logger.debug(f"update_shortcut_key_by_put {response.text}")
        return None
    # Map ####################################################################
    def get_map(self):
        try:
            url = self.get_url("/api/map")
            query_params = {}
            headers = self.get_headers()
            response = requests.get(url, headers=headers, params=query_params,timeout=2)    
            
            if response is not None:
                if response.status_code == HTTPStatusCode.OK.value:
                    return response
                else:
                    return None
            return None
        
        except Exception as e:
            logger.debug(f"Exception = {e}")

    def add_camera_to_map(self,id = None,ids = None):
        url = self.get_url("/api/map/add-cam")
        query_params = {}
        if id is not None:
            query_params["id"] = id
        if ids is not None:
            query_params["ids"] = ids

        payload = json.dumps(query_params)
        headers = self.get_headers()
        logger.debug(f'add_camera_to_map = {url,headers,payload}')
        response = requests.request("POST", url, headers=headers, data=payload)
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                return response
            else:
                logger.debug(f"get_events {response.text}")
        return None 
    
    def remove_camera_from_map(self,id = None,ids = None):
        url = self.get_url("/api/map/remove-cam")
        query_params = {}
        if id is not None:
            query_params["id"] = id
        if ids is not None:
            query_params["ids"] = ids

        payload = json.dumps(query_params)
        headers = self.get_headers()
        logger.debug(f'remove_camera_from_map = {url,headers,payload}')
        response = requests.request("POST", url, headers=headers, data=payload)
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                return response
            else:
                logger.debug(f"get_events {response.text}")
        return None 
    
    def add_building_to_map(self,id = None,ids = None):
        url = self.get_url("/api/map/add-building")
        query_params = {}
        if id is not None:
            query_params["id"] = id
        if ids is not None:
            query_params["ids"] = ids

        payload = json.dumps(query_params)
        headers = self.get_headers()
        logger.debug(f'add_building_to_map = {id,ids}')
        response = requests.request("POST", url, headers=headers, data=payload)
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                return response
            else:
                logger.debug(f"get_events {response.text}")
        return None
    
    def update_map_by_patch(self,id = None,cameraIds = None,buildingIds = None):
        url = self.get_url("/api/map")
        query_params = {}
        if id is not None:
            query_params["id"] = id
        if cameraIds is not None:
            query_params["cameraIds"] = cameraIds
        if buildingIds is not None:
            query_params["buildingIds"] = buildingIds
        payload = json.dumps(query_params)
        headers = self.get_headers()
        response = requests.request("PATCH", url, headers=headers, data=payload)
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                return response
            else:
                logger.debug(f"get_events {response.text}")
        return None
    
    def get_floors(self,ids = None,names = None,fileLinks = None,cameraIds = None, buildingIds = None):
        try:
            url = self.get_url("/api/floor")
            query_params = {}
            if ids is not None:
                query_params["ids"] = ids
            if names is not None:
                query_params["names"] = names
            if fileLinks is not None:
                query_params["fileLinks"] = fileLinks
            if cameraIds is not None:
                query_params["cameraIds"] = cameraIds
            headers = self.get_headers()
            response = requests.get(url, headers=headers, params=query_params,timeout=2)    
            if response is not None:
                if response.status_code == HTTPStatusCode.OK.value:
                    return response
                else:
                    return None
            return None
        except Exception as e:
            logger.debug(f"Exception = {e}")

    def create_floor(self,data = None):
        url = self.get_url("/api/floor")
        payload = json.dumps(data)
        headers = self.get_headers()
        logger.debug(f'create_floor = {url,headers,payload}')
        response = requests.request("POST", url, headers=headers, data=payload)
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                return response
            else:
                logger.info(f"create_floor {response.text}")
        return None
    
    def add_camera_on_floor(self,data = None):
        url = self.get_url("/api/floor/add-cam")
        payload = json.dumps(data)
        headers = self.get_headers()
        response = requests.request("POST", url, headers=headers, data=payload)
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                return response
            else:
                logger.info(f"add_camera_on_floor error: {response.text}")
        return None
    
    def remove_camera_on_floor(self,data = None):
        url = self.get_url("/api/floor/remove-cam")
        payload = json.dumps(data)
        headers = self.get_headers()
        response = requests.request("POST", url, headers=headers, data=payload)
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                return response
            else:
                logger.debug(f"remove_camera_on_floor {response.text}")
        return None

    def delete_floor(self,ids = None):
        url = self.get_url("/api/floor")
        query_params = {}
        if ids is not None:
            query_params["ids"] = ids
        payload = json.dumps(query_params)
        headers = self.get_headers()
        logger.debug(f'delete_floor = {payload}')
        response = requests.request("DELETE", url, headers=headers, params=query_params)
        if response is not None:
            if response.status_code == 204:
                return response
            else:
                logger.info(f"delete_floor {response.text}")
        return None
    
    def getBuildings(self,ids = None,names = None):
        try:
            url = self.get_url("/api/building")
            query_params = {}
            if ids is not None:
                query_params["ids"] = ids
            if names is not None:
                query_params["names"] = names
            headers = self.get_headers()
            response = requests.get(url, headers=headers, params=query_params,timeout=2)    
            if response is not None:
                if response.status_code == HTTPStatusCode.OK.value:
                    return response
                else:
                    return None
            return None
        except Exception as e:
            logger.debug(f"Exception = {e}")

    def create_building(self,data = None):
        url = self.get_url("/api/building")
        payload = json.dumps(data)
        headers = self.get_headers()
        response = requests.request("POST", url, headers=headers, data=payload)
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                return response
            else:
                logger.debug(f"get_events {response.text}")
        return None
    
    def update_building_by_put(self,data = None):
        url = self.get_url("/api/building")
        payload = json.dumps(data)
        headers = self.get_headers()
        response = requests.request("PUT", url, headers=headers, data=payload)
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                return response
            else:
                logger.debug(f"get_events {response.text}")
        return None
    
    def update_building_by_patch(self,id = None,name = None,data = None):
        url = self.get_url("/api/building")
        payload = json.dumps(data)
        headers = self.get_headers()
        response = requests.request("PATCH", url, headers=headers, data=payload)
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                return response
            else:
                logger.debug(f"get_events {response.text}")
        return None
    
    def delete_building(self,ids = None):
        url = self.get_url("/api/building")
        query_params = {}
        if ids is not None:
            query_params["ids"] = ids
        payload = json.dumps(query_params)
        headers = self.get_headers()
        logger.debug(f'delete_building = {payload}')
        response = requests.request("DELETE", url, headers=headers, params=query_params)
        logger.debug(f'delete_building = {response.status_code}')
        if response is not None:
            if response.status_code == 204:
                return response
            else:
                logger.debug(f"get_events {response.text}")
        return None
    
    def add_floor_to_building(self,id = None,ids = None):
        url = self.get_url("/api/building/add-floor")
        query_params = {}
        if id is not None:
            query_params["id"] = id
        if ids is not None:
            query_params["ids"] = ids

        payload = json.dumps(query_params)
        headers = self.get_headers()
        logger.debug(f'add_floor_to_building = {url,headers,payload}')
        response = requests.request("POST", url, headers=headers, data=payload)
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                return response
            else:
                logger.debug(f"get_events {response.text}")
        return None 
    
    def update_list_cameras_by_put(self,data = None):
        # logger.info(f"update_list_cameras_by_put2 {data}")
        url = self.get_url("/api/cameras/update-list")
        payload = json.dumps(data)
        headers = self.get_headers()
        response = requests.request("PUT", url, headers=headers, data=payload)
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                return response
            else:
                logger.info(f"update_list_cameras_by_put3 {response.text} - {data}")
        return None
    
    def update_file(self,filePath = None):
        url = self.get_url("/api/storage/upload")
        payload = {}
        files = [self.create_file_tuple(filePath)]
        if self.is_ip_address:
            headers = {
                # 'Content-Type': 'application/json',
                'Accept': '*/*',
                'Id': '123',
                'Authorities': 'VMS_ADMIN',
            }
        else:
            headers = {
                'Accept': '*/*',
                'Authorization': f'Bearer {self.access_token}',
                'Cookie': f'access_token={self.access_token}; refresh_token={self.refresh_token}',
                }
        logger.debug(f'update_file = {url,headers,payload,files}')
        response = requests.request("POST", url, headers=headers, data=payload, files=files)
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                return response
            else:
                logger.info(f"update_file error {response.text}")
        return None
    
    def create_file_tuple(self,file_path):
        file_name = os.path.basename(file_path)
        mime_type, _ = mimetypes.guess_type(file_path)
        if mime_type is None:
            mime_type = 'application/octet-stream'
        return ('file', (file_name, open(file_path, 'rb'), mime_type))
    
    def delete_map(self,ids = None,names = None,descriptions = None,listCameraMapItems = None,images = None):
        try:
            url = self.get_url("/api/map-item")
            query_params = {}
            if ids is not None:
                query_params["ids"] = ids
            if names is not None:
                query_params["names"] = names
            if descriptions is not None:
                query_params["descriptions"] = descriptions
            if listCameraMapItems is not None:
                query_params["listCameraMapItems"] = listCameraMapItems
            if images is not None:
                query_params["images"] = images
            headers = self.get_headers()
            response = requests.request("DELETE", url,headers=headers,params=query_params) 
            if response is not None:
                if response.status_code == HTTPStatusCode.OK.value:
                    return response
                else:
                    return None
            return None
        
        except Exception as e:
            logger.debug(f"Exception = {e}")


    # API AI Box
    def create_discovery_in_group(self,data = None):
        url = self.get_url("/api/cameragroups/discovery-info")
        payload = json.dumps(data)
        headers = self.get_headers()

        response = requests.request("POST", url, headers=headers, data=payload)
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                # data = response.json()
                # logger.debug(f"get_events {data}")
                # if page_idx == data['pageable']['pageNumber']:
                #     return response
                # else:
                #     return None
                return response
            else:
                logger.debug(f"create_discovery_in_group {response.text}")
        return None
    
    def get_discovery_in_group(self,cameraGroupId = None):
        try:
            url = self.get_url("/api/cameragroups/discovery-cameras")
            query_params = {}
            if cameraGroupId is not None:
                query_params["cameraGroupId"] = cameraGroupId
            headers = self.get_headers()
            response = requests.get(url, headers=headers, params=query_params)    
            
            if response is not None:
                if response.status_code == HTTPStatusCode.OK.value:
                    return response
                else:
                    return None
            return None
        
        except Exception as e:
            logger.debug(f"Exception = {e}")

    def create_camera_in_group(self,data = None):
        url = self.get_url("/api/cameras/add-aibox")
        payload = json.dumps(data)
        headers = self.get_headers()

        response = requests.request("POST", url, headers=headers, data=payload)
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                data = response.json()
                logger.debug(f"create_camera_in_group {data}")
                # if page_idx == data['pageable']['pageNumber']:
                #     return response
                # else:
                #     return None
                return response
            else:
                logger.debug(f"create_discovery_in_group {response.text}")
        return None
    # API Gate

    def create_door(self,data = None):
        url = self.get_url("/api/door")
        payload = json.dumps(data)
        headers = self.get_headers()

        response = requests.request("POST", url, headers=headers, data=payload)
        if response is not None:
            if response.status_code == HTTPStatusCode.OK.value:
                data = response.json()
                logger.debug(f"create_door {data}")
                # if page_idx == data['pageable']['pageNumber']:
                #     return response
                # else:
                #     return None
                return response
            else:
                logger.debug(f"create_door {response.text}")
        return None
    def get_doors(self,ids = None):
        try:
            url = self.get_url("/api/door")
            query_params = {}
            if ids is not None:
                query_params["ids"] = ids

            headers = self.get_headers()
            response = requests.get(url, headers=headers, params=query_params,timeout=2)    
            
            if response is not None:
                if response.status_code == HTTPStatusCode.OK.value:
                    return response
                else:
                    return None
            return None
        
        except Exception as e:
            logger.debug(f"Exception = {e}")

    def update_door_by_put(self, data):
        if data is None:
            return
        if self.is_ip_address:
            url = self.server_url + "/api/door"
        else:
            url = self.server_url + VmsService + "/api/door"
        headers = self.get_headers()
        
        response = requests.request(
            method="PUT", url=url, headers=headers, json=data)
        return response    
      
    def delete_door(self, ids=None):
        try:
            if self.is_ip_address:
                url = self.server_url + "/api/door"
            else:
                url = self.server_url + VmsService + "/api/door"
            headers = self.get_headers()
            query_params = {}
            if ids is not None:
                query_params["ids"] = ids
            response = requests.request("DELETE", url,headers=headers,params=query_params)
            if response.status_code == 204:
                return response
            else:
                return None
        except Exception as e:
            logger.error(f"delete_camera error = {e}")
    # Permissions
    def get_all_users(self):
        try:
            all_users_url = f"{self.permissions_url}/user?size=0"
            headers = {
                'Accept': 'application/json',
                'Authorization': f'Bearer {self.access_token}',
                'Cookie': f'access_token={self.access_token}; refresh_token={self.refresh_token}'
            }
            response = requests.get(url=all_users_url, headers=headers,timeout=2)
            if response.status_code == 200:
                pass
            return response
        except Exception as e:
            if Config.DEBUG:
                traceback.print_exc()
            logger.error(f"get_all_users error: {e} - type : {type(e)} - e.name:")
            if isinstance(e, AttributeError):
                logger.error(f'get_all_users: object has no attribute')
            response = requests.Response()
            response.status_code = requests.codes.internal_server_error
            response.reason = str(e)
            return response

    def get_all_roles(self):
        try:
            all_roles_url = f"{self.permissions_url}/role?size=0"
            headers = {
                'Accept': 'application/json',
                'Authorization': f'Bearer {self.access_token}',
                'Cookie': f'access_token={self.access_token}; refresh_token={self.refresh_token}'
            }
            response = requests.get(url=all_roles_url, headers=headers,timeout=2)
            if response.status_code == 200:
                pass
            return response
        except Exception as e:
            if Config.DEBUG:
                traceback.print_exc()
            logger.error(f"get_all_roles error: {e} - type : {type(e)} - e.name:")
            if isinstance(e, AttributeError):
                logger.error(f'get_all_roles: object has no attribute')
            response = requests.Response()
            response.status_code = requests.codes.internal_server_error
            response.reason = str(e)
            return response

    def get_all_menu_function(self):
        try:
            function_menu_url = f"{self.permissions_url}/menu"
            headers = {
                'Accept': 'application/json',
                'Authorization': f'Bearer {self.access_token}',
                'Cookie': f'access_token={self.access_token}; refresh_token={self.refresh_token}'
            }
            response = requests.get(url=function_menu_url, headers=headers,timeout=2)
            if response.status_code == 200:
                pass
            return response
        except Exception as e:
            if Config.DEBUG:
                traceback.print_exc()
            logger.error(f"get_all_menu_function error: {e} - type : {type(e)} - e.name:")
            if isinstance(e, AttributeError):
                logger.error(f'get_all_menu_function: object has no attribute')
            response = requests.Response()
            response.status_code = requests.codes.internal_server_error
            response.reason = str(e)
            return response

    def get_all_profile_group(self):
        try:
            profile_group_url = f"{self.server_url}/ems-service/groups"
            headers = {
                'Accept': 'application/json',
                'Authorization': f'Bearer {self.access_token}',
                'Cookie': f'access_token={self.access_token}; refresh_token={self.refresh_token}'
            }
            response = requests.get(url=profile_group_url, headers=headers,timeout=2)
            if response.status_code == 200:
                pass
            return response
        except Exception as e:
            if Config.DEBUG:
                traceback.print_exc()
            logger.error(f"get_all_profile_group error: {e} - type : {type(e)} - e.name:")
            if isinstance(e, AttributeError):
                logger.error(f'get_all_profile_group: object has no attribute')
            response = requests.Response()
            response.status_code = requests.codes.internal_server_error
            response.reason = str(e)
            return response

    def get_user_in_role(self, role_id):
        try:
            user_in_role_url = f"{self.server_url}/user-service/user/get-user-in-role?roleId={role_id}"
            headers = {
                'Accept': 'application/json',
                'Authorization': f'Bearer {self.access_token}',
                'Cookie': f'access_token={self.access_token}; refresh_token={self.refresh_token}'
            }
            response = requests.get(url=user_in_role_url, headers=headers)
            if response.status_code == 200:
                pass
            return response
        except Exception as e:
            if Config.DEBUG:
                traceback.print_exc()
            logger.error(f"get_user_in_role error: {e} - type : {type(e)} - e.name:")
            if isinstance(e, AttributeError):
                logger.error(f'get_user_in_role: object has no attribute')
            response = requests.Response()
            response.status_code = requests.codes.internal_server_error
            response.reason = str(e)
            return response

    def get_role_infor(self, role_id):
        try:
            role_infor_url = f"{self.server_url}/user-service/role/{role_id}"
            headers = {
                'Accept': 'application/json',
                'Authorization': f'Bearer {self.access_token}',
                'Cookie': f'access_token={self.access_token}; refresh_token={self.refresh_token}'
            }
            response = requests.get(url=role_infor_url, headers=headers)
            if response.status_code == 200:
                pass
            return response
        except Exception as e:
            if Config.DEBUG:
                traceback.print_exc()
            logger.error(f"get_role_infor error: {e} - type : {type(e)} - e.name:")
            if isinstance(e, AttributeError):
                logger.error(f'get_role_infor: object has no attribute')
            response = requests.Response()
            response.status_code = requests.codes.internal_server_error
            response.reason = str(e)
            return response

    def get_user_roles_api(self, user_id):
        try:
            url = f'{self.server_url}/user-service/user-role?userId={user_id}'
            headers = {
                'Accept': 'application/json',
                'Authorization': f'Bearer {self.access_token}',
                'Cookie': f'access_token={self.access_token}; refresh_token={self.refresh_token}'
            }
            response = requests.get(url=url, headers=headers)
            if response.status_code == 200:
                pass
            return response
        except Exception as e:
            logger.error(f"get_role_infor error: {e} - type : {type(e)} - e.name:")
            response = requests.Response()
            response.status_code = requests.codes.internal_server_error
            response.reason = str(e)
            return response

    def upload_file_thread(self, parent=None, callback=None, data=None):
        thread_create_group = APIThread(
            parent=parent, target=self.upload_file_service, callback=callback, args=(data,))
        thread_create_group.start()

    def upload_file_service(self, file):
        if file is None:
            return
        try:
            upload_file_url = f'{self.server_url}/upload'
            headers = {
                'Accept': 'application/json',
                'Authorization': f'Bearer {self.access_token}',
                'Cookie': f'access_token={self.access_token}; refresh_token={self.refresh_token}'
            }
            response = requests.post(url=upload_file_url, files={'file': file['file']}, headers=headers, data={'rootPath': file['rootPath']})
            if response.status_code == 200:
                pass
            return response

        except Exception as e:
            if Config.DEBUG:
                traceback.print_exc()
            logger.error(f"create_new_user error: {e} - type : {type(e)} - e.name:")
            if isinstance(e, AttributeError):
                logger.error(f'create_new_user: object has no attribute')
            return None

    def create_new_user_thread(self, parent=None, callback=None, data=None):
        thread_create_new_user = APIThread(
            parent=parent, target=self.create_new_user_api, callback=callback, args=(data,))
        thread_create_new_user.start()

    def create_new_user_api(self, data):
        if data is None:
            return
        try:
            create_user_url = f"{self.server_url}/user-service/user"
            headers = {
                'Accept': 'application/json',
                'Authorization': f'Bearer {self.access_token}',
                'Cookie': f'access_token={self.access_token}; refresh_token={self.refresh_token}'
            }
            response = requests.request("POST", url=create_user_url, json=data, headers=headers)
            if response.status_code == 200:
                pass
            return response
        except Exception as e:
            if Config.DEBUG:
                traceback.print_exc()
            logger.error(f"create_new_user error: {e} - type : {type(e)} - e.name:")
            if isinstance(e, AttributeError):
                logger.error(f'create_new_user: object has no attribute')
            response = requests.Response()
            response.status_code = requests.codes.internal_server_error
            response.reason = str(e)
            return response

    def update_user_information_thread(self, parent=None, callback=None, data=None):
        thread_update_user_information = APIThread(
            parent=parent, target=self.update_user_information_api, callback=callback, args=(data,))
        thread_update_user_information.start()

    def update_user_information_api(self, data):
        if data is None:
            return
        try:
            create_user_url = f"{self.server_url}/user-service/user"
            headers = {
                'Accept': 'application/json',
                'Authorization': f'Bearer {self.access_token}',
                'Cookie': f'access_token={self.access_token}; refresh_token={self.refresh_token}'
            }
            response = requests.request("PUT", url=create_user_url, json=data, headers=headers)
            if response.status_code == 200:
                pass
            return response
        except Exception as e:
            if Config.DEBUG:
                traceback.print_exc()
            logger.error(f"update_user_information error: {e} - type : {type(e)} - e.name:")
            if isinstance(e, AttributeError):
                logger.error(f'update_user_information: object has no attribute')
            response = requests.Response()
            response.status_code = requests.codes.internal_server_error
            response.reason = str(e)
            return response

    def delete_single_user_api(self, user_id):
        try:
            url = f'{self.server_url}/user-service/user/{user_id}'
            headers = {
                'Accept': 'application/json',
                'Authorization': f'Bearer {self.access_token}',
                'Cookie': f'access_token={self.access_token}; refresh_token={self.refresh_token}'
            }
            response = requests.request("DELETE", url=url, headers=headers)
            if response.status_code == 200:
                pass
            return response
        except Exception as e:
            logger.error(f"get_role_infor error: {e} - type : {type(e)} - e.name:")
            response = requests.Response()
            response.status_code = requests.codes.internal_server_error
            response.reason = str(e)
            return response

    def create_new_role_api(self, data):
        if data is None:
            return
        try:
            create_role_url = f"{self.server_url}/user-service/role"
            headers = {
                'Accept': 'application/json',
                'Authorization': f'Bearer {self.access_token}',
                'Cookie': f'access_token={self.access_token}; refresh_token={self.refresh_token}'
            }
            response = requests.request("POST", url=create_role_url, json=data, headers=headers)
            if response.status_code == 200:
                pass
            return response
        except Exception as e:
            if Config.DEBUG:
                traceback.print_exc()
            logger.error(f"create_new_role_api error: {e} - type : {type(e)} - e.name:")
            if isinstance(e, AttributeError):
                logger.error(f'create_new_role_api: object has no attribute')
            response = requests.Response()
            response.status_code = requests.codes.internal_server_error
            response.reason = str(e)
            return response

    def update_role_information_api(self, data):
        if data is None:
            return
        try:
            update_role_url = f"{self.server_url}/user-service/role"
            headers = {
                'Accept': 'application/json',
                'Authorization': f'Bearer {self.access_token}',
                'Cookie': f'access_token={self.access_token}; refresh_token={self.refresh_token}'
            }
            response = requests.request("PUT", url=update_role_url, json=data, headers=headers)
            if response.status_code == 200:
                pass
            return response
        except Exception as e:
            if Config.DEBUG:
                traceback.print_exc()
            logger.error(f"update_role_information_api error: {e} - type : {type(e)} - e.name:")
            if isinstance(e, AttributeError):
                logger.error(f'update_role_information_api: object has no attribute')
            response = requests.Response()
            response.status_code = requests.codes.internal_server_error
            response.reason = str(e)
            return response

    def delete_single_role_api(self, role_id):
        try:
            url = f'{self.server_url}/user-service/role/{role_id}'
            headers = {
                'Accept': 'application/json',
                'Authorization': f'Bearer {self.access_token}',
                'Cookie': f'access_token={self.access_token}; refresh_token={self.refresh_token}'
            }
            response = requests.request("DELETE", url=url, headers=headers)
            if response.status_code == 200:
                pass
            return response
        except Exception as e:
            logger.error(f"delete_single_role_api error: {e} - type : {type(e)} - e.name:")
            response = requests.Response()
            response.status_code = requests.codes.internal_server_error
            response.reason = str(e)
            return response

    def get_current_user_info(self):
        try:
            url = f'{self.server_url}/user-service/user/get-user-info'
            headers = {
                'Accept': 'application/json',
                'Authorization': f'Bearer {self.access_token}',
                'Cookie': f'access_token={self.access_token}; refresh_token={self.refresh_token}'
            }
            response = requests.request("GET", url=url, headers=headers)
            if response.status_code == 200:
                pass
            return response
        except Exception as e:
            logger.error(f"LOI ROI")
            return

    def check_user_information_service(self, data):
        try:
            url = f'{self.server_url}/user-service/user/check?{data}'
            headers = {
                'Accept': 'application/json',
                'Authorization': f'Bearer {self.access_token}',
                'Cookie': f'access_token={self.access_token}; refresh_token={self.refresh_token}'
            }
            response = requests.request("GET", url=url, headers=headers)
            if response.status_code == 200:
                pass
            return response
        except Exception as e:
            logger.error(f"LOI ROI")
            return

    # API Avigilon
    def forward_avigilon_ptz(self, cameraId = None, endPoint = "/camera/commands/pan-tilt-zoom",requestData = None):
        try:
            if self.is_ip_address:
                url = self.server_url + "/api/third-party/avigilon/ptz"
            else:
                url = self.server_url + VmsService + "/api/third-party/avigilon/ptz"

            query_params = {}
            if cameraId is not None:
                query_params["cameraId"] = cameraId
            if endPoint is not None:
                query_params["endPoint"] = endPoint
            if requestData is not None:
                query_params["requestData"] = str(requestData)
            headers = self.get_headers()
            # headers = {
            # 'Content-Type': 'application/json',
            # 'Accept': '*/*',
            # 'Id': '123'
            # }
            data = json.dumps(query_params)
            logger.debug(f'\nforward_avigilon_ptz\n url = {url}\n headers = {headers}\n data = {data}')
            response = requests.request("PUT",url, headers=headers, data=data)    
            if response is not None:
                if response.status_code == HTTPStatusCode.OK.value:
                    return response
                else:
                    logger.debug(f'response.text = {response.text}')
                    return None
            return None
        
        except Exception as e:
            logger.debug(f"Exception = {e}")

    def get_address_from_coord(self, lat, lon):
        try:
            osm_get_address_url = f"https://nominatim.openstreetmap.org/reverse?lat={lat}&lon={lon}&format=json"
            headers = {
                'Accept': 'application/json',
                "User-Agent": "iVMS"
            }
            response = requests.request("GET", url=osm_get_address_url, headers=headers)
            if response.status_code == 200:
                pass
            return response
        except Exception as e:
            if Config.DEBUG:
                traceback.print_exc()
            logger.error(f"create_new_role_api error: {e} - type : {type(e)} - e.name:")
            if isinstance(e, AttributeError):
                logger.error(f'create_new_role_api: object has no attribute')
            response = requests.Response()
            response.status_code = requests.codes.internal_server_error
            response.reason = str(e)
            return response

    def get_map_info_by_keyword(self, detail_address, search_for_coord: False):
        try:
            if self.is_ip_address:
                osm_get_coord_url = self.server_url + "/api/maps/info-by-keyword"
            else:
                osm_get_coord_url = self.server_url + VmsService + "/api/maps/info-by-keyword"
            params = {
                "detail_address": detail_address,
                "search_for_coord": search_for_coord
            }
            headers = {
                'Accept': 'application/json',
                "User-Agent": "iVMS"
            }
            response = requests.request("GET", url=osm_get_coord_url, headers=headers)
            if response.status_code == 200:
                pass
            return response
        except Exception as e:
            if Config.DEBUG:
                traceback.print_exc()
            logger.error(f"create_new_role_api error: {e} - type : {type(e)} - e.name:")
            if isinstance(e, AttributeError):
                logger.error(f'create_new_role_api: object has no attribute')
            response = requests.Response()
            response.status_code = requests.codes.internal_server_error
            response.reason = str(e)
            return response

    def add_zones_to_aiflow(self, data):
        """Add zones to an AI flow.
        
        Args:
            data: Dictionary containing:
                - id: str (AI flow ID)
                - ids: List[str] (List of zone IDs)
        """
        if self.is_ip_address:
            url = self.server_url + "/api/aiflows/add-polygons"
        else:
            url = self.server_url + VmsService + "/api/aiflows/add-polygons"
        
        headers = self.get_headers()
        json_data = json.dumps(data)
        logger.debug(f"add_zones_to_aiflow: {json_data}")
        response = requests.request("POST", url, headers=headers, data=json_data)
        return response

    def apply_aiflow_to_camera(self, data):
        """Apply AI flow to a camera.
        
        Args:
            data: Dictionary containing:
                - id: str (Camera ID)
                - apply: bool
                - type: str (e.g. "RECOGNITION")
        """
        if self.is_ip_address:
            url = self.server_url + "/api/cameras/apply-ai-flow"
        else:
            url = self.server_url + VmsService + "/api/cameras/apply-ai-flow"
        
        headers = self.get_headers()
        json_data = json.dumps(data)
        logger.debug(f"apply_aiflow_to_camera: {json_data}")
        response = requests.request("POST", url, headers=headers, data=json_data)
        return response

    def process_zone_aiflow_batch(self, polygons_data, aiflow_id, camera_id, aiflow_type="RECOGNITION"):
        """Process batch creation of zones and apply them to an AI flow.
        
        Args:
            polygons_data: List of dictionaries containing polygon data
            aiflow_id: str (AI flow ID)
            camera_id: str (Camera ID)
            aiflow_type: str (Type of AI flow, default "RECOGNITION")
            
        Returns:
            TypeRequestVMS: Status of the operation
            
        Raises:
            Exception with specific error details for each API failure
        """
        try:
            # Step 1: Create all zones
            zone_ids = []
            for polygon_data in polygons_data:
                response = self.create_zones(polygon_data)
                if response.status_code != 200:
                    error_msg = f"Failed to create zone: {response.text}"
                    logger.error(error_msg)
                    return TypeRequestVMS.CREATE_ZONES, response
                
                zone_data = response.json()
                if "id" in zone_data:
                    zone_ids.append(zone_data["id"])
                else:
                    error_msg = "Zone created but no ID returned"
                    logger.error(error_msg)
                    return TypeRequestVMS.CREATE_ZONES, response

            # Step 2: Add zones to AI flow
            if zone_ids:
                add_zones_data = {
                    "id": aiflow_id,
                    "ids": zone_ids
                }
                response = self.add_zones_to_aiflow(add_zones_data)
                if response.status_code != 200:
                    error_msg = f"Failed to add zones to AI flow: {response.text}"
                    logger.error(error_msg)
                    return TypeRequestVMS.ADD_ZONES_TO_AIFLOW, response

            # Step 3: Apply AI flow to camera
            apply_data = {
                "id": camera_id,
                "apply": True,
                "type": aiflow_type
            }
            response = self.apply_aiflow_to_camera(apply_data)
            if response.status_code != 200:
                error_msg = f"Failed to apply AI flow to camera: {response.text}"
                logger.error(error_msg)
                return TypeRequestVMS.APPLY_AIFLOW, response

            # All operations successful
            return None, None

        except Exception as e:
            logger.error(f"Unexpected error in process_zone_aiflow_batch: {str(e)}")
            if Config.DEBUG:
                traceback.print_exc()
            raise e

    def process_zone_aiflow_batch_async(self, parent=None, callback=None, polygons_data=None, aiflow_id=None, camera_id=None, aiflow_type="RECOGNITION"):
        """Asynchronously process batch creation of zones and apply them to an AI flow.
        
        Args:
            parent: Parent widget/window for the thread
            callback: Callback function to handle the result
            polygons_data: List of dictionaries containing polygon data
            aiflow_id: str (AI flow ID)
            camera_id: str (Camera ID)
            aiflow_type: str (Type of AI flow, default "RECOGNITION")
        """
        if not callback:
            logger.error("Callback function is required for async operation")
            return
            
        if not all([polygons_data, aiflow_id, camera_id]):
            logger.error("Missing required parameters for batch processing")
            return
            
        thread = APIThread(
            parent=parent,
            target=self.process_zone_aiflow_batch,
            callback=callback,
            args=(polygons_data, aiflow_id, camera_id, aiflow_type)
        )
        thread.start()

    def callback_process_zone_aiflow_batch(self, parent=None, callback=None, polygons_data=None, aiflow_id=None, camera_id=None, aiflow_type="RECOGNITION"):
        """Alias for process_zone_aiflow_batch_async to maintain consistency with other callback methods.
        
        This method follows the naming convention of other callback methods in the class.
        """
        return self.process_zone_aiflow_batch_async(
            parent=parent,
            callback=callback,
            polygons_data=polygons_data,
            aiflow_id=aiflow_id,
            camera_id=camera_id,
            aiflow_type=aiflow_type
        )

class HTTPStatusCode(enum.Enum):
    OK = 200
    CREATED = 201
    NO_CONTENT = 204
    BAD_REQUEST = 400
    UNAUTHORIZED = 401
    FORBIDDEN = 403
    NOT_FOUND = 404
    INTERNAL_SERVER_ERROR = 500

class TypeRequestVMS(enum.Enum):
    CREATE_CAMERA = "CREATE_CAMERA"
    CREATE_CAMERAS = "CREATE_CAMERAS"
    UPDATE_CAMERA = "UPDATE_CAMERA"
    DELETE_CAMERA = "DELETE_CAMERA"
    CREATE_CAMERA_GROUP = "CREATE_CAMERA_GROUP"
    UPDATE_CAMERA_GROUP = "UPDATE_CAMERA_GROUP"
    DELETE_CAMERA_GROUP = "DELETE_CAMERA_GROUP"
    CREATE_AIFLOWS = "CREATE_AIFLOWS"
    UPDATE_AIFLOWS = "UPDATE_AIFLOWS"
    WARNING_AIFLOWS = "WARNING_AIFLOWS"
    DELETE_AIFLOWS = "DELETE_AIFLOWS"
    GET_WARNING_ALERT_SETTING = "GET_WARNING_ALERT_SETTING"
    UPDATE_WARNING_ALERT_SETTING = "UPDATE_WARNING_ALERT_SETTING"
    UPDATE_MAP_SUCCESS = "UPDATE_MAP_SUCCESS"
    UPDATE_MAP_FAILED = "UPDATE_MAP_FAILED"
    CREATE_NEW_USER = "CREATE_NEW_USER"
    CREATE_NEW_ROLE = "CREATE_NEW_ROLE"
    DELETE_USER = "DELETE_USER"
    DELETE_ROLE = "DELETE_ROLE"
    UPDATE_USER = "UPDATE_USER"
    UPDATE_ROLE = "UPDATE_ROLE"
    CREATE_SHORTCUTID = "CREATE_SHORTCUTID"
    UPDATE_SHORTCUTID = "UPDATE_SHORTCUTID"
    DELETE_SHORTCUTID = "DELETE_SHORTCUTID"
    # Zone and AI Flow batch processing types
    CREATE_ZONES = "CREATE_ZONES"
    ADD_ZONES_TO_AIFLOW = "ADD_ZONES_TO_AIFLOW"
    APPLY_AIFLOW = "APPLY_AIFLOW"


class AuthenticatedRequest(enum.Enum):
    LOGOUT = "LOGOUT"
    CHANGE_SERVER = "CHANGE_SERVER"
    INVALID_REFRESH_TOKEN = "INVALID_REFRESH_TOKEN"


class LogErrorMessage:
    # example error message
    # "type": "CAMERA_STATUS_NULL",
    # "message": "Camera status is null",
    # "data": null,
    # "timestamp": "2023-04-20T09:18:35.331+00:00",
    # "status": 400,
    # "error": "Bad Request",
    # "exception": "com.cerberus.vms.exception.BadRequestException: Camera status is null"
    def __init__(self, response):
        caller_frame = inspect.currentframe().f_back
        caller_function = inspect.getframeinfo(caller_frame).function
        logger.debug(
            f"LogErrorMessage: created by function: {caller_function}")
        self.type = ErrorType.UNKNOWN
        self.message = ""
        self.data = None
        self.timestamp = ""
        self.status = HTTPStatusCode.OK
        self.error = ""
        self.exception = ""
        if response is not None:
            try:
                logger.debug(f"response: {response.json()}")
                if response.status_code == HTTPStatusCode.BAD_REQUEST.value:
                    self.type = "Bad Request"
                    self.message = "Lost connection to server, please check your network or server status"
                    data = response.json()
                    if data['type'] is not None:
                        self.type = data['type']
                    if data['message'] is not None:
                        self.message = data['message']
                        if data['data'] is not None:
                            error_message = ErrorType.get_error_message(
                                data['data'])
                            if error_message is not None:
                                self.message = error_message
                elif response.status_code == HTTPStatusCode.UNAUTHORIZED.value:
                    self.type = "Your session has expired"
                    self.message = "Please login again to continue using the application"
                elif response.status_code == HTTPStatusCode.INTERNAL_SERVER_ERROR.value:
                    self.type = "INTERNAL SERVER ERROR"
                    self.message = "Lost connection to server, please check your network or server status"
            except Exception as e:
                if Config.DEBUG:
                    traceback.print_exc()
                logger.error(f"LogErrorMessage: {str(e)}")
                self.type = ErrorType.UNKNOWN
                self.message = str(e)
                self.data = None
                self.timestamp = ""
                self.status = HTTPStatusCode.OK
                self.error = ""
                self.exception = ""

    # get message string
    def get_full_message(self):
        return (
            "- type: "
            + str(self.type)
            + "\n - message: "
            + str(self.message)
            + "\n - data: "
            + str(self.data)
            + "\n - timestamp: "
            + str(self.timestamp)
            + "\n - status: "
            + str(self.status)
            + "\n - error: "
            + str(self.error)
            + "\n - exception: "
            + str(self.exception)
        )

    # show error message to console
    def show_message(self):
        # if build type is release then don't show error message then write it to log file
        if Config.DEBUG:
            logger.debug(f"type: {self.type}")
            logger.debug(f"message: {self.message}")
            logger.debug(f"data: {self.data}")
            logger.debug(f"timestamp: {self.timestamp}")
            logger.debug(f"status: {self.status}")
            logger.debug(f"error: {self.error}")
            logger.debug(f"exception: {self.exception}")
        else:
            # write error message to log file
            # check log file exist or not then create new log file
            if not os.path.exists(Config.LOG_FILE_PATH):
                # create log file
                with open(Config.LOG_FILE_PATH, "w") as f:
                    f.write("")
            # write error message to log file
            with open(Config.LOG_FILE_PATH, "a") as f:
                log = (
                    "-----------------\n type: "
                    + str(self.type)
                    + "\n message: "
                    + str(self.message)
                    + "\n data: "
                    + str(self.data)
                    + "\n timestamp: "
                    + str(self.timestamp)
                    + "\n status: "
                    + str(self.status)
                    + "\n error: "
                    + str(self.error)
                    + "\n exception: "
                    + str(self.exception)
                    + "\n-----------------\n"
                )
                f.write("log: " + log + "\n")
