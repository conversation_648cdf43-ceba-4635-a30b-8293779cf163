import QtQuick
import QtQuick.Controls
import QtQuick.Layouts

/**
 * ConnectionStateOverlay.qml - Reusable component cho connection state display
 *
 * CHỨC NĂNG CHÍNH:
 * - Hiển thị trạng thái kết nối camera (connecting, disconnected, connected)
 * - Layout tối ưu với ColumnLayout
 * - Animation cho loading state
 * - Responsive design theo kích thước parent
 * - Single source of truth cho connection state
 *
 * KIẾN TRÚC:
 * - Centralized state management
 * - Optimized layout system
 * - Dynamic z-index từ model
 * - Reusable component pattern
 */
Item {
    id: root

    // Required properties
    property var itemData: null
    property bool isDarkTheme: true
    property int baseZIndex: 20
    property bool showLogo: true  // Có thể tắt logo nếu cần
    property real logoScale: 0.2  // Tỷ lệ logo so với container (0.1 = 10%, 0.5 = 50%)
    property int logoMinSize: 24  // Kích thước tối thiểu của logo
    property int logoMaxSize: 48  // <PERSON><PERSON><PERSON> thước tối đa của logo


    // Component visibility - hiển thị khi có logo hoặc text, nhưng không hiển thị khi cần permission
    visible: {
        if (root.itemData) {
            // Hide if permission is needed (PermissionRequestOverlay takes priority)
            if (root.itemData.needsPermission) {
                return false
            }

            var state = root.itemData.connectionState
            var cameraId = root.itemData.cameraModel ? root.itemData.cameraModel.id : "Unknown"
            // Show overlay for all states except "started" (connected and playing)
            // Include "unauthorized" state for token expiration
            var isVisible = state !== "started" // connecting, buffering, disconnected, stopped, unauthorized

            return isVisible
        }
        return false
    }

    anchors.fill: parent
    z: baseZIndex

    // // Background overlay cho better visibility
    // Rectangle {
    //     id: backgroundOverlay
    //     anchors.fill: parent
    //     color: root.isDarkTheme ? "#80000000" : "#80ffffff"
    //     radius: 8
    //     opacity: 0.8

    //     // Subtle border cho better definition
    //     border.width: 1
    //     border.color: root.isDarkTheme ? "#40ffffff" : "#40000000"
    // }

    // Main content layout
    ColumnLayout {
        id: contentLayout
        anchors.centerIn: parent
        spacing: 8
        width: Math.max(120, Math.min(200, parent.width * 0.6))
        height: Math.max(80, Math.min(120, parent.height * 0.4))

        // Row chứa icon và text trạng thái
        RowLayout {
            id: rowStatus
            Layout.alignment: Qt.AlignHCenter
            Layout.fillWidth: true
            spacing: 8

            // Hiệu ứng loading 3 bước khi connecting
            Item {
                id: loadingIcons
                width: Math.max(20, Math.min(32, contentLayout.width / 8))
                height: width
                visible: root.itemData && root.itemData.connectionState === "connecting"

                property int currentStep: 1

                Timer {
                    id: loadingTimer
                    interval: 300
                    running: loadingIcons.visible
                    repeat: true
                    onTriggered: {
                        loadingIcons.currentStep = loadingIcons.currentStep % 3 + 1
                    }
                }

                // Loading1
                Image {
                    anchors.fill: parent
                    source: "qrc:/src/assets/state/Loading1.svg"
                    opacity: loadingIcons.currentStep === 1 ? 1.0 : 0.3
                    Behavior on opacity { NumberAnimation { duration: 150 } }
                }
                // Loading2
                Image {
                    anchors.fill: parent
                    source: "qrc:/src/assets/state/Loading2.svg"
                    opacity: loadingIcons.currentStep === 2 ? 1.0 : 0.3
                    Behavior on opacity { NumberAnimation { duration: 150 } }
                }
                // Loading3
                Image {
                    anchors.fill: parent
                    source: "qrc:/src/assets/state/Loading3.svg"
                    opacity: loadingIcons.currentStep === 3 ? 1.0 : 0.3
                    Behavior on opacity { NumberAnimation { duration: 150 } }
                }
            }

            // Logo hiển thị khi stopped
            Image {
                id: logoImage
                width: {
                    var containerWidth = contentLayout.width
                    var calculatedWidth = containerWidth * root.logoScale
                    var finalWidth = Math.round(Math.min(root.logoMaxSize, Math.max(root.logoMinSize, calculatedWidth)))
                    return finalWidth
                }
                height: width
                fillMode: Image.PreserveAspectFit
                smooth: true
                layer.enabled: true
                layer.smooth: true
                visible: {
                    // Show logo for stopped state, or show icon for unauthorized state
                    var shouldShow = root.itemData && root.showLogo &&
                                   (root.itemData.connectionState === "stopped" ||
                                    root.itemData.connectionState === "unauthorized")
                    if (shouldShow) {
                        console.log("🖼️ [STATE_FLOW] ConnectionStateOverlay: Logo/Icon VISIBLE for camera",
                                   root.itemData.cameraModel ? root.itemData.cameraModel.id : "Unknown",
                                   "state =", root.itemData.connectionState,
                                   "size =", width + "x" + height,
                                   "showLogo =", root.showLogo)
                    }
                    return shouldShow
                }
                source: {
                    // Show different icons based on state
                    if (root.itemData && root.itemData.connectionState === "unauthorized") {
                        return "qrc:/src/assets/state/error.svg"  // Error icon for unauthorized
                    }
                    return "qrc:/src/assets/login_screen/logo.png"  // Default logo for stopped
                }

                // Center trong RowLayout
                Layout.alignment: Qt.AlignHCenter
                Layout.preferredWidth: width
                Layout.preferredHeight: height
            }

            // Status text
            Text {
                id: statusText
                Layout.alignment: Qt.AlignVCenter
                text: {
                    if (root.itemData) {
                        var displayText = ""
                        if (root.itemData.connectionState === "connecting") {
                            displayText = qsTr("Connecting")
                        } else if (root.itemData.connectionState === "buffering") {
                            displayText = root.itemData.percent
                        } else if (root.itemData.connectionState === "unauthorized") {
                            displayText = qsTr("Unauthorized - Please login again")
                        }
                        // Không hiển thị text cho trạng thái stopped - chỉ hiển thị logo

                        // console.log("📝 [STATE_FLOW] ConnectionStateOverlay: Camera",
                        //            root.itemData.cameraModel ? root.itemData.cameraModel.id : "Unknown",
                        //            "state =", root.itemData.connectionState,
                        //            "→ text =", displayText)
                        return displayText
                    }
                    return ""
                }
                font.pixelSize: Math.max(12, Math.min(16, contentLayout.width / 12))
                font.bold: true
                color: {
                    var textColor = "white"
                    if (root.itemData) {
                        if (root.itemData.connectionState === "connecting") {
                            textColor = "#F6BE00"  // Yellow for connecting
                        } else if (root.itemData.connectionState === "buffering") {
                            textColor = "#F6BE00"  // Yellow for buffering
                        } else if (root.itemData.connectionState === "unauthorized") {
                            textColor = "#FF4444"  // Red for unauthorized/error
                        }
                        // Không cần color cho stopped vì không hiển thị text

                        // console.log("🎨 [STATE_FLOW] ConnectionStateOverlay: Camera",
                        //            root.itemData.cameraModel ? root.itemData.cameraModel.id : "Unknown",
                        //            "state =", root.itemData.connectionState,
                        //            "→ color =", textColor)
                    }
                    return textColor
                }
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
                wrapMode: Text.WordWrap
                maximumLineCount: 2
                elide: Text.ElideRight
                opacity: text !== "" ? 1.0 : 0.0
                Behavior on opacity {
                    NumberAnimation { duration: 200 }
                }
            }
        }
    }

    // Component lifecycle
    Component.onCompleted: {
        console.log("🎬 [STATE_FLOW] ConnectionStateOverlay: Component created for camera",
                   root.itemData && root.itemData.cameraModel ? root.itemData.cameraModel.id : "Unknown")
    }

    Component.onDestruction: {
        console.log("🗑️ [STATE_FLOW] ConnectionStateOverlay: Component destroyed for camera",
                   root.itemData && root.itemData.cameraModel ? root.itemData.cameraModel.id : "Unknown")
    }
}
