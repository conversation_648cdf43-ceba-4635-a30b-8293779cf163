from PySide6.QtCore import Slot
from PySide6.QtWidgets import (QCheckBox, QComboBox, QRadioButton, QTextEdit,
                               QPushButton, QHBoxLayout, QVBoxLayout,
                               QButtonGroup)
import logging
from src.presentation.setting_screen.widget.base_setting_tab import BaseSettingTab

logger = logging.getLogger(__name__)


class SecuritySettingTab(BaseSettingTab):
    """Security settings tab for authentication and access control"""

    def __init__(self, parent=None):
        """Initialize the security settings tab"""
        super().__init__(parent)
        self.setup_sections()

    def setup_sections(self):
        """Setup all security sections"""
        # 1. Authentication
        self.setup_authentication_section()
        self.add_separator()
        
        # 2. Access Control
        self.setup_access_control_section()
        self.add_separator()
        
        # 3. Data Security
        self.setup_data_security_section()
        self.add_separator()
        
        # 4. Privacy Settings
        self.setup_privacy_section()

        # Add spacer at the bottom
        self.add_bottom_spacer()

    def setup_authentication_section(self):
        """Setup authentication settings section"""
        controls_layout = self.add_section(
            self.tr("Authentication"),
            self.tr("Configure login methods and security options")
        )
        
        # Login Method
        login_method_layout = QHBoxLayout()
        self.login_method_label = self.create_label(self.tr("Login Method:"))
        self.login_method_label.setObjectName("login_method_label")
        login_method_layout.addWidget(self.login_method_label)
        
        self.login_method_combo = QComboBox()
        login_methods = [self.tr("Username/Password"), self.tr("Windows Authentication"), self.tr("LDAP")]
        self.login_method_combo.addItems(login_methods)
        self.login_method_combo.setCurrentText(self.tr("Username/Password"))
        self.login_method_combo.currentTextChanged.connect(
            lambda value: self.emit_setting_changed("login_method", value)
        )
        
        login_method_layout.addWidget(self.login_method_combo)
        login_method_layout.addStretch()
        controls_layout.addLayout(login_method_layout)
        
        # Two-Factor Authentication
        self.two_factor_checkbox = QCheckBox(self.tr("Enable Two-Factor Authentication"))
        self.two_factor_checkbox.setObjectName("two_factor_checkbox")
        self.two_factor_checkbox.setChecked(False)
        self.two_factor_checkbox.toggled.connect(
            lambda checked: self.emit_setting_changed("two_factor_auth", checked)
        )
        controls_layout.addWidget(self.two_factor_checkbox)
        
        # Session Timeout
        timeout_layout = QHBoxLayout()
        timeout_layout.addWidget(self.create_label(self.tr("Session Timeout:")))
        
        self.session_timeout_combo = QComboBox()
        timeouts = ["15 minutes", "30 minutes", "1 hour", "2 hours", "Never"]
        self.session_timeout_combo.addItems(timeouts)
        self.session_timeout_combo.setCurrentText(self.tr("30 minutes"))
        self.session_timeout_combo.currentTextChanged.connect(
            lambda value: self.emit_setting_changed("session_timeout", value)
        )
        
        timeout_layout.addWidget(self.session_timeout_combo)
        timeout_layout.addStretch()
        controls_layout.addLayout(timeout_layout)
        
        # Remember Login
        self.remember_login_checkbox = QCheckBox(self.tr("Remember Login"))
        self.remember_login_checkbox.setObjectName("remember_login_checkbox")
        self.remember_login_checkbox.setChecked(True)
        self.remember_login_checkbox.toggled.connect(
            lambda checked: self.emit_setting_changed("remember_login", checked)
        )
        controls_layout.addWidget(self.remember_login_checkbox)

    def setup_access_control_section(self):
        """Setup access control settings section"""
        controls_layout = self.add_section(
            self.tr("Access Control"),
            self.tr("Configure user permissions and access restrictions")
        )
        
        # User Permissions
        permissions_layout = QHBoxLayout()
        permissions_layout.addWidget(self.create_label(self.tr("Default User Permissions:")))
        
        self.user_permissions_combo = QComboBox()
        permissions = ["View Only", "Operator", "Administrator"]
        self.user_permissions_combo.addItems(permissions)
        self.user_permissions_combo.setCurrentText(self.tr("View Only"))
        self.user_permissions_combo.currentTextChanged.connect(
            lambda value: self.emit_setting_changed("user_permissions", value)
        )
        
        permissions_layout.addWidget(self.user_permissions_combo)
        permissions_layout.addStretch()
        controls_layout.addLayout(permissions_layout)
        
        # Camera Access
        camera_access_layout = QVBoxLayout()
        camera_access_layout.addWidget(self.create_label(self.tr("Camera Access:")))
        
        self.camera_access_group = QButtonGroup()
        self.all_cameras_radio = QRadioButton(self.tr("All cameras"))
        self.all_cameras_radio.setObjectName("all_cameras_radio")
        self.selected_cameras_radio = QRadioButton(self.tr("Selected cameras"))
        self.selected_cameras_radio.setObjectName("selected_cameras_radio")
        self.all_cameras_radio.setChecked(True)
        
        self.camera_access_group.addButton(self.all_cameras_radio, 0)
        self.camera_access_group.addButton(self.selected_cameras_radio, 1)
        self.camera_access_group.buttonClicked.connect(self.on_camera_access_changed)
        
        camera_access_layout.addWidget(self.all_cameras_radio)
        camera_access_layout.addWidget(self.selected_cameras_radio)
        controls_layout.addLayout(camera_access_layout)
        
        # Time-based Access
        time_access_layout = QVBoxLayout()
        time_access_layout.addWidget(self.create_label(self.tr("Time-based Access:")))
        
        self.time_access_group = QButtonGroup()
        self.access_24_7_radio = QRadioButton(self.tr("24/7 Access"))
        self.access_24_7_radio.setObjectName("access_24_7_radio")
        self.business_hours_radio = QRadioButton(self.tr("Business hours only"))
        self.business_hours_radio.setObjectName("business_hours_radio")
        self.custom_schedule_radio = QRadioButton(self.tr("Custom schedule"))
        self.custom_schedule_radio.setObjectName("custom_schedule_radio")
        self.access_24_7_radio.setChecked(True)
        
        self.time_access_group.addButton(self.access_24_7_radio, 0)
        self.time_access_group.addButton(self.business_hours_radio, 1)
        self.time_access_group.addButton(self.custom_schedule_radio, 2)
        self.time_access_group.buttonClicked.connect(self.on_time_access_changed)
        
        time_access_layout.addWidget(self.access_24_7_radio)
        time_access_layout.addWidget(self.business_hours_radio)
        time_access_layout.addWidget(self.custom_schedule_radio)
        controls_layout.addLayout(time_access_layout)
        
        # IP Restrictions
        ip_layout = QVBoxLayout()
        ip_layout.addWidget(self.create_label(self.tr("IP Address Restrictions:")))
        
        self.ip_restrictions_group = QButtonGroup()
        self.allow_all_ip_radio = QRadioButton(self.tr("Allow all IP addresses"))
        self.allow_all_ip_radio.setObjectName("allow_all_ip_radio")
        self.whitelist_only_radio = QRadioButton(self.tr("Whitelist only"))
        self.whitelist_only_radio.setObjectName("whitelist_only_radio")
        self.allow_all_ip_radio.setChecked(True)
        
        self.ip_restrictions_group.addButton(self.allow_all_ip_radio, 0)
        self.ip_restrictions_group.addButton(self.whitelist_only_radio, 1)
        self.ip_restrictions_group.buttonClicked.connect(self.on_ip_restrictions_changed)
        
        ip_layout.addWidget(self.allow_all_ip_radio)
        ip_layout.addWidget(self.whitelist_only_radio)
        
        # IP Whitelist input
        self.ip_whitelist_text = QTextEdit()
        self.ip_whitelist_text.setObjectName("ip_whitelist_text")
        self.ip_whitelist_text.setMaximumHeight(80)
        self.ip_whitelist_text.setPlaceholderText(self.tr("Enter IP addresses, one per line (e.g., *************)"))
        self.ip_whitelist_text.setEnabled(False)
        self.ip_whitelist_text.textChanged.connect(
            lambda: self.emit_setting_changed("ip_whitelist", self.ip_whitelist_text.toPlainText())
        )
        
        ip_layout.addWidget(self.ip_whitelist_text)
        controls_layout.addLayout(ip_layout)

    def setup_data_security_section(self):
        """Setup data security settings section"""
        controls_layout = self.add_section(
            self.tr("Data Security"),
            self.tr("Configure encryption and security protocols")
        )
        
        # Encryption Level
        encryption_layout = QHBoxLayout()
        self.encryption_level_label = self.create_label(self.tr("Encryption Level:"))
        self.encryption_level_label.setObjectName("encryption_level_label")
        encryption_layout.addWidget(self.encryption_level_label)
        
        self.encryption_level_combo = QComboBox()
        encryption_levels = ["AES-128", "AES-256"]
        self.encryption_level_combo.addItems(encryption_levels)
        self.encryption_level_combo.setCurrentText(self.tr("AES-256"))
        self.encryption_level_combo.currentTextChanged.connect(
            lambda value: self.emit_setting_changed("encryption_level", value)
        )
        
        encryption_layout.addWidget(self.encryption_level_combo)
        encryption_layout.addStretch()
        controls_layout.addLayout(encryption_layout)
        
        # Secure Protocols
        protocols_layout = QVBoxLayout()
        protocols_layout.addWidget(self.create_label(self.tr("Secure Protocols:")))
        
        self.protocols_group = QButtonGroup()
        self.https_only_radio = QRadioButton(self.tr("HTTPS only"))
        self.https_only_radio.setObjectName("https_only_radio")
        self.mixed_protocols_radio = QRadioButton(self.tr("Mixed protocols"))
        self.mixed_protocols_radio.setObjectName("mixed_protocols_radio")
        self.http_allowed_radio = QRadioButton(self.tr("HTTP allowed"))
        self.http_allowed_radio.setObjectName("http_allowed_radio")
        self.https_only_radio.setChecked(True)
        
        self.protocols_group.addButton(self.https_only_radio, 0)
        self.protocols_group.addButton(self.mixed_protocols_radio, 1)
        self.protocols_group.addButton(self.http_allowed_radio, 2)
        self.protocols_group.buttonClicked.connect(self.on_protocols_changed)
        
        protocols_layout.addWidget(self.https_only_radio)
        protocols_layout.addWidget(self.mixed_protocols_radio)
        protocols_layout.addWidget(self.http_allowed_radio)
        controls_layout.addLayout(protocols_layout)
        
        # Certificate Management
        cert_layout = QHBoxLayout()
        self.cert_management_label = self.create_label(self.tr("Certificate Management:"))
        self.cert_management_label.setObjectName("cert_management_label")
        cert_layout.addWidget(self.cert_management_label)
        
        self.cert_upload_button = QPushButton(self.tr("Upload Certificate"))
        self.cert_upload_button.setObjectName("cert_upload_button")
        self.cert_generate_button = QPushButton(self.tr("Generate Certificate"))
        self.cert_generate_button.setObjectName("cert_generate_button")
        self.cert_view_button = QPushButton(self.tr("View Current"))
        self.cert_view_button.setObjectName("cert_view_button")
        
        self.cert_upload_button.clicked.connect(self.upload_certificate)
        self.cert_generate_button.clicked.connect(self.generate_certificate)
        self.cert_view_button.clicked.connect(self.view_certificate)
        
        cert_layout.addWidget(self.cert_upload_button)
        cert_layout.addWidget(self.cert_generate_button)
        cert_layout.addWidget(self.cert_view_button)
        cert_layout.addStretch()
        controls_layout.addLayout(cert_layout)
        
        # Audit Logging
        self.audit_logging_checkbox = QCheckBox(self.tr("Enable Audit Logging"))
        self.audit_logging_checkbox.setObjectName("audit_logging_checkbox")
        self.audit_logging_checkbox.setChecked(True)
        self.audit_logging_checkbox.toggled.connect(
            lambda checked: self.emit_setting_changed("audit_logging", checked)
        )
        controls_layout.addWidget(self.audit_logging_checkbox)
        
        # Log Level
        log_level_layout = QHBoxLayout()
        log_level_layout.addWidget(self.create_label(self.tr("Log Level:")))
        
        self.log_level_combo = QComboBox()
        log_levels = ["Info", "Warning", "Error"]
        self.log_level_combo.addItems(log_levels)
        self.log_level_combo.setCurrentText(self.tr("Info"))
        self.log_level_combo.currentTextChanged.connect(
            lambda value: self.emit_setting_changed("log_level", value)
        )
        
        log_level_layout.addWidget(self.log_level_combo)
        log_level_layout.addStretch()
        controls_layout.addLayout(log_level_layout)

    def setup_privacy_section(self):
        """Setup privacy settings section"""
        controls_layout = self.add_section(
            self.tr("Privacy Settings"),
            self.tr("Configure recording and data handling options")
        )
        
        # Video Recording
        recording_layout = QHBoxLayout()
        self.video_recording_label = self.create_label(self.tr("Video Recording:"))
        self.video_recording_label.setObjectName("video_recording_label")
        recording_layout.addWidget(self.video_recording_label)
        
        self.video_recording_combo = QComboBox()
        recording_modes = ["Always", "Motion only", "Manual", "Disabled"]
        self.video_recording_combo.addItems(recording_modes)
        self.video_recording_combo.setCurrentText(self.tr("Motion only"))
        self.video_recording_combo.currentTextChanged.connect(
            lambda value: self.emit_setting_changed("video_recording", value)
        )
        
        recording_layout.addWidget(self.video_recording_combo)
        recording_layout.addStretch()
        controls_layout.addLayout(recording_layout)
        
        # Audio Recording
        self.audio_recording_checkbox = QCheckBox(self.tr("Enable Audio Recording"))
        self.audio_recording_checkbox.setObjectName("audio_recording_checkbox")
        self.audio_recording_checkbox.setChecked(False)
        self.audio_recording_checkbox.toggled.connect(
            lambda checked: self.emit_setting_changed("audio_recording", checked)
        )
        controls_layout.addWidget(self.audio_recording_checkbox)

        # Screenshot Capture
        self.screenshot_checkbox = QCheckBox(self.tr("Enable Screenshot Capture"))
        self.screenshot_checkbox.setObjectName("screenshot_checkbox")
        self.screenshot_checkbox.setChecked(True)
        self.screenshot_checkbox.toggled.connect(
            lambda checked: self.emit_setting_changed("screenshot_capture", checked)
        )
        controls_layout.addWidget(self.screenshot_checkbox)
        
        # Export Restrictions
        export_layout = QVBoxLayout()
        export_layout.addWidget(self.create_label(self.tr("Export Restrictions:")))
        
        self.export_restrictions_group = QButtonGroup()
        self.admin_only_radio = QRadioButton(self.tr("Admin only"))
        self.admin_only_radio.setObjectName("admin_only_radio")
        self.all_users_radio = QRadioButton(self.tr("All users"))
        self.all_users_radio.setObjectName("all_users_radio")
        self.admin_only_radio.setChecked(True)
        
        self.export_restrictions_group.addButton(self.admin_only_radio, 0)
        self.export_restrictions_group.addButton(self.all_users_radio, 1)
        self.export_restrictions_group.buttonClicked.connect(self.on_export_restrictions_changed)
        
        export_layout.addWidget(self.admin_only_radio)
        export_layout.addWidget(self.all_users_radio)
        controls_layout.addLayout(export_layout)

    # Event handlers
    @Slot()
    def on_camera_access_changed(self):
        """Handle camera access radio button change"""
        button_id = self.camera_access_group.checkedId()
        access_type = "all" if button_id == 0 else "selected"
        self.emit_setting_changed("camera_access", access_type)

    @Slot()
    def on_time_access_changed(self):
        """Handle time access radio button change"""
        button_id = self.time_access_group.checkedId()
        access_types = [self.tr("24_7"), self.tr("business_hours"), self.tr("custom")]
        self.emit_setting_changed("time_access", access_types[button_id])

    @Slot()
    def on_ip_restrictions_changed(self):
        """Handle IP restrictions radio button change"""
        button_id = self.ip_restrictions_group.checkedId()
        is_whitelist = button_id == 1
        self.ip_whitelist_text.setEnabled(is_whitelist)
        restriction_type = "whitelist" if is_whitelist else "allow_all"
        self.emit_setting_changed("ip_restrictions", restriction_type)

    @Slot()
    def on_protocols_changed(self):
        """Handle protocols radio button change"""
        button_id = self.protocols_group.checkedId()
        protocols = [self.tr("https_only"), self.tr("mixed"), self.tr("http_allowed")]
        self.emit_setting_changed("secure_protocols", protocols[button_id])

    @Slot()
    def on_export_restrictions_changed(self):
        """Handle export restrictions radio button change"""
        button_id = self.export_restrictions_group.checkedId()
        restriction = "admin_only" if button_id == 0 else "all_users"
        self.emit_setting_changed("export_restrictions", restriction)

    # Certificate management methods
    @Slot()
    def upload_certificate(self):
        """Handle upload certificate button click"""
        self.emit_setting_changed("certificate_action", "upload")

    @Slot()
    def generate_certificate(self):
        """Handle generate certificate button click"""
        self.emit_setting_changed("certificate_action", "generate")

    @Slot()
    def view_certificate(self):
        """Handle view certificate button click"""
        self.emit_setting_changed("certificate_action", "view")

    def load_settings(self):
        """Load settings from storage"""
        pass

    def save_settings(self):
        """Save settings to storage"""
        pass

    def set_dynamic_stylesheet(self):
        """Apply dynamic theme-based styling"""
        # Call parent styling
        super().set_dynamic_stylesheet()

    def translate_ui(self):
        """Update UI text translations using findChild with ObjectName approach"""
        from PySide6.QtCore import QCoreApplication
        from PySide6.QtWidgets import QCheckBox, QPushButton, QRadioButton, QLabel


        # Translation mapping
        translations = {
            # Checkboxes
            "two_factor_checkbox": u"Enable Two-Factor Authentication",
            "remember_login_checkbox": u"Remember Login",
            "audit_logging_checkbox": u"Enable Audit Logging",
            "audio_recording_checkbox": u"Enable Audio Recording",
            "screenshot_checkbox": u"Enable Screenshot Capture",

            # Buttons
            "cert_upload_button": u"Upload Certificate",
            "cert_generate_button": u"Generate Certificate",
            "cert_view_button": u"View Current",

            # Radio buttons - Camera Access
            "all_cameras_radio": u"All cameras",
            "selected_cameras_radio": u"Selected cameras",

            # Radio buttons - Time-based Access
            "access_24_7_radio": u"24/7 Access",
            "business_hours_radio": u"Business hours only",
            "custom_schedule_radio": u"Custom schedule",

            # Radio buttons - IP Restrictions
            "allow_all_ip_radio": u"Allow all IP addresses",
            "whitelist_only_radio": u"Whitelist only",

            # Radio buttons - Secure Protocols
            "https_only_radio": u"HTTPS only",
            "mixed_protocols_radio": u"Mixed protocols",
            "http_allowed_radio": u"HTTP allowed",

            # Radio buttons - Export Restrictions
            "admin_only_radio": u"Admin only",
            "all_users_radio": u"All users",

            # Field labels
            "login_method_label": u"Login Method:",
            "video_recording_label": u"Video Recording:",
            "encryption_level_label": u"Encryption Level:",
            "cert_management_label": u"Certificate Management:",

            # Section titles and subtitles (generated by BaseSettingTab)
            "section_title_authentication": u"Authentication",
            "section_subtitle_authentication": u"Configure login methods and security options",
            "section_title_access_control": u"Access Control",
            "section_subtitle_access_control": u"Configure user permissions and access restrictions",
            "section_title_data_security": u"Data Security",
            "section_subtitle_data_security": u"Configure encryption and security protocols",
            "section_title_privacy_settings": u"Privacy Settings",
            "section_subtitle_privacy_settings": u"Configure recording and data handling options",
        }

        # Translate all widgets using findChild with ObjectName
        for object_name, text_key in translations.items():
            # Try different widget types
            widget = (self.findChild(QCheckBox, object_name) or
                     self.findChild(QPushButton, object_name) or
                     self.findChild(QRadioButton, object_name) or
                     self.findChild(QLabel, object_name))
            if widget:
                translated_text = QCoreApplication.translate("SecuritySettingTab", text_key, None)
                widget.setText(translated_text)
                pass
            else:
                pass

        # Translate combo box items manually
        self._translate_combo_boxes()

        # Translate placeholder text
        self._translate_placeholder_texts()



    def _translate_placeholder_texts(self):
        """Translate placeholder texts for input widgets"""
        from PySide6.QtCore import QCoreApplication

        # IP Whitelist placeholder
        placeholder_text = QCoreApplication.translate("SecuritySettingTab", u"Enter IP addresses, one per line (e.g., *************)", None)
        self.ip_whitelist_text.setPlaceholderText(placeholder_text)

    def _translate_combo_boxes(self):
        """Translate combo box items that need manual translation"""
        from PySide6.QtCore import QCoreApplication

        # Login Method combo
        current_login = self.login_method_combo.currentIndex()
        self.login_method_combo.clear()
        self.login_method_combo.addItems([
            QCoreApplication.translate("SecuritySettingTab", u"Username/Password", None),
            QCoreApplication.translate("SecuritySettingTab", u"Windows Authentication", None),
            QCoreApplication.translate("SecuritySettingTab", u"LDAP", None)
        ])
        if current_login < self.login_method_combo.count():
            self.login_method_combo.setCurrentIndex(current_login)

        # Session Timeout combo
        current_timeout = self.session_timeout_combo.currentIndex()
        self.session_timeout_combo.clear()
        self.session_timeout_combo.addItems([
            QCoreApplication.translate("SecuritySettingTab", u"15 minutes", None),
            QCoreApplication.translate("SecuritySettingTab", u"30 minutes", None),
            QCoreApplication.translate("SecuritySettingTab", u"1 hour", None),
            QCoreApplication.translate("SecuritySettingTab", u"2 hours", None),
            QCoreApplication.translate("SecuritySettingTab", u"Never", None)
        ])
        if current_timeout < self.session_timeout_combo.count():
            self.session_timeout_combo.setCurrentIndex(current_timeout)

        # User Permissions combo
        current_permissions = self.user_permissions_combo.currentIndex()
        self.user_permissions_combo.clear()
        self.user_permissions_combo.addItems([
            QCoreApplication.translate("SecuritySettingTab", u"View Only", None),
            QCoreApplication.translate("SecuritySettingTab", u"Operator", None),
            QCoreApplication.translate("SecuritySettingTab", u"Administrator", None)
        ])
        if current_permissions < self.user_permissions_combo.count():
            self.user_permissions_combo.setCurrentIndex(current_permissions)

        # Log Level combo
        current_log = self.log_level_combo.currentIndex()
        self.log_level_combo.clear()
        self.log_level_combo.addItems([
            QCoreApplication.translate("SecuritySettingTab", u"Info", None),
            QCoreApplication.translate("SecuritySettingTab", u"Warning", None),
            QCoreApplication.translate("SecuritySettingTab", u"Error", None)
        ])
        if current_log < self.log_level_combo.count():
            self.log_level_combo.setCurrentIndex(current_log)

        # Video Recording combo
        current_recording = self.video_recording_combo.currentIndex()
        self.video_recording_combo.clear()
        self.video_recording_combo.addItems([
            QCoreApplication.translate("SecuritySettingTab", u"Always", None),
            QCoreApplication.translate("SecuritySettingTab", u"Motion only", None),
            QCoreApplication.translate("SecuritySettingTab", u"Manual", None),
            QCoreApplication.translate("SecuritySettingTab", u"Disabled", None)
        ])
        if current_recording < self.video_recording_combo.count():
            self.video_recording_combo.setCurrentIndex(current_recording)
