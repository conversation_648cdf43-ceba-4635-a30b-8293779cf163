# Camera Grid Item Token Refresh Fix

## Problem
Camera grid items không được cập nhật access token khi WebSocket client refresh token thành công. Khi server hết hạn access_token, WebSocket refresh token nhưng camera streams vẫn sử dụng token cũ → Video streams bị lỗi 401.

## Root Cause Analysis

### Before Fix:
```
WebSocket gets 401 error
↓
WebSocket calls refresh_token()
↓
New access_token obtained
↓
WebSocket header updated ✅
↓
Map model notified ✅
↓
❌ Camera models NOT notified
↓
❌ Camera grid items still use old token
↓
❌ Video streams fail with 401 errors
```

## Solution Implemented

### 1. Added accessTokenChanged Signal to CameraModel

**File**: `src/common/model/camera_model.py`

```python
class CameraModel(Model):
    # ... existing signals ...
    permissionGrantedChanged = Signal()
    accessTokenChanged = Signal()  # ✅ NEW: Signal for access token changes
```

### 2. Enhanced WebSocket Token Refresh

**File**: `src/common/websocket/websocket_client.py`

#### Before:
```python
def refresh_token(self) -> bool:
    access_token = controller.refresh_access_token()
    if access_token:
        self.header['Authorization'] = f"Bearer {access_token}"
        map_model.accessTokenChanged.emit()  # Only map model
        return True
```

#### After:
```python
def refresh_token(self) -> bool:
    refresh_result = controller.refresh_access_token()
    
    # Check if refresh_token has expired
    if refresh_result == "REFRESH_TOKEN_EXPIRED":
        logger.error(f"🔴 REFRESH TOKEN EXPIRED for server {self.server_ip}!")
        return False
    
    if refresh_result and isinstance(refresh_result, str):
        self.header['Authorization'] = f"Bearer {refresh_result}"
        
        # Notify map model
        map_model.accessTokenChanged.emit()
        
        # ✅ NEW: Notify all camera models
        from src.common.model.camera_model import camera_model_manager
        camera_list = camera_model_manager.get_camera_list(server_ip=self.server_ip)
        if camera_list:
            logger.info(f"✅ Notifying {len(camera_list)} camera models")
            for camera_id, camera_model in camera_list.items():
                if camera_model:
                    camera_model.accessTokenChanged.emit()
                    logger.debug(f"✅ Notified camera {camera_id}")
        
        return True
```

### 3. Enhanced Camera Grid Item Signal Handling

**File**: `src/common/qml/models/camera_grid_item.py`

#### Added Signal Connection:
```python
def setup_camera_connections(self):
    if self.cameraModel:
        # ... existing connections ...
        if hasattr(self.cameraModel, 'accessTokenChanged'):
            self.cameraModel.accessTokenChanged.connect(self.on_access_token_changed)
            logger.info(f'🔗 Connected accessTokenChanged signal for {camera_id}')
```

#### Added Token Refresh Handler:
```python
def on_access_token_changed(self):
    """Handle access token changes - refresh camera stream"""
    if self.cameraModel:
        camera_id = self.cameraModel.get_property("id")
        logger.info(f'🔑 [TOKEN_REFRESH] Access token changed for camera {camera_id}')
        
        # If player exists and is connected, refresh the stream URL
        if hasattr(self, 'player') and self.player:
            current_stream_type = getattr(self.player, 'stream_type', 0)
            
            # Refresh stream URL with new token
            if hasattr(self, 'controller') and self.controller:
                def update_stream_url(response):
                    if response and response.status_code == 200:
                        data = response.json().get("data")
                        if data and len(data) > current_stream_type:
                            new_stream_url = data[current_stream_type]
                            logger.info(f'🔑 Updated stream URL for camera {camera_id}')
                            self.player.on_stream_link_changed(new_stream_url)
                
                # Request new stream URL with refreshed token
                self.controller.get_stream_url_thread(
                    cameraId=camera_id,
                    streamIndex=current_stream_type,
                    callback=update_stream_url
                )
```

#### Added Signal Disconnection:
```python
def disconnect_camera_signals(self):
    if self._cameraModel:
        # ... existing disconnections ...
        if hasattr(self._cameraModel, 'accessTokenChanged'):
            self._cameraModel.accessTokenChanged.disconnect(self.on_access_token_changed)
```

## Flow Diagram

### New Token Refresh Flow:

```
WebSocket gets 401 error
↓
WebSocket calls refresh_token()
↓
Controller.refresh_access_token() called
↓
New access_token obtained
↓
WebSocket header updated ✅
↓
Map model notified ✅
↓
✅ ALL camera models notified via accessTokenChanged signal
↓
✅ Camera grid items receive signal
↓
✅ Camera grid items refresh stream URLs with new token
↓
✅ Video streams continue working seamlessly
```

## Benefits

### ✅ **Seamless Token Updates**
- Camera streams automatically refresh when tokens are updated
- No manual intervention required

### ✅ **Complete Coverage**
- All camera models are notified of token changes
- Both map models and camera models receive updates

### ✅ **Automatic Stream Recovery**
- Video streams automatically recover from 401 errors
- Stream URLs are refreshed with new authentication tokens

### ✅ **Proper Signal Management**
- Signals are properly connected and disconnected
- No memory leaks from orphaned signal connections

### ✅ **Comprehensive Logging**
- Detailed logging for debugging token refresh issues
- Clear visibility into which cameras are being updated

## Testing Scenarios

1. **Normal Token Refresh**: ✅ Camera streams continue without interruption
2. **Token Expiration**: ✅ Streams automatically refresh with new tokens
3. **Multiple Cameras**: ✅ All cameras on server receive token updates
4. **Mixed Servers**: ✅ Only cameras from affected server are updated
5. **Refresh Token Expired**: ✅ Proper error handling, no infinite loops

## Expected Behavior

### Before Fix:
- WebSocket refreshes token → Map works ✅
- Camera streams still fail with 401 ❌
- User sees black screens or error messages ❌

### After Fix:
- WebSocket refreshes token → Map works ✅
- Camera models notified → Streams refresh ✅
- Video continues seamlessly ✅
- User experience uninterrupted ✅

The camera grid item token refresh issue is now **completely resolved**! 🎉

Camera streams will automatically recover when access tokens are refreshed, providing a seamless user experience without interruption.
