import QtQuick 2.15
import QtQuick.Controls 2.15
import "../constants/ZIndexConstants.js" as ZIndex
import "../components"

Item {
    id: root
    required property var gridItem
    required property var gridModel
    required property var itemData

    // ✅ CONTENT BOUNDS: Properties for content bounds positioning
    property real contentBoundsX: 0
    property real contentBoundsY: 0
    property real contentBoundsWidth: root.width
    property real contentBoundsHeight: root.height

    // ✅ MOUSE POSITION: Track mouse position for resize detection (relative to content bounds)
    property real currentMouseX: 0
    property real currentMouseY: 0
    property string activeResizeZone: "none"  // "right", "bottom", "corner", "none"

    function updateResizeZone(x, y) {
        var edgeThreshold = 20  // 20px from edge
        var cornerThreshold = 30  // 30px corner area

        // ✅ CONTENT BOUNDS: Check position relative to content bounds
        var contentX = x - contentBoundsX
        var contentY = y - contentBoundsY

        var isNearRight = (contentX >= contentBoundsWidth - edgeThreshold)
        var isNearBottom = (contentY >= contentBoundsHeight - edgeThreshold)
        var isInCorner = (contentX >= contentBoundsWidth - cornerThreshold) && (contentY >= contentBoundsHeight - cornerThreshold)

        var newZone = "none"
        if (isInCorner) {
            newZone = "corner"
        } else if (isNearRight) {
            newZone = "right"
        } else if (isNearBottom) {
            newZone = "bottom"
        }

        if (newZone !== activeResizeZone) {
            activeResizeZone = newZone
        }
    }
    
    //Performance optimized cell dimensions
    readonly property real cellWidth: dimensionsValid ? cachedCellWidth : _calculateCellWidth()
    readonly property real cellHeight: dimensionsValid ? cachedCellHeight : _calculateCellHeight()

    function _calculateCellWidth() {
        if (gridItem && gridItem.parent && gridItem.parent.parent && gridModel) {
            return gridItem.parent.parent.width / gridModel.columns
        }
        return 100
    }

    function _calculateCellHeight() {
        if (gridItem && gridItem.parent && gridItem.parent.parent && gridModel) {
            return gridItem.parent.parent.height / gridModel.rows
        }
        return 100
    }
    
    signal resizeStarted(string resizeType)
    signal resizeCompleted(string resizeType)

    // ✅ RESIZE HIGHLIGHT: Properties for highlight management
    property var dragHighlight: null
    property bool isResizeHighlightActive: false

    AnimationManager {
        id: animationManager
    }

    //Throttled validation timer
    Timer {
        id: validationTimer
        interval: 50  // 50ms throttle for performance
        repeat: false

        property int pendingRow: 0
        property int pendingCol: 0
        property int pendingWidthCells: 0
        property int pendingHeightCells: 0

        onTriggered: {
            // Perform actual validation
            isValidResize = root.gridModel.canResizeItemAt(
                pendingRow, pendingCol, pendingWidthCells, pendingHeightCells
            )
        }
    }

    property var currentHoverAnimation: null
    property var currentPressAnimation: null
    property var currentFeedbackAnimation: null
    property var currentSizeAnimation: null  // ✅ SMOOTH ANIMATION
    property real initialWidth: 0
    property real initialHeight: 0
    property int targetWidthCells: 0
    property int targetHeightCells: 0

    //Collision detection properties
    property bool isValidResize: true
    property var conflictingItems: []

    // ✅ PHASE 1.5: Bidirectional resize properties
    property bool canExpandRight: false
    property bool canShrinkRight: false
    property bool canResizeRight: false



    property bool canExpandBottom: false
    property bool canShrinkBottom: false
    property bool canResizeBottom: false

    property bool canExpandCorner: false
    property bool canShrinkCorner: false
    property bool canResizeCorner: false

    //Enhanced visual feedback properties
    property bool showSizePreview: false
    property string previewTextContent: ""

    // ✅ SMART SNAP: Threshold-based auto resize
    readonly property real snapThreshold: 0.3  // 30% threshold

    //Performance optimization - Cell dimensions caching
    property real cachedCellWidth: 0
    property real cachedCellHeight: 0
    property bool dimensionsValid: false
    property int cacheUpdateCount: 0

    //Cache update function
    function updateCellDimensionsCache() {

        if (!gridItem) {
            dimensionsValid = false
            return
        }

        if (!gridModel) {
            dimensionsValid = false
            return
        }

        if (!gridItem.parent) {
            dimensionsValid = false
            return
        }

        if (!gridItem.parent.parent) {
            dimensionsValid = false
            return
        }

        var newWidth = gridItem.parent.parent.width / gridModel.columns
        var newHeight = gridItem.parent.parent.height / gridModel.rows


        // Validate calculated dimensions
        if (newWidth <= 0 || newHeight <= 0 || isNaN(newWidth) || isNaN(newHeight)) {
            dimensionsValid = false
            return
        }

        // Update cache
        cachedCellWidth = newWidth
        cachedCellHeight = newHeight
        cacheUpdateCount++
        dimensionsValid = true
    }

    // Simple và robust resize validity function
    function updateResizeValidity() {
        // Only check essential properties
        if (!gridModel || !itemData) {
            resetResizeCapabilities()
            return
        }

        var currentWidth = itemData.cols_cell || 1
        var currentHeight = itemData.rows_cell || 1
        var row = itemData.row || 0
        var col = itemData.col || 0

        // ✅ FIX: Simple expansion check for UI (only +1 for performance)
        canExpandRight = gridModel.canResizeItemAt(row, col, currentWidth + 1, currentHeight)
        canExpandBottom = gridModel.canResizeItemAt(row, col, currentWidth, currentHeight + 1)
        canExpandCorner = gridModel.canResizeItemAt(row, col, currentWidth + 1, currentHeight + 1)

        // ✅ PHASE 1.5: Check shrink capabilities (size constraints)
        canShrinkRight = (currentWidth > 1)
        canShrinkBottom = (currentHeight > 1)
        canShrinkCorner = (currentWidth > 1 && currentHeight > 1)

        // ✅ PHASE 1.5: Combined capabilities (expand OR shrink)
        canResizeRight = canExpandRight || canShrinkRight
        canResizeBottom = canExpandBottom || canShrinkBottom
        canResizeCorner = canExpandCorner || canShrinkCorner

    }

    function resetResizeCapabilities() {
        canExpandRight = canShrinkRight = canResizeRight = false
        canExpandBottom = canShrinkBottom = canResizeBottom = false
        canExpandCorner = canShrinkCorner = canResizeCorner = false
    }

    // ✅ FIX: Calculate snap target based on actual drag distance (multi-cell support)
    function calculateSnapTarget(currentCells, dragDistance, cellSize) {
        if (!cellSize || cellSize <= 0) return currentCells

        var dragRatio = Math.abs(dragDistance) / cellSize
        var isExpanding = dragDistance > 0

        if (dragRatio >= snapThreshold) {
            // ✅ FIX: Snap to actual dragged size instead of just +1/-1
            var targetCells
            if (isExpanding) {
                // Snap to the cell we dragged to
                targetCells = currentCells + Math.round(dragRatio)
            } else {
                // Snap to the cell we dragged to (shrinking)
                targetCells = Math.max(1, currentCells - Math.round(dragRatio))
            }

            return targetCells
        } else {
            // Drag < 30% → revert to original
            return currentCells
        }
    }

    // ✅ SMART SNAP: Check if snap target is valid
    function validateSnapTarget(row, col, targetWidth, targetHeight) {
        if (!gridModel || !itemData) return false

        // Check minimum size
        if (targetWidth < 1 || targetHeight < 1) return false

        // Check collision for expansion
        var currentWidth = itemData.cols_cell || 1
        var currentHeight = itemData.rows_cell || 1

        if (targetWidth > currentWidth || targetHeight > currentHeight) {
            return gridModel.canResizeItemAt(row, col, targetWidth, targetHeight)
        }

        // Shrinking is always valid (already checked minimum)
        return true
    }

    // ✅ RESIZE HIGHLIGHT: Show highlight for resize preview
    function showResizeHighlight(targetWidthCells, targetHeightCells, isValid) {
        if (!dragHighlight || !itemData) return

        dragHighlight.updateHighlight(
            itemData.col,
            itemData.row,
            targetWidthCells,
            targetHeightCells,
            isValid,
            false  // isSwap = false for resize
        )
        isResizeHighlightActive = true
    }

    // ✅ RESIZE HIGHLIGHT: Hide highlight
    function hideResizeHighlight() {
        if (dragHighlight && isResizeHighlightActive) {
            dragHighlight.hideHighlight()
            isResizeHighlightActive = false
        }
    }
    
    // ✅ GRID LINES: Simple functions to show/hide grid lines
    function showGridLines() {
        var mainGrid = findMainGrid()
        if (mainGrid && mainGrid.showGridLinesRequested) {
            mainGrid.showGridLinesRequested()
        }
    }
    
    function hideGridLines() {
        var mainGrid = findMainGrid()
        if (mainGrid && mainGrid.hideGridLinesRequested) {
            mainGrid.hideGridLinesRequested()
        }
    }
    
    // ✅ GRID LINES: Find MainGrid
    function findMainGrid() {
        var current = gridItem ? gridItem.parent : null
        while (current) {
            if (current.objectName === "MainGrid") {
                return current
            }
            current = current.parent
        }
        return null
    }



    // Update validity when grid or item changes
    Connections {
        target: gridModel
        function onColumnsChanged() {
            updateCellDimensionsCache()  //Update cache first
            updateResizeValidity()
        }
        function onRowsChanged() {
            updateCellDimensionsCache()  //Update cache first
            updateResizeValidity()
        }
        function onResizeValidityUpdateRequested() {
            updateResizeValidity()
        }
    }

    Connections {
        target: itemData
        function onCellDimensionsChanged() {
            updateResizeValidity()
        }
        function onRowChanged() {
            updateResizeValidity()
        }
        function onColChanged() {
            updateResizeValidity()
        }
    }

    // Property watchers thay vì Component.onCompleted
    onGridModelChanged: {
        if (gridModel && itemData) {
            updateCellDimensionsCache()  // ✅ FIX: Update cache when grid model changes
            updateResizeValidity()
        }
    }

    onItemDataChanged: {
        if (gridModel && itemData) {
            updateCellDimensionsCache()  // ✅ FIX: Update cache when item data changes
            updateResizeValidity()
        }
    }

    // Watch for gridItem changes
    onGridItemChanged: {
        if (gridModel && itemData && gridItem) {
            updateCellDimensionsCache()  //Initialize cache
            updateResizeValidity()
        }
    }

    //Initialize cache on component completion
    Component.onCompleted: {
        updateCellDimensionsCache()
        findDragHighlight()
    }

    // ✅ RESIZE HIGHLIGHT: Find dragHighlight object from MainGrid
    function findDragHighlight() {
        var current = gridItem ? gridItem.parent : null
        while (current) {
            if (current.objectName === "MainGrid" || current.toString().indexOf("MainGrid") !== -1) {
                // Look for dragHighlight in MainGrid
                for (var i = 0; i < current.children.length; i++) {
                    var child = current.children[i]
                    if (child.objectName === "dragHighlight") {
                        dragHighlight = child
                        return
                    }
                }
                break
            }
            current = current.parent
        }
    }

    // ✅ MOUSE TRACKING: MouseArea để track mouse position và hiển thị cursor
    MouseArea {
        id: mouseTracker
        anchors.fill: parent
        hoverEnabled: true
        acceptedButtons: Qt.NoButton  // Không block mouse events
        z: ZIndex.gridItemControls - 1

        // ✅ CURSOR RESIZE: Hiển thị cursor resize dựa trên activeResizeZone
        cursorShape: {
            if (activeResizeZone === "corner" && canResizeCorner) return Qt.SizeFDiagCursor
            if (activeResizeZone === "right" && canResizeRight) return Qt.SizeHorCursor
            if (activeResizeZone === "bottom" && canResizeBottom) return Qt.SizeVerCursor
            return Qt.ArrowCursor
        }

        onPositionChanged: function(mouse) {
            currentMouseX = mouse.x
            currentMouseY = mouse.y
            updateResizeZone(mouse.x, mouse.y)
        }

        onExited: {
            activeResizeZone = "none"
            // ✅ RESIZE HIGHLIGHT: Hide highlight when mouse exits
            hideResizeHighlight()
        }
    }

    // ✅ FIX: Watch for grid size changes
    Connections {
        target: gridModel
        function onColumnsChanged() {
            updateCellDimensionsCache()
        }
        function onRowsChanged() {
            updateCellDimensionsCache()
        }
    }

    
    // ✅ UNIFIED: Single resize handler với Loader - dựa trên mouse position
    Loader {
        id: resizeHandlerLoader
        anchors.fill: parent
        active: activeResizeZone !== "none" && (
            (activeResizeZone === "right" && canResizeRight) ||
            (activeResizeZone === "bottom" && canResizeBottom) ||
            (activeResizeZone === "corner" && canResizeCorner)
        )

        // ✅ RESIZE HIGHLIGHT: Hide highlight when loader becomes inactive
        onActiveChanged: {
            if (!active) {
                hideResizeHighlight()
            }
        }



        sourceComponent: Item {
            id: multiResizeHandler
            anchors.fill: parent

            // ✅ CONTENT BOUNDS: Position handlers within content bounds area
            Item {
                id: contentBoundsContainer
                x: root.contentBoundsX
                y: root.contentBoundsY
                width: root.contentBoundsWidth
                height: root.contentBoundsHeight

                // ✅ INVISIBLE HANDLER: Invisible area for resize detection - only cursor changes
                Rectangle {
                    id: unifiedResizer
                    width: getHandlerWidth()
                    height: getHandlerHeight()
                    color: "transparent"  // ✅ INVISIBLE: Always transparent
                    visible: getHandlerVisible()
                    opacity: 0.0  // ✅ INVISIBLE: Always invisible
                    anchors.right: needsRightAnchor() ? parent.right : undefined
                    anchors.bottom: needsBottomAnchor() ? parent.bottom : undefined
                    anchors.rightMargin: needsRightAnchor() ? -1 : 0
                    anchors.bottomMargin: needsBottomAnchor() ? -1 : 0
                    anchors.verticalCenter: needsVerticalCenter() ? parent.verticalCenter : undefined
                    anchors.horizontalCenter: needsHorizontalCenter() ? parent.horizontalCenter : undefined
                    z: ZIndex.gridItemControls + (isCornerHandler() ? 2 : 1)

                    // ✅ POSITION-BASED: Handler type detection dựa trên mouse position
                    function getActiveHandlerType() {
                        // Chỉ hiển thị handler nếu mouse ở vùng tương ứng VÀ có capability
                        if (activeResizeZone === "corner" && canResizeCorner) return "corner"
                        if (activeResizeZone === "right" && canResizeRight) return "right"
                        if (activeResizeZone === "bottom" && canResizeBottom) return "bottom"
                        return "none"
                    }

                    function isCornerHandler() { return getActiveHandlerType() === "corner" }
                    function isRightHandler() { return getActiveHandlerType() === "right" }
                    function isBottomHandler() { return getActiveHandlerType() === "bottom" }

                    function needsRightAnchor() { return isRightHandler() || isCornerHandler() }
                    function needsBottomAnchor() { return isBottomHandler() || isCornerHandler() }
                    function needsVerticalCenter() { return isRightHandler() }
                    function needsHorizontalCenter() { return isBottomHandler() }

                    function getHandlerWidth() {
                        if (isCornerHandler()) return 8
                        if (isRightHandler()) return 3
                        if (isBottomHandler()) return parent.width  // ✅ EDGE SIZE: Full width for bottom edge
                        return 0
                    }

                    function getHandlerHeight() {
                        if (isCornerHandler()) return 8
                        if (isRightHandler()) return parent.height  // ✅ EDGE SIZE: Full height for right edge
                        if (isBottomHandler()) return 3
                        return 0
                    }

                    function getHandlerVisible() {
                        var type = getActiveHandlerType()
                        if (type === "corner") return canResizeCorner
                        if (type === "right") return canResizeRight
                        if (type === "bottom") return canResizeBottom
                        return false
                    }

                    function getActiveMouseArea() {
                        var type = getActiveHandlerType()
                        if (type === "corner") return unifiedMouseArea
                        if (type === "right") return unifiedMouseArea
                        if (type === "bottom") return unifiedMouseArea
                        return null
                    }

                    // ✅ CURSOR ONLY: MouseArea chỉ để hiển thị cursor resize
                    MouseArea {
                        id: unifiedMouseArea
                        anchors.fill: parent
                        anchors.margins: -8
                        hoverEnabled: true

                        // ✅ CURSOR RESIZE: Hiển thị cursor resize khi hover
                        cursorShape: {
                            var type = parent.getActiveHandlerType()
                            if (type === "right") return canResizeRight ? Qt.SizeHorCursor : Qt.ArrowCursor
                            if (type === "bottom") return canResizeBottom ? Qt.SizeVerCursor : Qt.ArrowCursor
                            if (type === "corner") return canResizeCorner ? Qt.SizeFDiagCursor : Qt.ArrowCursor
                            return Qt.ArrowCursor
                        }



                        property real lastMouseX: 0
                        property real lastMouseY: 0
                        property string resizeType: parent.getActiveHandlerType()





                        function canResize() {
                            var type = parent.getActiveHandlerType()
                            if (type === "right") return canResizeRight
                            if (type === "bottom") return canResizeBottom
                            if (type === "corner") return canResizeCorner
                            return false
                        }

                        onEntered: {
                            // ✅ CURSOR ONLY: No visual feedback, only cursor change
                        }

                        onExited: {
                            // ✅ CURSOR ONLY: No visual feedback, only cursor change
                        }

                        onPressed: function(mouse) {
                            if (!canResize() || !gridItem || !itemData || !gridModel) {
                                return
                            }

                            if (!dimensionsValid) {
                                updateCellDimensionsCache()
                            }

                            lastMouseX = mouse.x
                            lastMouseY = mouse.y
                            initialWidth = gridItem.width
                            initialHeight = gridItem.height
                            targetWidthCells = itemData.cols_cell
                            targetHeightCells = itemData.rows_cell
                            isValidResize = true

                            showSizePreview = true
                            previewTextContent = targetWidthCells + "×" + targetHeightCells + " cells"

                            // ✅ GRID LINES: Show grid lines when resize starts
                            showGridLines()

                            root.resizeStarted(resizeType)

                            // ✅ RESIZE HIGHLIGHT: Show initial highlight
                            var isValid = validateSnapTarget(itemData.row, itemData.col, targetWidthCells, targetHeightCells)
                            showResizeHighlight(targetWidthCells, targetHeightCells, isValid)
                        }

                        onPositionChanged: function(mouse) {
                            if (!pressed || !gridItem || !dimensionsValid) return

                            var deltaX = mouse.x - lastMouseX
                            var deltaY = mouse.y - lastMouseY

                            if (resizeType === "right") {
                                var newWidth = Math.max(cachedCellWidth, gridItem.width + deltaX)
                                var newWidthCells = Math.round(newWidth / cachedCellWidth)

                                targetWidthCells = parseInt(newWidthCells)
                                gridItem.width = newWidth
                                previewTextContent = targetWidthCells + "×" + targetHeightCells + " cells"
                            } else if (resizeType === "bottom") {
                                var newHeight = Math.max(cachedCellHeight, gridItem.height + deltaY)
                                var newHeightCells = Math.round(newHeight / cachedCellHeight)

                                targetHeightCells = parseInt(newHeightCells)
                                gridItem.height = newHeight
                                previewTextContent = targetWidthCells + "×" + targetHeightCells + " cells"
                            } else if (resizeType === "corner") {
                                var newWidth = Math.max(cachedCellWidth, gridItem.width + deltaX)
                                var newHeight = Math.max(cachedCellHeight, gridItem.height + deltaY)
                                var newWidthCells = Math.round(newWidth / cachedCellWidth)
                                var newHeightCells = Math.round(newHeight / cachedCellHeight)

                                targetWidthCells = parseInt(newWidthCells)
                                targetHeightCells = parseInt(newHeightCells)
                                gridItem.width = newWidth
                                gridItem.height = newHeight
                                previewTextContent = targetWidthCells + "×" + targetHeightCells + " cells"
                            }

                            isValidResize = true

                            // ✅ RESIZE HIGHLIGHT: Update highlight during drag
                            var isValid = validateSnapTarget(itemData.row, itemData.col, targetWidthCells, targetHeightCells)
                            showResizeHighlight(targetWidthCells, targetHeightCells, isValid)
                        }

                        onReleased: {
                            var currentWidthCells = itemData.cols_cell
                            var currentHeightCells = itemData.rows_cell

                            var finalTargetWidthCells = currentWidthCells
                            var finalTargetHeightCells = currentHeightCells

                            if (resizeType === "right") {
                                var dragDistance = gridItem.width - initialWidth
                                finalTargetWidthCells = calculateSnapTarget(currentWidthCells, dragDistance, cachedCellWidth)
                            } else if (resizeType === "bottom") {
                                var dragDistance = gridItem.height - initialHeight
                                finalTargetHeightCells = calculateSnapTarget(currentHeightCells, dragDistance, cachedCellHeight)
                            } else if (resizeType === "corner") {
                                var dragDistanceX = gridItem.width - initialWidth
                                var dragDistanceY = gridItem.height - initialHeight
                                finalTargetWidthCells = calculateSnapTarget(currentWidthCells, dragDistanceX, cachedCellWidth)
                                finalTargetHeightCells = calculateSnapTarget(currentHeightCells, dragDistanceY, cachedCellHeight)
                            }

                            var isValidSnapTarget = validateSnapTarget(itemData.row, itemData.col, finalTargetWidthCells, finalTargetHeightCells)
                            var hasChange = (finalTargetWidthCells !== currentWidthCells) || (finalTargetHeightCells !== currentHeightCells)

                            if (hasChange && isValidSnapTarget) {
                                var success = root.gridModel.resizeItemSafe(
                                    itemData.row, itemData.col, finalTargetWidthCells, finalTargetHeightCells
                                )

                                if (!success) {
                                    gridItem.width = initialWidth
                                    gridItem.height = initialHeight
                                }
                            } else {
                                gridItem.width = initialWidth
                                gridItem.height = initialHeight
                            }

                            showSizePreview = false

                            // ✅ GRID LINES: Hide grid lines when resize ends
                            hideGridLines()

                            // ✅ RESIZE HIGHLIGHT: Hide highlight when resize ends
                            hideResizeHighlight()


                            // ✅ PHASE 3: Call handleResizeComplete with detailed information including grid size
                            if (itemData && itemData.handleResizeComplete && gridModel) {
                                itemData.handleResizeComplete(
                                    currentHeightCells,     // old_rows
                                    currentWidthCells,      // old_cols
                                    finalTargetHeightCells, // new_rows
                                    finalTargetWidthCells,  // new_cols
                                    gridItem.width,         // final_width
                                    gridItem.height,        // final_height
                                    gridModel.rows,         // total_grid_rows
                                    gridModel.columns       // total_grid_cols
                                )
                            }

                            root.resizeCompleted(resizeType)
                        }
                    }
                }
            }
        }
    }

}
