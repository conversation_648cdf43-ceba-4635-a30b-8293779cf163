from PySide6.QtWidgets import QWidget, QVBoxLayout, QTabWidget, QStackedWidget, QTextEdit
from PySide6.QtCore import Qt, QCoreApplication, Signal
import logging

from VMS import main_controller
from src.common.widget.custom_titlebar.custom_titlebar_with_tab import CustomTitleBarWithTab
from src.presentation.setting_screen.widget.general_setting_tab import GeneralSettingTab
from src.presentation.setting_screen.widget.user_interface_setting_tab import UserInterfaceSettingTab
from src.presentation.setting_screen.widget.security_setting_tab import SecuritySettingTab
from src.presentation.setting_screen.widget.advanced_setting_tab import AdvancedSettingTab
from src.presentation.setting_screen.widget.tracking_setting_tab import TrackingSettingTab
from src.styles.style import Style
from src.utils.theme_setting import ThemeSettings
# from src.presentation.setting_screen.widget.auto_update_tab import AutoUpdateTab


class SettingScreen(QWidget):
    logout_emit_signal = Signal(str)

    def __init__(self, parent=None, window_parent=None):
        super(SettingScreen, self).__init__(parent)
        self.parent = parent
        self.window_parent = window_parent
        self.setup_ui()
        self.set_dynamic_stylesheet()

    def setup_ui(self):
        self.setStyleSheet("border: none;")
        self.main_layout = QVBoxLayout()
        self.main_layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.main_layout.setContentsMargins(4, 0, 2, 4)
        self.main_layout.setSpacing(0)
        self.title_bar = CustomTitleBarWithTab(parent=self, window_parent=self.window_parent, is_show_tab_bar=True)
        self.title_bar.setFixedHeight(40)
        self.title_bar.add_Tab(self.tr('General'))
        self.title_bar.add_Tab(self.tr('User Interface'))
        self.title_bar.add_Tab(self.tr('Security'))
        self.title_bar.add_Tab(self.tr('Advanced'))
        self.title_bar.add_Tab(self.tr('Tracking configuration'))
        self.title_bar.tab_widget.tabBar().show()
        self.title_bar.tab_widget.tabBar().setExpanding(True)
        self.title_bar.signal_change_tab.connect(self.on_tab_change)
        self.main_layout.addWidget(self.title_bar)

        self.layout_content = QVBoxLayout()
        self.layout_content.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.layout_content.setContentsMargins(0, 0, 0, 0)

        # Create all setting tabs
        self.general_setting = GeneralSettingTab()
        self.user_interface_setting = UserInterfaceSettingTab()
        self.security_setting = SecuritySettingTab()
        self.advanced_setting = AdvancedSettingTab()
        self.tracking_setting = TrackingSettingTab()

        self.stacked_content = QStackedWidget()
        self.stacked_content.setContentsMargins(0, 0, 0, 0)
        self.stacked_content.setObjectName('stacked_content')

        # Add all tabs to stacked widget
        self.stacked_content.addWidget(self.general_setting)
        self.stacked_content.addWidget(self.user_interface_setting)
        self.stacked_content.addWidget(self.security_setting)
        self.stacked_content.addWidget(self.advanced_setting)
        self.stacked_content.addWidget(self.tracking_setting)
        self.layout_content.addWidget(self.stacked_content)
        self.main_layout.addLayout(self.layout_content)
        self.setLayout(self.main_layout)

        # Set initial tab and content
        self.title_bar.tab_widget.setCurrentIndex(0)
        self.stacked_content.setCurrentIndex(0)

        # Connect setting change signals
        self.connect_setting_signals()

    def connect_setting_signals(self):
        """Connect setting change signals from all tabs"""
        self.user_interface_setting.setting_changed.connect(self.on_setting_changed)
        self.security_setting.setting_changed.connect(self.on_setting_changed)
        self.advanced_setting.setting_changed.connect(self.on_setting_changed)

    def on_tab_change(self, index):
        # Show the corresponding content widget when a tab is selected
        self.stacked_content.setCurrentIndex(index)

    def on_setting_changed(self, setting_name, value):
        """Handle setting changes from any tab"""
        # TODO: Implement actual setting persistence here
        pass

    def logout_emit(self, data):
        self.logout_emit_signal.emit(data)

    def retranslateUiSettingScreen(self):
        self.title_bar.tab_widget.setTabText(
            0, QCoreApplication.translate("SettingScreen", u"General", None))
        self.title_bar.tab_widget.setTabText(
            1, QCoreApplication.translate("SettingScreen", u"User Interface", None))
        self.title_bar.tab_widget.setTabText(
            2, QCoreApplication.translate("SettingScreen", u"Security", None))
        self.title_bar.tab_widget.setTabText(
            3, QCoreApplication.translate("SettingScreen", u"Advanced", None))
        self.title_bar.tab_widget.setTabText(
            4, QCoreApplication.translate("SettingScreen", u"Tracking configuration", None))

        # Translate all tabs
        self.general_setting.translate_ui_general()
        self.user_interface_setting.translate_ui()
        self.security_setting.translate_ui()
        self.advanced_setting.translate_ui()
        self.tracking_setting.translate_ui_general()

    def restyle_ui_setting_screen(self):
        self.set_dynamic_stylesheet()
        self.general_setting.set_dynamic_stylesheet()
        self.user_interface_setting.set_dynamic_stylesheet()
        self.security_setting.set_dynamic_stylesheet()
        self.advanced_setting.set_dynamic_stylesheet()
        self.tracking_setting.set_dynamic_stylesheet()
        self.title_bar.set_dynamic_stylesheet()

    def set_dynamic_stylesheet(self):
        self.stacked_content.setStyleSheet(f'''
            QWidget#stacked_content {{
                border: 1px solid {main_controller.get_theme_attribute("Color", "text_color_all_app", 0.15)};
            }}
            ''')
        
        # Add tab bar styling
        self.title_bar.tab_widget.setStyleSheet(f"""
            QTabBar {{
                qproperty-drawBase: 0;
                background-color: transparent;
                border: none;
            }}
            QTabBar::tab {{
                min-width: 180px;
                background-color: {main_controller.get_theme_attribute('Color', 'tabbar_background_normal')};
                color: {main_controller.get_theme_attribute('Color', 'text_color_all_app')};
                padding: 4px 12px;
                margin-left: 2px;
                margin-top: 4px;
                font-weight: normal;
                border-radius: 10px;
                border: 1px solid transparent;
            }}
            QTabBar::tab:selected {{
                border-radius: 10px;
                border: 2px solid {main_controller.get_theme_attribute('Color', 'primary')};
                background-color: {main_controller.get_theme_attribute('Color', 'tabbar_background_selected')};
                color: {main_controller.get_theme_attribute('Color', 'primary')};
                padding: 4px 12px;
                margin-left: 2px;
                font-weight: bold;
            }}
            QTabBar::tab:hover {{
                color: {main_controller.get_theme_attribute('Color', 'primary')};
            }}
        """)
        
        self.general_setting.set_dynamic_stylesheet()
        self.user_interface_setting.set_dynamic_stylesheet()
        self.security_setting.set_dynamic_stylesheet()
        self.advanced_setting.set_dynamic_stylesheet()
        self.tracking_setting.set_dynamic_stylesheet()
        self.title_bar.set_dynamic_stylesheet()
