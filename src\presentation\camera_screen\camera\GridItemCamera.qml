/**
 * GridItemCamera.qml - Specialized camera component kế thừa từ GridItemBase
 *
 * Ch<PERSON><PERSON> năng chính:
 * - <PERSON><PERSON> thừa từ GridItemBase.qml (OOP inheritance)
 * - Video stream rendering và playback thông qua FrameModel
 * - PTZ controls support (pan, tilt, zoom)
 * - Digital zoom functionality với mouse wheel
 * - Camera state indicators và connection status
 * - Camera info overlay với responsive design
 * - Rotation support với snap-to-cardinal-angles
 * - Camera-specific interactions và context menu
 *
 * Architecture:
 * - Inherits: GridItemBase properties, functions, signals
 * - Extends: Camera-specific functionality (PTZ, zoom, rotation)
 * - Overrides: Camera-specific behaviors (fullscreen, selection)
 * - Integration: FrameModel cho video rendering, ConnectionStateOverlay cho status
 */

import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import models 1.0
import "../controls"
import "../base"
import "../components"
import "."
import "../constants/ZIndexConstants.js" as ZIndex
import "../handlers"

GridItemBase {
    id: root

    property bool onvifSupported: itemData.onvifSupported


    property bool isPtz3dActive: false
    property bool isPtzActive: false
    property bool isDragZoomActive: false
    property real zoomFactor: 1.0
    property real minZoom: 1.0
    property real maxZoom: 5.0
    property real zoomSensitivity: 0.1
    property bool isZooming: false
    property point zoomCenter: Qt.point(0, 0)
    property bool isPTZZooming: false
    property string currentZoomDirection: ""
    property real ptzZoomSpeed: 1
    property string menuTextColor: gridModel ? gridModel.get_color_theme_by_key("text_color_all_app") : "white"
    property string backgroundColor: gridModel ? gridModel.get_color_theme_by_key("main_background") : "white"

    showControls: isHovered || isPtzActive || isPtz3dActive || isDragZoomActive

    signal cameraSelected()
    signal ptzActivated(string type)
    signal zoomChanged(real factor)
    signal fullscreenToggled(bool isFullscreen)

    Component.onCompleted: {
    }
    Component {
        id: cameraContextMenuComponent
        GridItemContextMenuCamera {
            gridItem: root
            itemData: root.itemData
            position: root.position
            itemType: root.itemType
            z: ZIndex.gridItemContextMenu
            // gridModel is available globally - no need to pass
            onActionTriggered: function(key, direction) {
                if (key === "videoStream") {
                    root.switchStreamTypeChanged(key, direction)
                }
                // Có thể mở rộng cho các action khác
            }
        }
    }

    Loader {
        id: contextMenuLoader
        parent: root.Window.window ? root.Window.window.contentItem : root
        sourceComponent: cameraContextMenuComponent
        active: false // Bật khi cần show menu
    }
    
    Component.onDestruction: {
        if (videoItem) {
            console.log("[DEBUG] Unregistering video capture during destruction ",root.itemData.cameraModel.name)
            if (root.itemData) {
                root.itemData.unregister_player() 
                root.itemData.closeEvent()
            }
            if (gridModel.gridItemSelected.widget === root.itemData) {
                gridModel.gridItemSelected.widget.unClickedChanged()
            }
        }
    }

    onItemDataChanged: {
        if (itemData) {
        }
    }

    // ✅ CONNECTIONS: ItemData signals
    Connections {
        target: itemData
        function onFullscreenChanged() {
            // Handle fullscreen changes if needed
        }
        function onAccessTokenRefreshed() {
            console.log("🔑 [QML] Access token refreshed for camera:", root.itemData.cameraModel.name)
            console.log("🔑 [QML] Stream should be automatically refreshed by backend")
        }
    }

    // ✅ CONNECTIONS: CameraModel signals
    Connections {
        target: itemData.cameraModel
        function onStateMergeChanged() {
            console.log("🎬 [QML] Camera state merged changed:", root.itemData.cameraModel.state_merged)
        }
        function onAccessTokenChanged() {
            console.log("🔑 [QML] Access token changed for camera:", root.itemData.cameraModel.name)
            console.log("🔑 [QML] Stream refresh handled automatically by Python backend")
        }
    }
    Connections {
        target: gridModel
        function onThemeChanged() {
            menuTextColor = gridModel.get_color_theme_by_key("text_color_all_app")
            backgroundColor = gridModel.get_color_theme_by_key("main_background")
        }
    }

    onIsMaximizedChanged: {
        if (isMaximized) {
            fullscreenToggled(true)
        } else {
            resetZoom()
            fullscreenToggled(false)
        }
    }

    /**
     * Reset digital zoom về mức mặc định
     */
    function resetZoom() {
        zoomFactor = 1.0
        zoomCenter = Qt.point(root.width / 2, root.height / 2)
        isZooming = false
        zoomChanged(zoomFactor)
    }

    function activatePtzButton(buttonType) {
        if (gridModel && gridModel.activatePtzForCamera) {
            gridModel.activatePtzForCamera(position, buttonType)
        }

        // Nếu camera chưa được chọn thì chọn nó
        if (!root.isSelected && gridModel && gridModel.selectItemAt) {
            gridModel.selectItemAt(root.gridRow, root.gridCol, false)
        }

        if (buttonType === "ptz") {
            updatePTZControlPanelPosition()
        }

        // Use centralized z-index management from GridItemBase
        if (buttonType === "none") {
            updateParentZIndex() // Restore to normal state
        } else {
            forceParentZIndex(ZIndex.gridItemPopup) // Force high z-index for PTZ
        }

        console.log("🎮 [PTZ_ACTIVATE] PTZ activation completed for:", buttonType)
        ptzActivated(buttonType)
    }

    // Update PTZ control panel position when camera position changes
    onXChanged: updatePTZControlPanelPosition()
    onYChanged: updatePTZControlPanelPosition()
    onWidthChanged: {
        updatePTZControlPanelPosition()
    }
    onHeightChanged: {
        updatePTZControlPanelPosition()
    }
    /**
     * Update PTZ control panel position to stay next to camera
     */
    function switchStreamTypeChanged(key,direction) {
        console.log("hell3", direction)
        root.itemData.switchStreamTypeChanged(direction,videoItem)
    }
    function handleItemClicked() {
        if (itemData && itemData.isAnimating) return;
        if (gridModel.gridItemSelected.widget !== root.itemData) {
            if (gridModel.gridItemSelected.widget) {
                gridModel.gridItemSelected.widget.unClickedChanged()
            }
            gridModel.gridItemSelected.widget = root.itemData
            itemData.clickedChanged()
        } else {
            itemData.clickedChanged()
        }
    }
    function updatePTZControlPanelPosition() {
        if (ptzControlPanel && ptzControlPanel.visible && root.parent && !isMaximized) {
            // Position to the right of the camera
            ptzControlPanel.x = root.contentBoundsX + root.width + 10
            ptzControlPanel.y = root.contentBoundsY
            console.log("maxi 1",  root.x, root.width, root.parent.parent.width, ptzControlPanel.x, ptzControlPanel.y)

            // Ensure panel doesn't go off-screen
            if (ptzControlPanel.x + itemData.x > root.parent.parent.width) {
                ptzControlPanel.x = root.contentBoundsX - ptzControlPanel.width
                ptzControlPanel.y = root.contentBoundsY
                console.log("maxi 2",  itemData.x, itemData.y, ptzControlPanel.height, root.parent.parent.height, root.width, root.parent.parent.width, ptzControlPanel.x, ptzControlPanel.y)
            }
            if (ptzControlPanel.height + itemData.y > root.parent.parent.height) {
                ptzControlPanel.y = root.contentBoundsY - ptzControlPanel.height - itemData.y + root.parent.parent.height
                console.log("maxi 3",  itemData.x, itemData.y, root.width, root.parent.parent.width, ptzControlPanel.x, ptzControlPanel.y)

            }
            if ((gridModel.columns === 1 && gridModel.rows === 1)) {
                ptzControlPanel.x = itemData.x + root.parent.parent.width / 4 -32
                ptzControlPanel.y = itemData.y + root.height - 250 - root.contentBoundsY
            }
        }
        else if (isMaximized) {
            ptzControlPanel.x = itemData.x + root.parent.parent.width / 4 -32
            ptzControlPanel.y = itemData.y + root.height - 250 - root.contentBoundsY
        }
    }

    /**
     * Emit camera selected signal
     */
    function selectCamera() {
        cameraSelected()
    }

    /**
     * Snap rotation angle về cardinal angles (0, 90, 180, 270) trong vòng 5°
     *
     * @param angle - Góc rotation input
     * @returns {number} Góc đã được snap
     */
    function snapToCardinalAngles(angle) {
        var normalized = ((angle % 360) + 360) % 360
        var cardinals = [0, 90, 180, 270, 360]
        for (var i = 0; i < cardinals.length; i++) {
            var diff = Math.abs(normalized - cardinals[i])
            if (diff <= 5 || (360 - diff) <= 5) {
                return cardinals[i] % 360
            }
        }
        return normalized
    }

    /**
     * Tính toán video size - full size khi maximized, rotated size khi normal
     *
     * @param isWidth - True để lấy width, false để lấy height
     * @returns {number} Calculated size
     */
    function getVideoSize(isWidth) {
        // Khi maximized, sử dụng full size bất kể rotation
        // if (isMaximized) {
        //     return isWidth ? width : height
        // }

        // Khi normal, tính toán rotated size để fit trong grid cell
        if (!itemData) return isWidth ? width : height

        var rotation = snapToCardinalAngles(itemData.rotation || 0)
        var radians = rotation * Math.PI / 180
        var cos = Math.abs(Math.cos(radians))
        var sin = Math.abs(Math.sin(radians))

        var boundingW = width * cos + height * sin
        var boundingH = width * sin + height * cos
        var scale = Math.min(width / boundingW, height / boundingH, 1.0)

        return isWidth ? width * scale : height * scale
    }

    /**
     * Send PTZ command to camera backend
     *
     * @param direction - PTZ direction (up, down, left, right, etc.)
     * @param speed - PTZ speed (0.0 to 1.0)
     */
    function sendPTZCommand(direction, speed) {
        if (!gridModel) {
            console.log("🎮 [PTZ_CMD] No grid model available")
            return
        }

        console.log("🎮 [PTZ_CMD] Sending PTZ command:", direction, "speed:", speed, "position:", position)

        // Send PTZ command through grid model using normalized direction values
        // The speed will be applied by the controlPTZ method using the stored PTZ speed
        // NOTE: PTZ directions are reversed as requested
        if (direction === "stop") {
            gridModel.stopPTZ(position)
        } else if (direction === "up") {
            gridModel.controlPTZ(position, 0, 0, 0, 1)  // Use normalized value, speed applied in controlPTZ
        } else if (direction === "down") {
            gridModel.controlPTZ(position, 0, 0, 0, -1)   // Use normalized value, speed applied in controlPTZ
        } else if (direction === "left") {
            gridModel.controlPTZ(position, 0, 0, -1, 0)   // Use normalized value, speed applied in controlPTZ
        } else if (direction === "right") {
            gridModel.controlPTZ(position, 0, 0, 1, 0)  // Use normalized value, speed applied in controlPTZ
        } else if (direction === "top-left") {
            gridModel.controlPTZ(position, 0, 0, -1, 1)  // Use normalized values, speed applied in controlPTZ
        } else if (direction === "top-right") {
            gridModel.controlPTZ(position, 0, 0, 1, 1) // Use normalized values, speed applied in controlPTZ
        } else if (direction === "bottom-left") {
            gridModel.controlPTZ(position, 0, 0, -1, -1)   // Use normalized values, speed applied in controlPTZ
        } else if (direction === "bottom-right") {
            gridModel.controlPTZ(position, 0, 0, 1, -1)  // Use normalized values, speed applied in controlPTZ
        } else if (direction === "home") {
            // Home button now behaves like right button (reversed right = left movement)
            gridModel.controlPTZ(position, 0, 0, 1, 0)  // Use normalized value, speed applied in controlPTZ
        } else if (direction === "zoom-in") {
            gridModel.controlPTZZoom(position, speed)
            gridModel.stopPTZ(position) // Stop zoom immediately
        } else if (direction === "zoom-out") {
            gridModel.controlPTZZoom(position, -speed)
            gridModel.stopPTZ(position) // Stop zoom immediately
        } else {
            console.log("🎮 [PTZ_CMD] Unknown direction:", direction)
        }
    }

    /**
     * Send PTZ 3D command (mouse-based control)
     *
     * @param startPoint - Start point (center of camera)
     * @param endPoint - End point (mouse position)
     */
    function sendPTZ3DCommand(startPoint, endPoint) {
        if (!gridModel) {
            console.log("🎮 [PTZ_3D_CMD] No grid model available")
            return
        }

        console.log("🎮 [PTZ_3D_CMD] Sending 3D PTZ command - Start:", startPoint.x, startPoint.y, "End:", endPoint.x, endPoint.y)

        // Send PTZ 3D command through grid model
        gridModel.controlPTZ(position, startPoint, endPoint)
    }

    /**
     * Send PTZ zoom command
     *
     * @param factor - Zoom factor (positive for zoom in, negative for zoom out)
     * @param speed - Zoom speed
     */
    function sendPTZZoomCommand(factor, speed) {
        if (!gridModel) {
            console.log("🎮 [PTZ_ZOOM_CMD] No grid model available")
            return
        }

        console.log("🎮 [PTZ_ZOOM_CMD] Sending zoom command - factor:", factor, "speed:", speed)

        // Send zoom command through grid model
        gridModel.controlPTZZoom(position, factor * speed)
    }

    /**
     * Send PTZ stop command
     */
    function sendPTZStopCommand() {
        if (!gridModel) {
            console.log("🎮 [PTZ_STOP_CMD] No grid model available")
            return
        }

        console.log("🎮 [PTZ_STOP_CMD] Sending PTZ stop command")

        // Send stop command through grid model
        gridModel.stopPTZ(position)
    }
    
    function sendPTZSpeed(speed) {
        console.log("ptz speed", position, speed)
        gridModel.setPTZSpeed(position, speed)
    }
    /**
     * Send drag-to-zoom command
     *
     * @param startPoint - Start point of drag rectangle
     * @param endPoint - End point of drag rectangle
     */
    function sendDragZoomCommand(startPoint, endPoint) {
        if (!gridModel) {
            console.log("🎮 [DRAG_ZOOM_CMD] No grid model available")
            return
        }

        console.log("🎮 [DRAG_ZOOM_CMD] Sending drag zoom command")
        console.log("🎮 [DRAG_ZOOM_CMD] Start:", startPoint.x, startPoint.y, "End:", endPoint.x, endPoint.y)

        // Send drag-to-zoom command through grid model if available
        if (gridModel.setDragToZoom) {
            // Tính toán tâm vùng drag
            var centerX = (startPoint.x + endPoint.x) / 2
            var centerY = (startPoint.y + endPoint.y) / 2

            // Tính toán kích thước vùng drag
            var rectWidth = Math.abs(endPoint.x - startPoint.x)
            var rectHeight = Math.abs(endPoint.y - startPoint.y)

            // Tính toán zoom factor dựa trên tỷ lệ vùng drag
            var zoomFactor = Math.min(width / rectWidth, height / rectHeight)

            // Chuẩn hóa tọa độ tâm về [-1, 1]
            var normalizedX = (centerX - width/2) / (width/2)
            var normalizedY = (centerY - height/2) / (height/2)

            gridModel.setDragToZoom(
                position,
                startPoint.x, startPoint.y,
                endPoint.x, endPoint.y,
                width, height,
                normalizedX, normalizedY,
                zoomFactor
            )
        }
    }

    /**
     * Enhanced wheel zoom functionality - Direct PTZ zoom control for PTZ cameras
     */
    function handleEnhancedWheelZoom(wheel) {
        console.log("🎮 [ENHANCED_WHEEL] Enhanced wheel zoom called with delta:", wheel.angleDelta.y, root.camera_id)

        // Only handle PTZ cameras
        if (root.onvifSupported) {
            console.log("🎮 [ENHANCED_WHEEL] Using PTZ zoom for supported camera")

            if (!gridModel) {
                console.log("🎮 [ENHANCED_WHEEL] No grid model available")
                return false
            }

            // Direct PTZ zoom control without immediate stop
            if (wheel.angleDelta.y > 0) {
                // Zoom in
                console.log("🎮 [ENHANCED_WHEEL] PTZ Zoom In")
                gridModel.controlPTZZoom(position, root.ptzZoomSpeed)
            } else {
                // Zoom out
                console.log("🎮 [ENHANCED_WHEEL] PTZ Zoom Out")
                gridModel.controlPTZZoom(position, -root.ptzZoomSpeed)
            }

            // Start timer to stop zoom after a short delay (when wheel stops)
            ptzZoomStopTimer.restart()

            return true; // Indicate that PTZ zoom was handled
        }

        console.log("🎮 [ENHANCED_WHEEL] No PTZ zoom handling - camera doesn't support PTZ or no camera_id");
        return false; // Let the default handler take over
    }

    // Timer to stop PTZ zoom when wheel events stop
    Timer {
        id: ptzZoomStopTimer
        interval: 200  // Stop zoom after 200ms of no wheel events
        repeat: false
        running: false

        onTriggered: {
            console.log("🎮 [ENHANCED_WHEEL] Stopping PTZ zoom after wheel inactivity")
            if (gridModel) {
                gridModel.stopPTZ(position)
            }
        }
    }
    FrameModel {
        id: videoItem
        anchors.centerIn: parent
        gridRow: root.gridRow
        gridCol: root.gridCol
        isSelected: root.isSelected

        // Listen to content bounds changes from frame model
        onContentBoundsChanged: function(x, y, width, height) {
            // Update the base GridItemBase content bounds properties
            root.contentBoundsX = x
            root.contentBoundsY = y
            root.contentBoundsWidth = width
            root.contentBoundsHeight = height
        }
        itemData: root.itemData
        z: ZIndex.gridItemContent
        width: getVideoSize(true)
        height: getVideoSize(false)
        transform: Rotation {
            origin.x: videoItem.width / 2
            origin.y: videoItem.height / 2
            angle: itemData ? snapToCardinalAngles(itemData.rotation || 0) : 0

            Behavior on angle {
                RotationAnimation {
                    duration: 100
                    direction: RotationAnimation.Shortest
                    easing.type: Easing.OutQuad
                }
            }
        }

        onClicked: {
            if (itemData && itemData.isAnimating) return;
            // ✅ PTZ STATE: Deactivate PTZ for all cameras when clicking on video area
            if (gridModel && gridModel.activatePtzForCamera) {
                gridModel.activatePtzForCamera(position, "none")
                console.log("🎮 [PTZ_VIDEO_CLICK] Video area clicked - deactivating PTZ for all cameras")
            }
            root.itemClicked(root)
        }
        onDoubleClicked: {
            if (itemData && itemData.isAnimating) return;
            root.itemDoubleClicked(root)
        }
        onRightClicked: {
            if (itemData && itemData.isAnimating) return;
            root.itemRightClicked(root)
        }

            // --- Gradient overlay top (red, đậm) ---
        Rectangle {
            id: gradientOverlayTop
            x: root.contentBoundsX
            y: root.contentBoundsY
            width: root.contentBoundsWidth
            height: Math.max(24, Math.ceil(root.contentBoundsHeight * 0.16))
            z: ZIndex.gridItemOverlay + 10
            visible: (root.isHovered || root.isSelected) && !(root.itemData && root.itemData.isAnimating)
            layer.enabled: true
            layer.smooth: true
            gradient: Gradient {
                GradientStop { position: 0.0; color: Qt.rgba(0, 0, 0, 0.55) }
                GradientStop { position: 1.0; color: Qt.rgba(0, 0, 0, 0.0) }
            }
            opacity: 1.0
            MouseArea { anchors.fill: parent; enabled: false }
        }
        // --- Gradient overlay bottom (red, đậm) ---
        Rectangle {
            id: gradientOverlayBottom
            x: root.contentBoundsX
            y: root.contentBoundsY + root.contentBoundsHeight - Math.max(24, Math.ceil(root.contentBoundsHeight * 0.16))
            width: root.contentBoundsWidth
            height: Math.max(24, Math.ceil(root.contentBoundsHeight * 0.16))
            z: ZIndex.gridItemOverlay + 10
            visible: (root.isHovered || root.isSelected) && !(root.itemData && root.itemData.isAnimating)
            layer.enabled: true
            layer.smooth: true
            gradient: Gradient {
                GradientStop { position: 0.0; color: Qt.rgba(0, 0, 0, 0.0) }
                GradientStop { position: 1.0; color: Qt.rgba(0, 0, 0, 0.65) }
            }
            opacity: 1.0
            MouseArea { anchors.fill: parent; enabled: false }
        }
    }

    // 🔍 DEBUG: Monitor root content bounds changes to detect "giật giật" issue
    onContentBoundsXChanged: {
    }
    onContentBoundsYChanged: {
    }
    onContentBoundsWidthChanged: {
    }
    onContentBoundsHeightChanged: {
    }

    // // 🔍 DEBUG: Monitor isAnimating state changes for UI hiding
    // Connections {
    //     target: root.itemData
    //     function onIsAnimatingChanged() {
    //         if (root.itemData) {
    //             console.log("🎭 [ANIMATION_STATE] isAnimating changed:", root.itemData.isAnimating,
    //                        "→ UI elements will be", root.itemData.isAnimating ? "HIDDEN" : "SHOWN")
    //         }
    //     }
    // }



    ConnectionStateOverlay {
        id: connectionOverlay
        x: root.contentBoundsX
        y: root.contentBoundsY
        width: root.contentBoundsWidth
        height: root.contentBoundsHeight
        itemData: root.itemData
        isDarkTheme: root.isDarkTheme
        baseZIndex: ZIndex.gridItemOverlay + 11
    }

    // ✅ PERMISSION REQUEST OVERLAY: Load only when needed for PHONE cameras
    Loader {
        id: permissionOverlayLoader
        // ✅ CONTENT BOUNDS: Position within content bounds area
        x: root.contentBoundsX
        y: root.contentBoundsY
        width: root.contentBoundsWidth
        height: root.contentBoundsHeight
        z: ZIndex.gridItemOverlay + 15  // Higher than ConnectionStateOverlay
        active: root.itemData && root.itemData.needsPermission
        sourceComponent: PermissionRequestOverlay {
            itemData: root.itemData
        }
    }

    // ✅ BUTTON CONTROLS LOADER: Load only when needed, hide in fullscreen
    Loader {
        id: buttonControlsLoader
        // ✅ CONTENT BOUNDS: Position within content bounds area
        x: root.contentBoundsX
        y: root.contentBoundsY
        width: root.contentBoundsWidth
        height: root.contentBoundsHeight
        z: ZIndex.gridItemPopup + 1
        active: shouldShowButtonControls
        asynchronous: true

        // ✅ VISIBILITY LOGIC: Show when hovered/selected but hide during fullscreen animations
        property bool shouldShowButtonControls: {
            // Hide during fullscreen transitions (like border canvas)
            if (root.itemData && root.itemData.isAnimating) return false
            // Show when hovered or selected (even in fullscreen)
            // Sửa: Hiện controls khi hovered, hoặc bất kỳ PTZ nào đang active
            return root.isHovered || root.isPtzActive || root.isPtz3dActive || root.isDragZoomActive || root.isSelected
        }

        onActiveChanged: {
        }

        sourceComponent: Component {
            GridItemButtonControls {
                gridItem: root
                itemType: "camera"
                anchors.fill: parent

                // ✅ CONTENT BOUNDS: Already positioned within content bounds by loader
                contentBoundsX: 0
                contentBoundsY: 0
                contentBoundsWidth: parent.width
                contentBoundsHeight: parent.height

                onCloseButtonClicked: function(item) {
                    if (root.gridModel) {
                        root.gridModel.isSave = false
                        root.gridModel.removeItemAt(root.gridRow, root.gridCol)
                    }
                }

                onMaximizeButtonClicked: function(item) {
                    // ✅ CENTRALIZED: Use reusable fullscreen handler
                    if (root.itemData && root.animationManager) {
                        var targetState = !root.itemData.fullscreen
                        root.animationManager.handleFullscreenTransition(
                            root, targetState, "CAMERA_MAXIMIZE"
                        )
                    }
                }

                onChangeActived: function(ptzType) {
                    if (ptzType === "ptz") {
                        if (root.isPtzActive) {
                            root.isPtzActive = false;
                            root.isPtz3dActive = false;
                            root.isDragZoomActive = false;
                            root.activatePtzButton("none");
                        } else {
                            root.isPtzActive = true;
                            root.isPtz3dActive = false;
                            root.isDragZoomActive = false;
                            root.activatePtzButton("ptz");
                        }
                    } else if (ptzType === "ptz3d") {
                        if (root.isPtz3dActive) {
                            root.isPtzActive = false;
                            root.isPtz3dActive = false;
                            root.isDragZoomActive = false;
                            root.activatePtzButton("none");
                        } else {
                            root.isPtzActive = false;
                            root.isPtz3dActive = true;
                            root.isDragZoomActive = false;
                            root.activatePtzButton("ptz3d");
                        }
                    } else if (ptzType === "dragZoom") {
                        if (root.isDragZoomActive) {
                            root.isPtzActive = false;
                            root.isPtz3dActive = false;
                            root.isDragZoomActive = false;
                            root.activatePtzButton("none");
                        } else {
                            root.isPtzActive = false;
                            root.isPtz3dActive = false;
                            root.isDragZoomActive = true;
                            root.activatePtzButton("dragZoom");
                        }
                    }
                }
            }
        }
    }

    // ✅ CAMERA INFO LOADER: Load only when needed, hide in fullscreen
    Loader {
        id: cameraInfoLoader
        // ✅ CONTENT BOUNDS: Position within content bounds area
        x: root.contentBoundsX
        y: root.contentBoundsY
        width: root.contentBoundsWidth
        height: root.contentBoundsHeight
        z: ZIndex.gridItemContent + 1
        active: shouldShowCameraInfo
        asynchronous: true

        // ✅ VISIBILITY LOGIC: Show when hovered/selected but hide during fullscreen animations
        property bool shouldShowCameraInfo: {
            // Hide during fullscreen transitions (like border canvas)
            if (root.itemData && root.itemData.isAnimating) return false
            // Show when hovered or selected (even in fullscreen)
            return root.isHovered || root.isSelected
        }

        sourceComponent: Component {
            Rectangle {
                id: cameraInfoContainer
                // ✅ CONTENT BOUNDS: Position within content bounds area (loader already positioned)
                anchors.top: parent.top
                anchors.topMargin: 5
                anchors.horizontalCenter: parent.horizontalCenter
                property real maxWidth: parent.width * 0.6
                width: parent.width
                height: cameraInfoDisplay.height + 8
                color: "#00000060"
                radius: 6

                Row {
                    id: cameraInfoDisplay
                    anchors.horizontalCenter: parent.horizontalCenter
                    anchors.topMargin: 1
                    spacing: 5

                    Image {
                        id: cameraStateIcon
                        width: 15
                        height: 15
                        anchors.verticalCenter: parent.verticalCenter
                        source: getStateCamIconByItemData(root.itemData.cameraModel)
                        fillMode: Image.PreserveAspectFit
                        smooth: true
                    }

                    Text {
                        id: cameraNameText
                        text: getCameraName()
                        color: "white"
                        font.pixelSize: Math.max(10, Math.min(14, root.width / 25))
                        font.weight: Font.Medium
                        anchors.verticalCenter: parent.verticalCenter
                        width: Math.min(cameraInfoContainer.maxWidth - cameraStateIcon.width - 10, implicitWidth)
                        wrapMode: Text.NoWrap
                        elide: Text.ElideRight
                        maximumLineCount: 1

                        function getCameraName() {
                            if (root.itemData && root.itemData.cameraModel.name) {
                                return root.itemData.cameraModel.name
                            }
                            if (root.itemData && root.itemData.name) {
                                return root.itemData.name
                            }
                            return "Camera " + (root.position + 1)
                        }
                    }
                }
            }
        }
    }
    // Drag Zoom Control - Drag-to-zoom functionality
    DragZoomControl {
        id: dragZoomControl
        anchors.fill: parent
        isDarkTheme: root.isDarkTheme
        z: ZIndex.gridItemPopup
        visible: root.itemData ? root.itemData.isDragZoomActive : false

        onDragZoom: function(startPoint, endPoint) {
            if (gridModel && gridModel.setDragToZoom) {
                // Tính toán tâm vùng drag
                var centerX = (startPoint.x + endPoint.x) / 2
                var centerY = (startPoint.y + endPoint.y) / 2

                // Tính toán kích thước vùng drag
                var rectWidth = Math.abs(endPoint.x - startPoint.x)
                var rectHeight = Math.abs(endPoint.y - startPoint.y)

                // Tính toán zoom factor dựa trên tỷ lệ vùng drag
                var zoomFactor = Math.min(width / rectWidth, height / rectHeight)

                // Chuẩn hóa tọa độ tâm về [-1, 1]
                var normalizedX = (centerX - width/2) / (width/2)
                var normalizedY = (centerY - height/2) / (height/2)

                gridModel.setDragToZoom(
                    position,
                    startPoint.x, startPoint.y,
                    endPoint.x, endPoint.y,
                    width, height,
                    normalizedX, normalizedY,
                    zoomFactor
                )
            }
        }

        onDragZoomClosed: {
            root.activatePtzButton("none")
        }
    }
    
    // PTZ 3D Control - Mouse-based PTZ control
    PTZ3DControl {
        id: ptz3dControl
        anchors.fill: parent
        isDarkTheme: root.isDarkTheme
        z: ZIndex.gridItemPopup
        visible: root.itemData ? root.itemData.isPtz3dActive : false

        onPtzMove: function(startPoint, endPoint) {
            if (gridModel && gridModel.controlPTZ3D) {
                gridModel.controlPTZ3D(position, startPoint, endPoint, videoItem.width, videoItem.height)
            }
        }

        onPtzStop: {
            if (gridModel && gridModel.stopPTZ) {
                gridModel.stopPTZ(position)
            }
        }

        onPtzClosed: {
            root.activatePtzButton("none")
        }

        onPtzZoom: function(factor) {
            if (gridModel && gridModel.controlPTZZoom) {
                gridModel.controlPTZZoom(position, factor)
                gridModel.stopPTZ(position)
            }
        }
    }

    // PTZ Control Panel - Traditional directional buttons
    PTZControlPanel {
        id: ptzControlPanel
        parent: root.parent // Set parent to CameraGrid to avoid clipping

        // Position to the right of the camera
        x: root.contentBoundsX + root.width + 50
        y: root.contentBoundsY

        width: 180
        height: 240
        color: backgroundColor
        buttonSize: 24  // Much smaller buttons for compact design
        iconSize: 24   // Much smaller icons for compact design
        z: ZIndex.gridItemPopup
        visible: root.itemData ? root.itemData.isPtzActive : false  // Changed default to false


        // PTZ movement signals
        onPtzMove: function(direction) {
            root.sendPTZCommand(direction, speedValue)
        }


        onPtzZoom: function(factor) {
            if (factor > 0) {
                root.sendPTZCommand("zoom-in", Math.abs(factor))
            } else {
                root.sendPTZCommand("zoom-out", Math.abs(factor))
            }
        }

        onPtzStop: {
            // console.log("🎮 [PTZ_PANEL] PTZ Stop")
            root.sendPTZStopCommand()
        }

        onPtzClose: {
            // console.log("🎮 [PTZ_PANEL] PTZ Close")
            root.activatePtzButton("none")
        }

        onPtzSpeed: function(speed) {
            // console.log("🎮 [PTZ_PANEL] PTZ Speed:", speed)
            root.sendPTZSpeed(speed)
        }
    }


    Timer {
        id: checkHoverTimer
        interval: 100
        repeat: false
        onTriggered: {
            if (!root.isHovered &&
                !ptzControlPanel.visible &&
                !ptz3dControl.visible &&
                !dragZoomControl.visible &&
                !isPtzActive &&
                !isPtz3dActive &&
                !isDragZoomActive) {
                root.isHovered = false
            }
        }
    }

    GridItemActionHandler {
        id: actionHandler
        gridItem: root
        isDarkTheme: root.isDarkTheme
        isSelected: root.isSelected
        isMaximized: root.itemData ? root.itemData.fullscreen : false
        isVirtualGrid: root.isVirtualGrid
        z: ZIndex.gridItemActionHandler
        anchors.fill: parent
        contextMenuLoader: contextMenuLoader
    }

    function getStateCamIconByItemData(itemData) {
        if (!itemData || itemData.state_merged === undefined)
            return "qrc:/src/assets/treeview_and_menu_treeview/norec_unpin.svg"

        switch (itemData.state_merged) {
        case CommonEnum.CameraState.CONNECTED_REC_PIN:
            return "qrc:/src/assets/treeview_and_menu_treeview/rec_pin.svg"
        case CommonEnum.CameraState.CONNECTED_REC_UNPIN:
            return "qrc:/src/assets/treeview_and_menu_treeview/rec_unpin.svg"
        case CommonEnum.CameraState.CONNECTED_NOREC_PIN:
            return "qrc:/src/assets/treeview_and_menu_treeview/norec_pin.svg"
        case CommonEnum.CameraState.CONNECTED_NOREC_UNPIN:
            return "qrc:/src/assets/treeview_and_menu_treeview/norec_unpin.svg"
        case CommonEnum.CameraState.DISCONNECTED_REC_PIN:
            return "qrc:/src/assets/treeview_and_menu_treeview/rec_pin.svg"
        case CommonEnum.CameraState.DISCONNECTED_REC_UNPIN:
            return "qrc:/src/assets/treeview_and_menu_treeview/rec_unpin.svg"
        case CommonEnum.CameraState.DISCONNECTED_NOREC_PIN:
            return "qrc:/src/assets/treeview_and_menu_treeview/norec_pin.svg"
        default:
            return "qrc:/src/assets/treeview_and_menu_treeview/norec_unpin.svg"
        }
    }
}
