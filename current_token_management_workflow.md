# Current Token Management Workflow

## Overview

Hệ thống token management hiện tại đã được implement hoàn chỉnh với comprehensive workflow từ API level đến UI level, xử lý tất cả các scenarios của token lifecycle.

## Complete Token Management Architecture

### **1. Token Types & Lifecycle**

```
┌─────────────────┐    ┌──────────────────┐
│   access_token  │    │  refresh_token   │
│   (15-30 min)   │    │   (7-30 days)    │
└─────────────────┘    └──────────────────┘
         │                       │
         ▼                       ▼
    Auto refresh              Manual refresh
    (seamless)               (re-login only)
```

### **2. Core Components Integration**

```
API Client ←→ WebSocket Client ←→ Camera Models ←→ Camera Grid Items ←→ QML UI
     ↓              ↓                ↓               ↓              ↓
Token retry    Token refresh    Signal emit    State management   UI display
```

## Detailed Workflow Scenarios

### **Scenario 1: Normal Access Token Refresh (Seamless)**

#### **Flow:**
```
1. API Request → 401 Unauthorized
2. API Client: _retry_request_with_token_refresh()
3. Check: refresh_token exists ✅
4. Call: refresh_access_token()
5. Server Response: { "accessToken": "new_token" }
6. Update: ONLY access_token (refresh_token unchanged)
7. WebSocket: Notify all camera models
8. Camera Models: accessTokenChanged.emit()
9. Camera Grid Items: on_access_token_changed()
10. Refresh stream URLs with new token
11. Continue operation seamlessly ✅
```

#### **Expected Logs:**
```
🔑 [TOKEN_RETRY] get_stream_url got 401, attempting token refresh
✅ [REFRESH] Access token refreshed successfully
✅ [REFRESH] access_token: old... → new...
✅ [REFRESH] refresh_token: UNCHANGED (as expected)
✅ [TOKEN_RETRY] Token refreshed successfully, retrying get_stream_url
✅ Notifying X camera models of token change
✅ [TOKEN_REFRESH] Access token changed for camera xxx
✅ [TOKEN_REFRESH] Updated stream URL for camera xxx
```

### **Scenario 2: Refresh Token Expiration (Critical)**

#### **Flow:**
```
1. API Request → 401 Unauthorized
2. API Client: _retry_request_with_token_refresh()
3. Check: refresh_token exists ✅
4. Call: refresh_access_token()
5. Server Response: {"code":"400","message":"Invalid token"}
6. Detect: Token expiration (enhanced detection)
7. Clear: All tokens from memory/storage
8. Return: "REFRESH_TOKEN_EXPIRED"
9. WebSocket: _notify_token_expired()
10. Camera Models: tokenExpired.emit()
11. Camera Grid Items: on_token_expired()
    - Stop video player immediately
    - Set connectionState = "unauthorized"
    - Emit connectionStateChanged signal
12. QML: ConnectionStateOverlay shows unauthorized
13. WebSocket: _trigger_relogin_ui()
14. Show: ReLoginDialog for user ✅
```

#### **Expected Logs:**
```
🔑 [TOKEN_RETRY] get_stream_url got 401, attempting token refresh
🔄 [REFRESH] Response status: 200
🔄 [REFRESH] Response text: {"code":"400","message":"Invalid token"}
🔴 [REFRESH] Refresh token is invalid/expired - user needs to login again
🧹 [CLEANUP] Clearing expired tokens for server
🔴 REFRESH TOKEN EXPIRED for server!
🔴 [TOKEN_EXPIRED] Notifying X camera models of token expiration
🔴 [TOKEN_EXPIRED] Emitted tokenExpired signal for camera xxx
🔴 [TOKEN_EXPIRED] All tokens expired for camera xxx
🔴 [TOKEN_EXPIRED] Stopping video stream and showing unauthorized state
🔑 [RELOGIN] Showing re-login dialog for user
```

### **Scenario 3: Tokens Already Cleared (Edge Case)**

#### **Flow:**
```
1. API Request → 401 Unauthorized
2. API Client: _retry_request_with_token_refresh()
3. Check: refresh_token = None ❌
4. Log: "No refresh token available"
5. Call: _notify_token_expired_from_api()
6. Direct notify: camera_model_manager.get_camera_list()
7. Camera Models: tokenExpired.emit()
8. Camera Grid Items: on_token_expired()
9. QML: ConnectionStateOverlay shows unauthorized ✅
```

#### **Expected Logs:**
```
🔑 [TOKEN_RETRY] Current access_token: None
🔑 [TOKEN_RETRY] Current refresh_token: None
🔴 [TOKEN_RETRY] No refresh token available
🔴 [TOKEN_RETRY] Tokens were already cleared - need to notify components
🔴 [TOKEN_EXPIRED] Direct notify cameras for server
🔴 [TOKEN_EXPIRED] Stopping X cameras
🔴 [TOKEN_EXPIRED] Stopped camera xxx
```

## UI State Management

### **ConnectionStateOverlay.qml States:**

| State | Icon | Text | Color | Behavior |
|-------|------|------|-------|----------|
| **connecting** | Loading | "Connecting" | Yellow (#F6BE00) | Show progress |
| **buffering** | Loading | "X%" | Yellow (#F6BE00) | Show percentage |
| **stopped** | Logo | (none) | White | Show logo only |
| **unauthorized** | unauthorized.svg | "Unauthorized" | Red (#FF4444) | Show error state |
| **started** | (hidden) | (hidden) | (hidden) | Video playing |

### **QML Integration:**
```qml
// Enhanced visibility logic
visible: {
    var isVisible = state !== "started" // includes "unauthorized"
    return isVisible
}

// Enhanced text display
text: {
    if (root.itemData.connectionState === "unauthorized") {
        displayText = qsTr("Unauthorized")  // ✅ User customized
    }
    return displayText
}

// Enhanced icon logic
source: {
    if (root.itemData && root.itemData.connectionState === "unauthorized") {
        return "qrc:/src/assets/state/unauthorized.svg"  // ✅ User customized
    }
    return "qrc:/src/assets/login_screen/logo.png"
}
```

## Token Policy Configuration

### **Current Policy:**
```python
# In refresh_access_token()
ALLOW_REFRESH_TOKEN_ROTATION = False  # Chỉ update khi connect lại

# Policy enforcement:
if ALLOW_REFRESH_TOKEN_ROTATION:
    # Update refresh_token (DISABLED)
else:
    # Keep refresh_token unchanged (ENABLED)
    logger.warning("refresh_token chỉ update khi connect lại")
```

### **Login vs Auto-Refresh Behavior:**

| Action | access_token | refresh_token | Notes |
|--------|-------------|---------------|-------|
| **Login/Connect** | ✅ Update | ✅ Update | Both tokens from server |
| **Auto Refresh** | ✅ Update | ❌ Keep unchanged | Policy enforcement |
| **Token Expired** | ❌ Clear | ❌ Clear | Force re-login |

## Signal Flow Architecture

### **Complete Signal Chain:**
```
Server Error → API Client → WebSocket Client → Camera Models → Camera Grid Items → QML UI

Specific signals:
1. accessTokenChanged: Normal token refresh (success)
2. refreshTokenChanged: Token rotation warning (if enabled)
3. tokenExpired: Complete token failure (critical)
4. connectionStateChanged: UI state updates
5. accessTokenRefreshed: QML notification
```

### **Camera Grid Item Signal Handlers:**
```python
# Signal connections
self.cameraModel.accessTokenChanged.connect(self.on_access_token_changed)
self.cameraModel.refreshTokenChanged.connect(self.on_refresh_token_changed)
self.cameraModel.tokenExpired.connect(self.on_token_expired)

# Handler actions
def on_access_token_changed(self):
    # Refresh stream URL with new token
    # Continue video seamlessly

def on_refresh_token_changed(self):
    # Monitor for potential auth issues
    # Log warnings about token rotation

def on_token_expired(self):
    # Stop video player immediately
    # Set connectionState = "unauthorized"
    # Show ConnectionStateOverlay
```

## Error Detection & Recovery

### **Enhanced Token Expiration Detection:**
```python
# Multiple error message patterns
is_token_expired = (
    'invalid token' in error_lower or      # ✅ "Invalid token"
    'token expired' in error_lower or      # ✅ "Token expired"
    'refresh' in error_lower or            # ✅ "Refresh expired"
    'expired' in error_lower or            # ✅ Generic "expired"
    'token not found' in error_lower or    # ✅ "Token not found"
    'unauthorized' in error_lower          # ✅ "Unauthorized"
)
```

### **Recovery Mechanisms:**
1. **Auto Recovery**: Normal access_token refresh (seamless)
2. **Manual Recovery**: Re-login dialog when refresh_token expires
3. **Fallback Recovery**: Direct camera notification when WebSocket fails

## Benefits of Current Implementation

### ✅ **Complete Coverage**
- **All token scenarios**: Normal refresh, expiration, already cleared
- **All components**: API, WebSocket, Models, Grid Items, QML
- **All error cases**: Network issues, server errors, token problems

### ✅ **User Experience**
- **Seamless operation**: Normal token refresh invisible to user
- **Clear feedback**: Unauthorized state with proper UI
- **Guided recovery**: Re-login dialog when needed

### ✅ **Developer Experience**
- **Comprehensive logging**: Full visibility into token operations
- **Configurable policy**: Easy to change token behavior
- **Robust error handling**: Graceful degradation on failures

### ✅ **Security & Control**
- **Strict token policy**: refresh_token only changes on re-login
- **Proper cleanup**: Expired tokens cleared from memory/storage
- **Forced re-authentication**: Security boundary enforcement

## Current Status: ✅ PRODUCTION READY

The token management system is **complete and robust**, handling all scenarios from normal operation to critical failures with appropriate user feedback and recovery mechanisms.

**Key Features:**
- 🔄 **Seamless token refresh** for normal operation
- 🔴 **Proper unauthorized state** for token expiration
- 🔑 **Guided re-login flow** for user recovery
- 🛡️ **Strict security policy** for token management
- 📊 **Comprehensive logging** for debugging and monitoring
