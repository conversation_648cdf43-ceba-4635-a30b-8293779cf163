# Authentication Workflow Analysis

## Overview
Phân tích workflow giữa các component chính trong hệ thống authentication và token management của iVMS.

## Core Components

### 1. `AuthQSettings` - Token Storage Layer
**File**: `src/utils/auth_qsettings.py`

**Chức năng**: Persistent storage cho tokens
```python
class AuthQSettings:
    def get_access_token(self) -> str        # Lấy access_token từ QSettings key "jwt"
    def get_refresh_token(self) -> str       # Lấy refresh_token từ QSettings key "refreshToken"  
    def save_access_token(self, jwt)         # Lưu access_token vào QSettings
    def save_refresh_token(self, token)      # Lưu refresh_token vào QSettings
    def delete_access_token(self)            # Xóa access_token
    def delete_refresh_token(self)           # Xóa refresh_token
```

**Vấn đề hiện tại**:
- ❌ Tokens được lưu globally, không phân biệt theo server
- ❌ Không có validation khi load tokens
- ❌ Không có expiration time tracking

---

### 2. `APIClient` - HTTP API Layer  
**File**: `src/api/api_client.py`

**Chức năng**: Xử lý HTTP requests và token management
```python
class APIClient:
    def __init__(self, server):
        self.access_token = None          # ❌ Luôn khởi tạo = None
        self.refresh_token = None         # ❌ Không load từ storage
        
    def login(self, username, password):
        # ✅ Gọi API login
        # ✅ Lưu access_token vào AuthQSettings  
        # ❌ KHÔNG lưu refresh_token vào AuthQSettings
        
    def refresh_access_token(self):
        # ✅ Gọi API refresh token
        # ✅ Lưu access_token mới vào AuthQSettings
        # ❌ KHÔNG lưu refresh_token mới vào AuthQSettings
        # ❌ KHÔNG xử lý khi refresh_token hết hạn (401/403)
```

**Vấn đề hiện tại**:
- ❌ Constructor không load stored tokens
- ❌ refresh_token không được persist
- ❌ Không xử lý refresh_token expiration
- ❌ Mỗi lần tạo APIClient mới = tokens bị reset

---

### 3. `Controller` - Business Logic Layer
**File**: `src/common/controller/controller_manager.py`

**Chức năng**: Orchestrate authentication flow
```python
class Controller:
    def login(self):
        # ❌ Luôn gọi API login, không check existing tokens
        login_thread = APIThread(target=self.api_client.login, callback=self.callback_login)
        
    def refresh_access_token(self):
        return self.api_client.refresh_access_token()  # ✅ Delegate to APIClient
```

**Vấn đề hiện tại**:
- ❌ Không validate existing tokens trước khi login
- ❌ Không có logic retry khi token expired
- ❌ Mỗi lần connect = tạo Controller mới = APIClient mới = tokens reset

---

### 4. `WebsocketClient` - Real-time Communication Layer
**File**: `src/common/websocket/websocket_client.py`

**Chức năng**: Handle WebSocket authentication và auto-refresh
```python
class WebsocketClient:
    def on_error(self, ws, error):
        if error.status_code == HTTPStatus.UNAUTHORIZED:
            ok = self.refresh_token()  # ✅ Auto refresh on 401
            
    def refresh_token(self):
        controller = controller_manager.get_controller(server_ip=self.server_ip)
        access_token = controller.refresh_access_token()
        if access_token:
            self.header['Authorization'] = f"Bearer {access_token}"
            map_model.accessTokenChanged.emit()  # ✅ Notify UI components
```

**Điểm mạnh**:
- ✅ Auto-refresh khi gặp 401 error
- ✅ Notify UI components về token changes
- ✅ Proper error handling

---

## Current Workflow Problems

### Problem 1: Token Loss on Reconnect
```
User clicks "Connect" 
→ new Controller() 
→ new APIClient() 
→ tokens = None 
→ 401 error 
→ Login dialog appears
```

### Problem 2: Incomplete Token Persistence
```
Login successful 
→ access_token saved ✅
→ refresh_token NOT saved ❌
→ Next connect = no refresh_token available
```

### Problem 3: No Token Validation
```
APIClient created 
→ No check for existing valid tokens
→ Always assume need to login
→ Unnecessary login dialogs
```

## Recommended Starting Points

### 🎯 **Priority 1: Fix Token Persistence**
**File**: `src/api/api_client.py`
- Add `load_stored_tokens()` in constructor
- Save refresh_token in both `login()` and `refresh_access_token()`

### 🎯 **Priority 2: Add Token Validation**  
**File**: `src/api/api_client.py`
- Add `validate_tokens()` method
- Add `handle_refresh_token_expired()` method

### 🎯 **Priority 3: Smart Login Logic**
**File**: `src/common/controller/controller_manager.py`  
- Add `try_existing_tokens()` before login
- Handle refresh_token expiration gracefully

### 🎯 **Priority 4: Improve Storage**
**File**: `src/utils/auth_qsettings.py`
- Add server-specific token storage
- Add token expiration tracking

## Implementation Order

1. **Fix APIClient constructor** - Load existing tokens
2. **Fix token persistence** - Save both tokens properly  
3. **Add token validation** - Check before API calls
4. **Add smart login** - Use existing tokens when possible
5. **Handle expiration** - Clear tokens when refresh fails
6. **Test integration** - Verify end-to-end flow

## Expected Outcome

After fixes:
```
User clicks "Connect"
→ Controller created
→ APIClient loads existing tokens
→ Validate tokens with test API call
→ If valid: Skip login, connect directly
→ If expired: Auto-refresh tokens  
→ If refresh fails: Show login dialog
→ Seamless user experience ✅
```
