import json
import sys
import platform
import uuid
import hashlib
from src.common.onvif_api.camera_model import Resolution
from typing import List
import datetime
import logging
logger = logging.getLogger(__name__)
import json
import re
import subprocess  # For executing a shell command
from PySide6.QtCore import QDateTime, QTimeZone, QTime, QDate
from PySide6.QtGui import QColor
import logging
logger = logging.getLogger(__name__)

class Utils:
    MaxEvent = 5

    def getClientId():
        data = (
            platform.node() +       # hostname
            platform.system() +     # OS
            platform.machine() +    # x86_64, etc
            str(uuid.getnode())     # MAC address
        )
        return hashlib.sha256(data.encode()).hexdigest()
    clientId = getClientId()
    def resolution_to_dict(input_string: List[Resolution]=[]):
        json_resolution = json.loads(input_string)
        list_resolution = []
        for item in json_resolution:
            list_resolution.append(Resolution.from_dict(item))
        return list_resolution
    # def fps_to_dict(input_string: List[Resolution]=[]):
    #     json_resolution = json.loads(input_string)
    #     list_resolution = []
    #     for item in json_resolution:
    #         list_resolution.append(Resolution(**item))
    #     return list_resolution
    def convert_format_time(time = None):
        # time_obj = isoparse(time)
        # Đặt múi giờ mong muốn (ở đây là +07:00)
        # timezone = pytz.timezone("Asia/Bangkok")
        # time_with_timezone = time_obj.astimezone(timezone)
        # Chuyển đổi sang định dạng "2024-05-16T09:23:09.332283+07:00"
        # return time_obj.isoformat()
        dt = datetime.datetime.strptime(time, "%Y-%m-%d %H:%M:%S")

        # Convert to ISO 8601 format with UTC time zone indicator
        return dt.strftime("%Y-%m-%dT%H:%M:%SZ")
    
    def time_to_string(time:datetime.datetime = None):
        time_string = time.strftime("%Y-%m-%d %H:%M:%S")
        return time_string
    
    def convert_string_to_time(time = None):
        # print(f'convert_string_to_time {time}')
        time_format = "%Y-%m-%dT%H:%M:%SZ"
        return datetime.datetime.strptime(time,time_format) 
    
    def convert_today_to_string():
        current_time = datetime.datetime.today().strftime("%Y-%m-%dT%H:%M:%SZ")
        return current_time
    
    def make_divisible_by_eight(number):
        remainder = number % 8
        if remainder != 0:
            number += 8 - remainder
        return number
    

    def convert_string_to_coordinate_points(points_from_dict: str) -> List[tuple[int, int]]:
        if points_from_dict is None:
            return []
        try:
            points = []
            # parser string to list points
            points_from_dict = points_from_dict.replace('[', '')
            points_from_dict = points_from_dict.replace(']', '')
            points_from_dict = points_from_dict.split(',')
            for i in range(0, len(points_from_dict), 2):
                points.append((int(points_from_dict[i]), int(points_from_dict[i+1])))
        except Exception as e:
            points = []
        return points

    def convert_coordinate_points_to_string(coordinate_points):
        if coordinate_points is None:
            return None
        try:
            list_coordinate_points = []
            for point in coordinate_points:
                list_coordinate_points.append([point[0], point[1]])
            return json.dumps(list_coordinate_points)
        except Exception as e:
            return None

    def calculate_center_of_polygon(points):
        x_list = [point[0] for point in points]
        y_list = [point[1] for point in points]
        x = sum(x_list) / len(points)
        y = sum(y_list) / len(points)
        return (x, y)
    

    # convert points width scale size
    def convert_points_to_scale(points, scale_x, scale_y):
        points_scale = []
        for point in points:
            points_scale.append((int(point[0] * scale_x), int(point[1] * scale_y)))
        return points_scale
    


    # thoiGianXuatHienConverted = datetime.datetime.strptime(event.createdAtLocalDate, "%Y-%m-%dT%H:%M:%S.%f%z")
    # timezone_offset = thoiGianXuatHienConverted.utcoffset()
    # local_timezone = datetime.timezone(timezone_offset)
    # thoiGianXuatHienConverted = thoiGianXuatHienConverted.replace(tzinfo=local_timezone)
    def convert_time_format_utc(time, format):
        timeConverted = datetime.datetime.strptime(time, format)
        timezone_offset = timeConverted.utcoffset()
        local_timezone = datetime.timezone(timezone_offset)
        timeConverted = timeConverted.replace(tzinfo=local_timezone)
        return timeConverted
    
    def convert_same_time_utc(time: datetime.datetime, time_need_match_utc: datetime.datetime):
        time_need_match_utc = time_need_match_utc.utcoffset()
        local_timezone = datetime.timezone(time_need_match_utc)
        time_converted = time.replace(tzinfo=local_timezone)
        return time_converted
    
    def convert_string_to_duration(time:str)->int:
        if time.endswith("Z"):
            time = time.replace("Z", "+00:00")
            dt = datetime.datetime.fromisoformat(time)
        else:
            dt = datetime.datetime.fromisoformat(time)
        duration = int(dt.timestamp() * 1000)
        return duration
    
    def is_macos():
        return sys.platform == 'darwin'
    
    def is_linux():
        return sys.platform == 'linux'
    
    def is_windows():
        return sys.platform == 'win32'
    
    def scan_address_ip(rtsp):
        # Phuong phap detect ip username duoi day chi dung khi 'username' va 'password' khong chua ky tu dac biet la (':','/')
        logger.debug(f'rtsp = {rtsp}')
        # detect 1
        # format  rtsp://user:password@<DeviceIP>:port/profile
        try:
            url = re.findall(r'rtsp://(.*?):(.*?):', rtsp)[0]
            if len(url) == 2:
                username = url[0]
                pass_ip = url[1]
                list_string = pass_ip.split('@')
                if len(list_string) > 1:
                    ip = list_string[-1]
                    password = pass_ip.split('@' + ip)[0]
                    logger.debug(f'ip = {ip}')
                    logger.debug(f'password = {password}')
                    return {'username': username,'ip': ip, 'password': password, 'port': 80,'detect':'success'}
                else:
                    pass
            else:
                pass
        except Exception as e:
            logger.error(f'error = {e}')
            pass
        # detect 2
        # format rtsp://user:password@<ip>/
        try:
            url = re.findall(r'rtsp://(.*?)/', rtsp)[0]
            username, pass_ip = url.split(':')
            list_string = pass_ip.split('@')
            if len(list_string) > 1:
                ip = list_string[-1]
                password = pass_ip.split('@' + ip)[0]
                return {'username': username,'ip': ip, 'password': password, 'port': 80,'detect':'success'}
            else:
                pass

        except Exception as e:
            logger.error(f'error = {e}')
            pass
        # detect 3
        # format rtsp://user:password@<ip>
        try:
            # detect 'rtsp://' co trong link khong
            protocol,url2 = rtsp.split('rtsp://')
            username, pass_ip = url2.split(':')
            list_string = pass_ip.split('@')
            if len(list_string) > 1:
                ip = list_string[-1]
                password = pass_ip.split('@' + ip)[0]
                return {'username': username,'ip': ip, 'password': password, 'port': 80,'detect':'success'}
            else:
                pass
        except Exception as e:
            logger.error(f'error = {e}')
            pass
        # detect 4
        # format rtsp://<ip>:<port>/
        try:
            url = re.findall(r'rtsp://(.*?)/', rtsp)[0]
            ip, port = url.split(':')
            return {'username': None,'ip': ip, 'password': None, 'port': 80,'detect':'success'}
        except Exception as e:
            logger.error(f'error = {e}')
            pass

        # detect 5
        # format rtsp://<ip>/
        try:
            ip = re.findall(r'rtsp://(.*?)/', rtsp)[0]
            return {'username': None,'ip': ip, 'password': None, 'port': 80,'detect':'success'}
        except Exception as e:
            logger.error(f'error = {e}')
            pass
        
        # detect 6
        # format rtsp://<ip>
        try:
            protocol, ip = rtsp.split('rtsp://')
            return {'username': None,'ip': ip, 'password': None, 'port': 80,'detect':'success'}
        except Exception as e:
            logger.error(f'error = {e}')
            pass

        return {'username': None,'ip': None, 'password': None, 'port': 80,'detect':'error'}

    def ping(host):
        """
        Returns True if host (str) responds to a ping request.
        Remember that a host may not respond to a ping (ICMP) request even if the host name is valid.
        """

        # Option for the number of packets as a function of
        param = '-n' if platform.system().lower() == 'windows' else '-c'

        # Building the command. Ex: "ping -n 1 google.com" on Windows
        command = ['ping', param, '1', host]

        # Use CREATE_NO_WINDOW flag on Windows to suppress the command window
        print(f'Utils: ping: {command}')
        creation_flags = subprocess.CREATE_NO_WINDOW if platform.system().lower() == 'windows' else 0
        startupinfo = None
        if platform.system().lower() == 'windows':
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
        # Redirect stdout and stderr to subprocess.DEVNULL to suppress output
        process = subprocess.Popen(command, creationflags=creation_flags, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL, startupinfo=startupinfo)
        process.wait()

        return process.returncode == 0

    #TimelineController

    OFFSET_FROM_UTC = QDateTime.currentDateTime().offsetFromUtc()
    # Constants
    D1S = 1000
    D5S = 5000
    D10S = 10000
    D30S = 30000
    D1MIN = 60000
    D5MIN = 300000
    D10MIN = 600000
    D30MIN = 1800000
    D1H = 3600000
    D3H = 10800000
    D12H = 43200000
    D1D = 86400000
    D1W = 604800000
    @staticmethod
    def convertTimeToString(milliseconds: int) -> str:
        seconds = milliseconds // 1000
        days = seconds // (24 * 3600)
        seconds = seconds % (24 * 3600)
        hours = seconds // 3600
        seconds %= 3600
        minutes = seconds // 60
        seconds %= 60
        milliseconds_part = milliseconds % 1000
        time_str = f"{days:02d}:{hours:02d}:{minutes:02d}:{seconds:02d}:{milliseconds_part:03d}"
        return time_str[3:] if days == 0 else time_str

    @staticmethod
    def convertMilliSecondTimeToString(milliseconds: int) -> str:
        # print(f'milliseconds = {milliseconds}')
        dt = QDateTime.fromMSecsSinceEpoch(milliseconds)
        return dt.toString("yyyy-MM-ddThh:mm:ssZ")
    
        # # Chuyển đổi milliseconds thành giây (chia cho 1000)
        # dt = datetime.fromtimestamp(milliseconds / 1000)
        
        # # Trả về chuỗi theo định dạng yêu cầu
        # return dt.strftime("%Y-%m-%dT%H:%M:%SZ")
    @staticmethod
    def convertDurationToString(duration: int) -> str:
        # Convert to UTC datetime
        dt = datetime.datetime.fromtimestamp(duration / 1000, tz=datetime.timezone.utc)

        # Format ISO 8601 with milliseconds + Z
        iso_str = dt.isoformat(timespec='milliseconds').replace('+00:00', 'Z')
        return iso_str
    
    @staticmethod
    def getTimeStringFromMillisecond(milliseconds: int) -> str:
        dt = QDateTime.fromMSecsSinceEpoch(milliseconds)
        return dt.time().toString("hh:mm:ss")

    @staticmethod
    def getOrdinalSuffix(day: int) -> str:
        if 11 <= day <= 13:
            return "th"
        match day % 10:
            case 1: return "st"
            case 2: return "nd"
            case 3: return "rd"
            case _: return "th"

    @staticmethod
    def getTimeStepString(milliseconds: int, is_real_date: bool = False) -> str:
        if is_real_date:
            time = QDateTime.fromMSecsSinceEpoch(int(milliseconds)).toTimeZone(
                QTimeZone.fromSecondsAheadOfUtc(Utils.OFFSET_FROM_UTC)
            )

            if time.time().msec() != 0:
                return f"{time.time().msec()}ms"
            elif time.time().second() != 0:
                return f"{time.time().second()}s"
            elif time.time().minute() != 0:
                # return time.toString("h:mm AP")
                return time.toString("HH:mm")
            elif time.time().hour() != 0:
                # return time.toString("h AP")
                return time.toString("HH:mm")
            else:
                return str(time.date().day())

        # Non-real date calculations
        value = milliseconds % 1000
        if value > 0:
            return f"{value}ms"

        value = milliseconds % 60000
        if value > 0:
            return f"{value//1000}s"

        value = milliseconds % 3600000
        if value > 0:
            return f"{value//60000}m"

        value = milliseconds % 86400000
        if value > 0:
            return f"{value//3600000}h"

        value = milliseconds % 604800000
        if value > 0:
            return f"{value//86400000}d"

        value = milliseconds // 604800000
        if value > 0:
            return f"{value}w"

        return ""

    @staticmethod
    def isRoundedBy(target: int, unit: int) -> bool:
        return target % unit == 0

    @staticmethod
    def roundedBy(target: int, unit: int, is_real_date: bool = False) -> int:
        if not is_real_date:
            return target - (target % unit)

        # Get offset milliseconds from UTC to current local time
        offset = Utils.OFFSET_FROM_UTC * 1000
        # Convert target to local time
        target += offset
        return target - (target % unit) - offset

    @staticmethod
    def getSubItemCount(unit: int) -> int:
        secs = unit // 1000
        mins = unit // 60000
        hours = unit // 3600000
        ds = unit // 86400000
        ws = unit // 604800000
        _2ws = unit // 1209600000
        _4ws = unit // 2419200000
        _8ws = unit // 4838400000
        _13ws = unit // 7862400000
        _26ws = unit // 15724800000
        _52ws = unit // 31449600000
        if unit == 100:
            return 0
        elif unit == 500:
            return 5
        elif secs == 5 or mins == 5:
            return 5
        elif hours == 3:
            return 3
        elif secs == 10 or mins == 10 or hours == 10:
            return 2
        elif secs == 30 or mins == 30 or hours == 30:
            return 3
        elif secs == 1 or mins == 1 or hours == 1 or ds == 1:
            return 2
        elif hours == 12:
            return 4
        elif ws == 1:
            return 7
        elif _2ws == 1:
            return 2
        elif _4ws == 1:
            return 2
        elif _8ws == 1:
            return 8
        elif _13ws == 1:
            return 13
        elif _26ws == 1:
            return 26
        elif _52ws == 1:
            return 52
        return 0
    
    # write function convert (255, 0, 0) to #FF0000
    def convert_rgb_to_hex(rgb: tuple[int, int, int]) -> str:
        return '#{:02X}{:02X}{:02X}'.format(rgb[0], rgb[1], rgb[2])
    
    # write function convert #FF0000 to (255, 0, 0)
    def convert_hex_to_rgb(hex_color: str) -> tuple[int, int, int]:
        # Remove # prefix if present
        if hex_color.startswith('#'):
            hex_color = hex_color[1:]
        return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
    
    def is_valid_hex_color(hex_color: str) -> bool:
        if hex_color.startswith('#'):
            hex_color = hex_color[1:]
        if len(hex_color) != 6:
            return False
        return all(c in '0123456789abcdef' for c in hex_color)
    
    def is_valid_rgb_color(rgb: tuple[int, int, int]) -> bool:
        return all(0 <= c <= 255 for c in rgb)

    @staticmethod
    def extract_widget_address(widget_address: str) -> str:
        """
        Trích xuất địa chỉ bộ nhớ của widget từ chuỗi widget_address.

        Phương thức này lấy địa chỉ bộ nhớ của bất kỳ widget nào trong PySide6 từ chuỗi đại diện của nó.
        Ví dụ: từ chuỗi "<src.common.qml.models.video_model.VideoModel(0x248fea077e0, id=\"videoItem\", parent=0x248fe753970, geometry=0,0 602x524) at 0x00000248FF382180>"
        sẽ trích xuất được địa chỉ "0x248fea077e0".

        Args:
            widget_address: Chuỗi đại diện của widget

        Returns:
            Địa chỉ bộ nhớ của widget hoặc chuỗi gốc nếu không tìm thấy địa chỉ
        """
        if not widget_address:
            return widget_address

        try:
            # Tìm kiếm địa chỉ bộ nhớ trong chuỗi
            # Mẫu chung cho bất kỳ widget PySide6:
            # Tìm địa chỉ đầu tiên sau dấu mở ngoặc
            # Ví dụ: <...VideoModel(0x248fea077e0, id="videoItem", ...) at ...>
            match = re.search(r'\((0x[0-9a-fA-F]+)', str(widget_address))
            if match:
                return match.group(1)  # Lấy nhóm 1 (0x248fea077e0)

            # Nếu không tìm thấy theo cách trên, thử tìm bất kỳ địa chỉ 0x... nào
            match = re.search(r'0x[0-9a-fA-F]+', str(widget_address))
            if match:
                return match.group(0)

            # Nếu không tìm thấy, trả về chuỗi gốc
            return str(widget_address)

        except Exception as e:
            logger.error(f"Lỗi khi trích xuất địa chỉ widget: {e}")
            return str(widget_address)

    @staticmethod
    def convert_rgba_string_to_qcolor(rgba_string: str) -> QColor:
        """
        Convert RGBA string to QColor object.

        Args:
            rgba_string: String in format "rgba(r,g,b,a)" where r,g,b are 0-255 and a is 0.0-1.0

        Returns:
            QColor object or default QColor if parsing fails

        Examples:
            "rgba(0,0,0,0.1)" -> QColor.fromRgbF(0.0, 0.0, 0.0, 0.1)
            "rgba(255,128,64,0.5)" -> QColor.fromRgbF(1.0, 0.5, 0.25, 0.5)
        """
        if not rgba_string:
            logger.warning("Empty RGBA string provided")
            return QColor()

        try:
            # Parse rgba(r,g,b,a) format
            rgba_match = re.match(r'rgba\((\d+),(\d+),(\d+),([\d.]+)\)', rgba_string.strip())
            if rgba_match:
                r = int(rgba_match.group(1))
                g = int(rgba_match.group(2))
                b = int(rgba_match.group(3))
                a = float(rgba_match.group(4))

                # Convert RGB from 0-255 to 0.0-1.0, keep alpha as is (0.0-1.0)
                return QColor.fromRgbF(r/255.0, g/255.0, b/255.0, a)
            else:
                logger.warning(f"Invalid RGBA format: {rgba_string}")
                return QColor()

        except (ValueError, AttributeError) as e:
            logger.error(f"Error parsing RGBA string '{rgba_string}': {e}")
            return QColor()

