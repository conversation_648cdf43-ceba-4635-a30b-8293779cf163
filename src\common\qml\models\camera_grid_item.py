from queue import Queue
import time
import uuid
from PySide6.QtCore import QObject, Property, Signal, Slot, QTimer
from PySide6.QtWidgets import QMenu
from PySide6.QtGui import QP<PERSON>ter, QPixmap,QCursor
from src.common.qml.models.grid_item import GridItem
from src.styles.style import Style, Theme
from typing import List
from enum import IntEnum
from src.common.camera.video_player_manager import video_player_manager
from src.common.camera.player import Player
from src.common.model.camera_model import camera_model_manager,CameraModel
from src.common.model.record_model import record_model_manager
from src.common.controller.main_controller import main_controller
from src.common.qml.models.timelinecontroller import Time<PERSON>ine<PERSON>ontroller,SpeedStatus
from src.common.qml.models.common_enum import CommonEnum
from src.presentation.camera_screen.export_video_dialog import ExportVideoDialog
from src.common.qml.models.export_video_data import ExportVideoData
from src.common.threads.sub_thread import SubThread
from src.common.threads.thread_pool_manager import threadPoolManager
from PySide6.QtQml import QJSValue
from typing import Callable, List
import threading
import logging
logger = logging.getLogger(__name__)

class StreamSwitchingConstants:
    # ✅ Percentage threshold for stream switching: > 30% of grid dimension → MAIN_STREAM
    MAIN_STREAM_THRESHOLD_PERCENTAGE = 30.0

    MAIN_STREAM_INDEX = 0
    SUB_STREAM_INDEX = 1
    SUB_STREAM_2_INDEX = 2

    AUTO_MODE = "AUTO"
    MAIN_FIXED_MODE = "MAIN_FIXED"
    SUB1_FIXED_MODE = "SUB1_FIXED"
    SUB2_FIXED_MODE = "SUB2_FIXED"

    FALLBACK_PRIORITY = {
        0: [1, 2],
        1: [2, 0],
        2: [1, 0],
    }
class CameraAnimationType(IntEnum):
    NONE = 0
    CURRENT = 1
    NEXT = 2

class CameraGridItem(GridItem):

    isPlayingChanged = Signal()
    cameraModelChanged = Signal()
    connectionStateChanged = Signal()
    stateCamIconServerChanged = Signal()
    isNewlyAddedChanged = Signal()  # ✅ NEW: Signal for animation support
    isCurrentStreamTypeChanged = Signal()
    accessTokenRefreshed = Signal()  # ✅ NEW: Signal for QML when token is refreshed

    ptzSupportChanged = Signal()
    isPtzActiveChanged = Signal()
    isPtz3dActiveChanged = Signal()
    isDragZoomActiveChanged = Signal()
    ptzActivated = Signal(str)

    streamModeChanged = Signal()
    streamUrlsChanged = Signal()
    resizeCompleted = Signal(int, int)
    percentChanged = Signal()
    isVideoPlaybackModeChanged = Signal()  # ✅ NEW: Signal for video playback mode
    process_player_signal = Signal(QObject)
    needsPermissionChanged = Signal()

    def __init__(self,row:int = 0,col:int = 0,rows_cell:int = 1,cols_cell:int = 1):
        super().__init__(row = row,col= col,rows_cell = rows_cell,cols_cell = cols_cell)
        self.uuid = str(uuid.uuid4())  # Unique identifier for this logical item
        self.process_player_signal.connect(self.process_player_slot)
        self.resizeCompleted.connect(self.updateResizeAfterLayout)
        self._itemType = "camera"
        self._state_cam_icon_server: str = self.getStateCamIconServer()
        self._cameraModel: 'CameraModel' = None
        self._isPlaying: bool = False
        self._connectionState: str = "connecting"
        self._isNewlyAdded: bool = False

        self._isPtzActive: bool = False
        self._isPtz3dActive: bool = False
        self._isDragZoomActive: bool = False
        self._activePtzMode: str = "none"
        self._isCurrentStreamType: int = CommonEnum.StreamType.MAIN_STREAM
        self._streamMode: str = StreamSwitchingConstants.AUTO_MODE
        self._streamUrls: List[str] = ["", "", ""]
        self._isVideoPlaybackMode: bool = False  # Flag to track video playback mode
        self._percent = 0
        self.player = None
        #VideoPlayback
        self.cameraScreen = main_controller.list_parent["CameraScreen"]
        self.timelinecontroller = None
        self.record_data = None
        self.record_url = None
        self.start_duration = 0
        self.end_duration = 0
        self.seek_time = 0
        threadPoolManager.create_pool(self.uuid,max_threads=1)
        self.input_queue = Queue()
        self.threads = self.start_threads(1,self.process_data)
        ##################
        self.selectedDuration:dict = {"start": None, "end":None}
        self.callback_post_process = None
        self.frameModels = []
    @Property(int, notify=isCurrentStreamTypeChanged)
    def isCurrentStreamType(self):
        """Current stream type property"""
        return self._isCurrentStreamType

    def setVideoPlaybackMode(self, enabled: bool):
        """Set video playback mode - no longer triggers automatic stream switching"""
        if self._isVideoPlaybackMode != enabled:
            self._isVideoPlaybackMode = enabled
            self.isVideoPlaybackModeChanged.emit()

    @Property(bool, notify=isVideoPlaybackModeChanged)
    def isVideoPlaybackMode(self) -> bool:
        """Check if currently in video playback mode"""
        return self._isVideoPlaybackMode

    @Property(str, notify=streamModeChanged)
    def streamMode(self):
        """Stream mode property (AUTO/MAIN_FIXED/SUB1_FIXED/SUB2_FIXED)"""
        return self._streamMode

    @streamMode.setter
    def streamMode(self, value: str):
        """Set stream mode - no longer triggers automatic stream switching"""
        if self._streamMode != value:
            self._streamMode = value
            self.streamModeChanged.emit()
            logger.debug(f"🚀 [STREAM_MODE] Stream mode changed to: {value}")

            # Don't switch stream when in video playback mode
            if not self._isVideoPlaybackMode:
                self.switchStreamTypeChanged(self.getStreamTypeForCurrentMode(), None)
            else:
                logger.debug(f"🚀 [STREAM_MODE] Skipped stream switching - in video playback mode")

    @Property('QStringList', notify=streamUrlsChanged)
    def streamUrls(self):
        """Stream URLs property [main_url, sub_url, sub2_url]"""
        return self._streamUrls

    @streamUrls.setter
    def streamUrls(self, value: List[str]):
        """Set stream URLs and trigger re-evaluation"""
        if self._streamUrls != value:
            self._streamUrls = value
            self.streamUrlsChanged.emit()

    @Slot(int, int, int, int, int, int, int, int)
    def handleResizeComplete(self, old_rows: int, old_cols: int, new_rows: int, new_cols: int,
                            final_width: int, final_height: int, total_grid_rows: int, total_grid_cols: int):
        """
        ✅ PHASE 3: Handle resize completion with stream switch + update_resize
        Called when resize release occurs with row/col changes

        Args:
            old_rows: Previous row span
            old_cols: Previous column span
            new_rows: New row span
            new_cols: New column span
            final_width: Final pixel width
            final_height: Final pixel height
            total_grid_rows: Total grid rows
            total_grid_cols: Total grid columns
        """
        logger.debug(f"🚀 [RESIZE] Resize complete: {old_cols}x{old_rows} → {new_cols}x{new_rows}, size: {final_width}x{final_height}, grid: {total_grid_cols}x{total_grid_rows}")

        # 1. Switch stream if grid dimensions changed
        if old_rows != new_rows or old_cols != new_cols:
            new_stream_type = self.calculateStreamTypeForGridSize(new_rows, new_cols, total_grid_rows, total_grid_cols)
            if new_stream_type != self._isCurrentStreamType:
                logger.debug(f"🚀 [RESIZE] Stream switch: {self._isCurrentStreamType} → {new_stream_type}")
                # Simple signal emission - FrameModel will handle appropriately
                self.switchStreamTypeChanged(new_stream_type, None)

        # 2. Always emit resize signal for player update (handled by FrameModel)
        self.resizeCompleted.emit(final_width, final_height)

    @Slot(int, int)
    def handleGridExpansion(self, new_grid_rows: int, new_grid_cols: int):
        """
        ✅ PHASE 4: Handle grid expansion - switch stream if grid > 2x2
        Called when grid expansion occurs (Ctrl+Wheel)

        Args:
            new_grid_rows: New total grid rows
            new_grid_cols: New total grid columns
        """
        logger.debug(f"🚀 [GRID_EXPANSION] Grid expanded to {new_grid_cols}x{new_grid_rows}")

        # 1. Check if grid becomes > 2x2 (affects stream type)
        if new_grid_rows > 2 or new_grid_cols > 2:
            # Calculate new stream type for current item position
            current_rows = getattr(self, '_rows_cell', 1)
            current_cols = getattr(self, '_cols_cell', 1)

            new_stream_type = self.calculateStreamTypeForGridSize(current_rows, current_cols, new_grid_rows, new_grid_cols)
            if new_stream_type != self._isCurrentStreamType:
                logger.debug(f"🚀 [GRID_EXPANSION] Stream switch: {self._isCurrentStreamType} → {new_stream_type}")

                # Don't switch stream when in video playback mode
                if not self._isVideoPlaybackMode:
                    # Simple signal emission - FrameModel will handle appropriately
                    self.switchStreamTypeChanged(new_stream_type, None)
                else:
                    logger.debug(f"🚀 [GRID_EXPANSION] Skipped stream switching - in video playback mode")

        # 2. Always emit resize signal for player update after grid layout
        self.resizeCompleted.emit(self._width, self._height)



    def getStreamTypeForCurrentMode(self) -> int:
        """
        ✅ PHASE 5: Get stream type for current mode (replaces optimalStreamIndex)
        Used by FrameModel when direct grid info is not available
        """
        # Video playback mode takes priority
        if self._isVideoPlaybackMode:
            return CommonEnum.StreamType.VIDEO_STREAM

        # Fixed mode logic
        if self._streamMode != "AUTO":
            mode_map = {
                "MAIN_FIXED": CommonEnum.StreamType.MAIN_STREAM,
                "SUB1_FIXED": CommonEnum.StreamType.SUB_STREAM,
                "SUB2_FIXED": CommonEnum.StreamType.SUB_STREAM2
            }
            return mode_map.get(self._streamMode, CommonEnum.StreamType.MAIN_STREAM)

        # Auto mode - simplified fallback logic
        # Use current cell dimensions for basic decision
        current_rows = getattr(self, '_rows_cell', 1)
        current_cols = getattr(self, '_cols_cell', 1)

        # Basic logic: items spanning 2+ cells → MAIN_STREAM
        if current_rows >= 2 or current_cols >= 2:
            return CommonEnum.StreamType.MAIN_STREAM
        else:
            return CommonEnum.StreamType.SUB_STREAM

    def calculateStreamTypeForGridSize(self, rows: int, cols: int, total_rows: int, total_cols: int) -> int:
        """
        ✅ Calculate optimal stream type based on percentage of grid occupied

        Args:
            rows: Number of rows the item spans
            cols: Number of columns the item spans
            total_rows: Total grid rows
            total_cols: Total grid columns

        Returns:
            Stream type (CommonEnum.StreamType)
        """
        # Video playback mode takes priority
        if self._isVideoPlaybackMode:
            return CommonEnum.StreamType.VIDEO_STREAM

        # Fixed mode logic
        if self._streamMode != "AUTO":
            mode_map = {
                "MAIN_FIXED": CommonEnum.StreamType.MAIN_STREAM,
                "SUB1_FIXED": CommonEnum.StreamType.SUB_STREAM,
                "SUB2_FIXED": CommonEnum.StreamType.SUB_STREAM2
            }
            return mode_map.get(self._streamMode, CommonEnum.StreamType.MAIN_STREAM)

        # Auto mode - percentage-based logic
        if total_rows <= 0 or total_cols <= 0:
            # Fallback to cell-based logic if invalid grid info
            logger.warning(f"🚨 [FALLBACK] Invalid grid info: {total_cols}x{total_rows}, using cell-based logic")
            return CommonEnum.StreamType.MAIN_STREAM if (rows >= 2 or cols >= 2) else CommonEnum.StreamType.SUB_STREAM

        # Calculate percentage of grid occupied
        row_percentage = (rows / total_rows) * 100
        col_percentage = (cols / total_cols) * 100

        # Use the larger percentage for decision
        max_percentage = max(row_percentage, col_percentage)

        # Use threshold from constants
        logger.debug(f"🚀 [PERCENTAGE] Item {cols}x{rows} in grid {total_cols}x{total_rows}: row={row_percentage:.1f}%, col={col_percentage:.1f}%, max={max_percentage:.1f}%")

        if max_percentage > StreamSwitchingConstants.MAIN_STREAM_THRESHOLD_PERCENTAGE:
            return CommonEnum.StreamType.MAIN_STREAM
        else:
            return CommonEnum.StreamType.SUB_STREAM

    @Slot(str)
    def setStreamModeFromQML(self, mode: str):
        """Set stream mode from QML interface"""
        valid_modes = [StreamSwitchingConstants.AUTO_MODE,
                      StreamSwitchingConstants.MAIN_FIXED_MODE,
                      StreamSwitchingConstants.SUB1_FIXED_MODE,
                      StreamSwitchingConstants.SUB2_FIXED_MODE]
        if mode in valid_modes:
            self.streamMode = mode


    @isCurrentStreamType.setter
    def isCurrentStreamType(self, value: int):
        if self._isCurrentStreamType != value:
            self._isCurrentStreamType = value
            self.isCurrentStreamTypeChanged.emit()

    @Property(str, notify=stateCamIconServerChanged)
    def stateCamIconServer(self):
        """State camera icon server property"""
        return self._state_cam_icon_server

    @stateCamIconServer.setter
    def stateCamIconServer(self, value: str ):
        if self._state_cam_icon_server != value:
            self._state_cam_icon_server = value
            self.stateCamIconServerChanged.emit()

    @Property(bool, notify=isPlayingChanged)
    def isPlaying(self):
        """Video playing state property"""
        return self._isPlaying

    @isPlaying.setter
    def isPlaying(self, value: bool):
        self._isPlaying = value
        self.isPlayingChanged.emit()

    @Property('QVariant', notify=cameraModelChanged)
    def cameraModel(self):
        """Camera model reference property"""
        return self._cameraModel

    @cameraModel.setter
    def cameraModel(self, value: 'CameraModel'):
        if self._cameraModel != value:
            old_camera_id = self._cameraModel.get_property("id") if self._cameraModel else "None"
            new_camera_id = value.get_property("id") if value else "None"
            is_phone = value.isPhoneCamera() if value else False
            state = value.state if value else "Unknown"
            permission = value.permissionGranted if value else False

            logger.info(f'🎯 [GRID_ITEM] cameraModel setter: {old_camera_id} → {new_camera_id} (isPhone: {is_phone}, state: {state}, permission: {permission})')

            if self._cameraModel is not None:
                logger.debug(f'🎯 [GRID_ITEM] Unregistering old camera {old_camera_id}')
                self.unregister_player()

            self._cameraModel = value
            if self._cameraModel is not None:
                logger.info(f'🎯 [GRID_ITEM] Setting up new camera {new_camera_id}')
                self.setup_camera_connections()
                self.register_player()

            logger.debug(f'🎯 [GRID_ITEM] Emitting signals for camera {new_camera_id}')
            self.cameraModelChanged.emit()
            self.needsPermissionChanged.emit()

    @Property(bool, notify=isNewlyAddedChanged)
    def isNewlyAdded(self):
        """Flag indicating if this item was just added (for animation)"""
        return self._isNewlyAdded

    @isNewlyAdded.setter
    def isNewlyAdded(self, value: bool):
        if self._isNewlyAdded != value:
            self._isNewlyAdded = value
            self.isNewlyAddedChanged.emit()


    def getStateCamIconServer(self, cameraModel: 'CameraModel' = None):
        if cameraModel is not None:
            from src.common.qml.models.common_enum import CommonEnum
            if cameraModel.state_merged == CommonEnum.CameraState.CONNECTED_REC_PIN:
                return "qrc:/src/assets/treeview_and_menu_treeview/rec_pin.svg"
            elif cameraModel.state_merged == CommonEnum.CameraState.CONNECTED_REC_UNPIN:
                return "qrc:/src/assets/treeview_and_menu_treeview/rec_unpin.svg"
            elif cameraModel.state_merged == CommonEnum.CameraState.CONNECTED_NOREC_PIN:
                return "qrc:/src/assets/treeview_and_menu_treeview/norec_pin.svg"
            elif cameraModel.state_merged == CommonEnum.CameraState.CONNECTED_NOREC_UNPIN:
                return "qrc:/src/assets/treeview_and_menu_treeview/norec_unpin.svg"
            elif cameraModel.state_merged == CommonEnum.CameraState.DISCONNECTED_REC_PIN:
                return "qrc:/src/assets/treeview_and_menu_treeview/rec_pin.svg"
            elif cameraModel.state_merged == CommonEnum.CameraState.DISCONNECTED_REC_UNPIN:
                return "qrc:/src/assets/treeview_and_menu_treeview/rec_unpin.svg"
            elif cameraModel.state_merged == CommonEnum.CameraState.DISCONNECTED_NOREC_PIN:
                return "qrc:/src/assets/treeview_and_menu_treeview/norec_pin.svg"
            else:  # DISCONNECTED_NOREC_UNPIN
                return "qrc:/src/assets/treeview_and_menu_treeview/norec_unpin.svg"
        return "qrc:/src/assets/treeview_and_menu_treeview/norec_unpin.svg"  # Default return value if no camera model

    @Property(str, notify=connectionStateChanged)
    def connectionState(self):
        """Connection state for overlay display - MVVM property"""
        return self._connectionState

    @connectionState.setter
    def connectionState(self, value: str):
        if self._connectionState != value:
            self._connectionState = value
            self.connectionStateChanged.emit()
            # logger.debug(f"Connection state changed to: {value}")

    @Property(str, notify=percentChanged)
    def percent(self):
        return self._percent

    @percent.setter
    def percent(self, value: str):
        if self._percent != value:
            self._percent = value
            self.percentChanged.emit()

    @Property(bool, constant=True)
    def onvifSupported(self):
        return self._cameraModel.onvifSupported
    
    @Property(bool, constant=True)
    def ptzSupported(self):
        return self._cameraModel.ptzSupported
    
    @Property(bool, constant=True)
    def ptz3DSupport(self):
        return self._cameraModel.ptz3DSupport
    
    # ✅ PTZ STATE PROPERTIES: Add PTZ button state management
    @Property(bool, notify=isPtzActiveChanged)
    def isPtzActive(self):
        """PTZ panel active state property"""
        return self._isPtzActive

    @isPtzActive.setter
    def isPtzActive(self, value: bool):
        if self._isPtzActive != value:
            self._isPtzActive = value
            self.isPtzActiveChanged.emit()
            logger.debug(f"🎮 [PTZ_STATE] PTZ panel active changed to: {value}")

    @Property(bool, notify=isPtz3dActiveChanged)
    def isPtz3dActive(self):
        """PTZ 3D control active state property"""
        return self._isPtz3dActive

    @isPtz3dActive.setter
    def isPtz3dActive(self, value: bool):
        if self._isPtz3dActive != value:
            self._isPtz3dActive = value
            self.isPtz3dActiveChanged.emit()
            logger.debug(f"🎮 [PTZ_STATE] PTZ 3D active changed to: {value}")

    @Property(bool, notify=isDragZoomActiveChanged)
    def isDragZoomActive(self):
        """Drag zoom control active state property"""
        return self._isDragZoomActive

    @isDragZoomActive.setter
    def isDragZoomActive(self, value: bool):
        if self._isDragZoomActive != value:
            self._isDragZoomActive = value
            self.isDragZoomActiveChanged.emit()
            logger.debug(f"🎮 [PTZ_STATE] Drag zoom active changed to: {value}")

    @Property(str, notify=ptzActivated)
    def activePtzMode(self):
        """Current active PTZ mode property"""
        return self._activePtzMode

    @activePtzMode.setter
    def activePtzMode(self, value: str):
        if self._activePtzMode != value:
            self._activePtzMode = value
            self.ptzActivated.emit(value)
            logger.debug(f"🎮 [PTZ_STATE] Active PTZ mode changed to: {value}")

    @Slot(result=str)
    def getCameraId(self) -> str:
        """Get camera ID"""
        return self._cameraModel.id if self._cameraModel else ""

    @Slot(result=str)
    def getCameraName(self) -> str:
        """Get camera name"""
        return self._cameraModel.name if self._cameraModel else ""

    @Slot(str)
    def activatePtzButton(self, buttonType: str):
        """Activate PTZ button with specific type and manage state"""
        if buttonType == "ptz":
            self.isPtzActive = True
            self.isPtz3dActive = False
            self.isDragZoomActive = False
            self.activePtzMode = "ptz"

        elif buttonType == "ptz3d":
            self.isPtzActive = False
            self.isPtz3dActive = True
            self.isDragZoomActive = False
            self.activePtzMode = "ptz3d"

        elif buttonType == "dragZoom":
            self.isPtzActive = False
            self.isPtz3dActive = False
            self.isDragZoomActive = True
            self.activePtzMode = "dragZoom"

        else:
            self.isPtzActive = False
            self.isPtz3dActive = False
            self.isDragZoomActive = False
            self.activePtzMode = "none"

    @Slot()
    def deactivateAllPtz(self):
        """Deactivate all PTZ controls"""
        self.activatePtzButton("none")

    @Slot(result=bool)
    def hasActivePtz(self) -> bool:
        """Check if any PTZ control is currently active"""
        return self.isPtzActive or self.isPtz3dActive or self.isDragZoomActive

    @Slot(result=str)
    def getActivePtzMode(self) -> str:
        """Get the currently active PTZ mode"""
        return self.activePtzMode

    @Property(bool, notify=needsPermissionChanged)
    def needsPermission(self):
        if self._cameraModel and self._cameraModel.isPhoneCamera():
            state = self._cameraModel.state
            permission = self._cameraModel.get_property("permissionGranted", False)
            return (state == "CONNECTED" and not permission)
        return False

    @Slot()
    def grantPermission(self):
        if self._cameraModel and self._cameraModel.isPhoneCamera():
            camera_id = self._cameraModel.get_property("id")

            # Close any active phone permission notification for this camera
            from src.common.widget.notifications.listen_message_notifications import listen_show_notification
            listen_show_notification.close_phone_permission_notification(camera_id)

            # Set permission granted
            self._cameraModel.permissionGranted = True

            # Notify video_player_manager about state change
            from src.common.camera.video_player_manager import video_player_manager
            video_player_manager.notify_camera_state_change(self._cameraModel, "CONNECTED")

    @Slot()
    def updatePermissionState(self):
        """Force update permission state - useful for debugging"""
        self.needsPermissionChanged.emit()

    def isConnected(self) -> bool:
        """Check if camera is connected"""
        if self._cameraModel:
            camera_state = getattr(self._cameraModel, 'state_merged', 7)
            return camera_state in [0, 1, 2, 3]
        return False

    def getStreamUrl(self) -> str:
        """Get camera stream URL"""
        if self._cameraModel:
            return getattr(self._cameraModel, 'stream_url', "")
        return ""

    def isValid(self) -> bool:
        """Validate camera grid item data"""
        base_valid = super().isValid()
        camera_valid = self._cameraModel is not None
        return base_valid and camera_valid

    @Slot(int, int)
    def updateResizeAfterLayout(self, width: int, height: int):
        """
        ✅ PHASE 2: Update player resize after fullscreen animation or layout change completes
        Called after fullscreen animation finishes or layout changes are complete
        """
        if self.player and hasattr(self.player, 'update_resize'):
            logger.debug(f"🚀 [LAYOUT] Updating player resize to {width}x{height}")
            self.player.update_resize(width=width, height=height, uuid=self.uuid)

    def setup_camera_connections(self):
        """Setup signal connections for camera model"""
        if self.cameraModel:
            camera_id = self.cameraModel.get_property("id")
            is_phone = self.cameraModel.isPhoneCamera()
            state = self.cameraModel.state
            permission = self.cameraModel.get_property("permissionGranted", False)

            logger.info(f'🔗 [SETUP] Setting up camera connections for {camera_id} (isPhone: {is_phone}, state: {state}, permission: {permission})')

            # Connect to camera model signals for permission changes
            if hasattr(self.cameraModel, 'stateChanged'):
                self.cameraModel.stateChanged.connect(self.on_camera_state_changed)
                logger.info(f'🔗 [SETUP] Connected stateChanged signal for {camera_id}')
            if hasattr(self.cameraModel, 'permissionGrantedChanged'):
                self.cameraModel.permissionGrantedChanged.connect(self.on_permission_granted_changed)
                logger.info(f'🔗 [SETUP] Connected permissionGrantedChanged signal for {camera_id}')
            if hasattr(self.cameraModel, 'accessTokenChanged'):
                self.cameraModel.accessTokenChanged.connect(self.on_access_token_changed)
                logger.info(f'🔗 [SETUP] Connected accessTokenChanged signal for {camera_id}')

            # Initialize state tracking
            self._last_camera_state = state
            self._last_permission_state = permission
            logger.info(f'🔗 [SETUP] Initial state tracking: state={self._last_camera_state}, permission={self._last_permission_state}')

    def on_camera_state_changed(self):
        """Handle camera state changes"""
        if self.cameraModel:
            self.needsPermissionChanged.emit()

    def on_permission_granted_changed(self):
        """Handle permission granted changes"""
        if self.cameraModel:
            self.needsPermissionChanged.emit()

    def on_access_token_changed(self):
        """Handle access token changes - refresh camera stream"""
        if self.cameraModel:
            camera_id = self.cameraModel.get_property("id")
            logger.info(f'🔑 [TOKEN_REFRESH] Access token changed for camera {camera_id}')

            # If player exists and is connected, refresh the stream URL
            if hasattr(self, 'player') and self.player and hasattr(self.player, 'camera_model'):
                logger.info(f'🔑 [TOKEN_REFRESH] Refreshing stream for camera {camera_id}')

                # Get current stream type
                current_stream_type = getattr(self.player, 'stream_type', 0)

                # Refresh stream URL with new token
                if hasattr(self, 'controller') and self.controller:
                    def update_stream_url(response):
                        if response and response.status_code == 200:
                            data = response.json().get("data")
                            if data and len(data) > current_stream_type:
                                new_stream_url = data[current_stream_type]
                                logger.info(f'🔑 [TOKEN_REFRESH] Updated stream URL for camera {camera_id}')
                                self.player.on_stream_link_changed(new_stream_url)
                            else:
                                logger.error(f'🔑 [TOKEN_REFRESH] No stream data for camera {camera_id}')
                        else:
                            logger.error(f'🔑 [TOKEN_REFRESH] Failed to get stream URL for camera {camera_id}')

                    # Request new stream URL with refreshed token
                    self.controller.get_stream_url_thread(
                        cameraId=camera_id,
                        streamIndex=current_stream_type,
                        callback=update_stream_url
                    )
                else:
                    logger.warning(f'🔑 [TOKEN_REFRESH] No controller available for camera {camera_id}')
            else:
                logger.debug(f'🔑 [TOKEN_REFRESH] No active player for camera {camera_id}')

            # Emit signal to notify QML that token has been refreshed
            self.accessTokenRefreshed.emit()
            logger.info(f'🔑 [TOKEN_REFRESH] Emitted accessTokenRefreshed signal for camera {camera_id}')

    def on_server_state_changed(self, new_state):
        """Handle server state changes from video_player_manager"""
        # Trigger needsPermissionChanged for PHONE cameras
        if self.cameraModel and self.cameraModel.isPhoneCamera():
            self.needsPermissionChanged.emit()

        # Update connection state
        self.camera_state_signal(new_state)

    @Slot("QVariant")
    def register_player(self):
        if self.cameraModel:
            camera_id = self.cameraModel.get_property("id")
            is_phone = self.cameraModel.isPhoneCamera()
            state = self.cameraModel.state
            permission = self.cameraModel.permissionGranted

            logger.info(f'🎬 [REGISTER] register_player called for camera {camera_id} (isPhone: {is_phone}, state: {state}, permission: {permission})')

            self.stateCamIconServer = self.getStateCamIconServer(self.cameraModel)
            from src.common.controller.controller_manager import controller_manager
            self.controller = controller_manager.get_controller(server_ip=self.cameraModel.get_property("server_ip"))

            # Check if PHONE camera needs permission before registering player
            if (is_phone and state == "CONNECTED" and not permission):
                logger.info(f'📱 [PHONE_PERMISSION] PHONE camera {camera_id} needs permission, skipping player registration')
                # Don't register player yet, wait for permission
                return

            # Use MAIN_STREAM as default for better quality
            stream_type = self.getStreamTypeForCurrentMode()
            logger.info(f'🎬 [REGISTER] Registering player for camera {camera_id} with stream_type = {stream_type}')
            video_player_manager.register_player(self, self.cameraModel, stream_type)
            self.record_data = record_model_manager.register_record_data(self)
            logger.info(f'🎬 [REGISTER] Player registration completed for camera {camera_id}')


    def disconnect_camera_signals(self):
        """Disconnect signal connections for camera model"""
        if self._cameraModel:
            camera_id = self._cameraModel.get_property("id")
            logger.debug(f'🔗 [PERMISSION] Disconnecting camera signals for {camera_id}')
            try:
                if hasattr(self._cameraModel, 'stateChanged'):
                    self._cameraModel.stateChanged.disconnect(self.on_camera_state_changed)
                    logger.debug(f'🔗 [PERMISSION] Disconnected stateChanged signal for {camera_id}')
                if hasattr(self._cameraModel, 'permissionGrantedChanged'):
                    self._cameraModel.permissionGrantedChanged.disconnect(self.on_permission_granted_changed)
                    logger.debug(f'🔗 [PERMISSION] Disconnected permissionGrantedChanged signal for {camera_id}')
                if hasattr(self._cameraModel, 'accessTokenChanged'):
                    self._cameraModel.accessTokenChanged.disconnect(self.on_access_token_changed)
                    logger.debug(f'🔗 [PERMISSION] Disconnected accessTokenChanged signal for {camera_id}')
            except Exception as e:
                logger.debug(f"🔗 [PERMISSION] disconnect_camera_signals error for {camera_id}: {e}")

    @Slot()
    def unregister_player(self):
        try:
            logger.debug(f"[FrameModel] unregister_player called")
            self.disconnect_camera_signals()
            video_player_manager.unregister_player(self)
            record_model_manager.unregister_record_data(self)
        except Exception as e:
            logger.error(f"[FrameModel] Error unregistering video capture: {str(e)}")
            logger.error(f"[FrameModel] Exception type: {type(e).__name__}")
            import traceback
            logger.error(f"[FrameModel] Traceback: {traceback.format_exc()}")

    def process_player_slot(self, player):
        logger.debug(f'process_player_slot: {player} - type: {type(player)}')
        def callback(data):
            pass
        try:
            subThread = SubThread(parent=self,target=self.process_player,args=(player,),callback=callback)
            subThread.start()
        except Exception as e:
            import traceback
            logger.error(f'process_player_slot: Traceback: {traceback.format_exc()}')

    def process_player(self,player:Player):
        try:
            # print(f'process_player camera_id = {self.cameraModel.id} - player = {player}')
            self.player = player
            from src.common.camera.live_stream_player import LiveStreamPlayer
            if isinstance(self.player, LiveStreamPlayer):
                self.player.is_send_mat_frame = self.callback_post_process is not None
            self.player.connect_status = True
            self.player.register_signal(self)
            self.isCurrentStreamType = self.player.stream_type
            if len(self.frameModels) > 0:
                for frameModel in self.frameModels:
                    width = int(frameModel.width())
                    height = int(frameModel.height())
                    if width <= 0 or height <= 0:
                        # Use default size if component size is invalid
                        width = 1920
                        height = 1080
                    self.player.update_resize(width=width, height=height, uuid=self.uuid)
            else:
                self.player.update_resize(width=192, height=108, uuid=self.uuid)

            if not self.player.isRunning():
                if self.player.stream_type != CommonEnum.StreamType.VIDEO_STREAM:
                    self.player.start_thread()

                # phần xử lý get stream url chỉ gọi khi stream chưa đc chạy và gọi 1 lần mỗi khi khởi tạo thôi. 
                # cơ chế cập nhật đã có ở websocket message_processor.py để update stream url khi có thay đổi rồi
                def update_stream_url(reponse, streamIndex):
                    if self.player is not None:
                        # logger.debug(f'update_stream_url: {reponse} - type: {type(reponse)}')
                        # Giải thích logic:
                        # - Nếu streamIndex trùng với index của item và state là CONNECTED thì lấy url của item đó
                        # - Nếu không thì lấy url của item có state là CONNECTED
                        data = reponse.json()
                        logger.debug(f'update_stream_url: \n{data} \ntype: {type(data)}')
                        self.streamUrls = [item["url"] for item in data]
                        # Lọc chỉ các luồng có trạng thái CONNECTED
                        connected_streams = [item for item in data if item["state"] == "CONNECTED" or item["state"] is None]
                        target_stream = None

                        if connected_streams:
                            # Đầu tiên tìm luồng phù hợp với chỉ số được yêu cầu
                            target_stream = next((stream for stream in connected_streams if stream["index"] == streamIndex), None)
                            # Nếu không tìm thấy luồng phù hợp với chỉ số, sử dụng luồng kết nối đầu tiên có sẵn
                            if target_stream is None and len(connected_streams) > 0:
                                target_stream = connected_streams[0]
                            self.player.on_stream_link_changed(target_stream["url"])
                        else:
                            # set None -> DISCONNECTED
                            self.player.on_stream_link_changed(None)
                logger.debug(f'process_player: C')
                # Check permission for PHONE cameras
                if player.camera_model and player.camera_model.isPhoneCamera():
                    permission_granted = player.camera_model.get_property("permissionGranted", False)
                    if not permission_granted:
                        logger.info(f'process_player: PHONE camera {self.player.camera_model.id} needs permission, skipping player registration')
                        return False
                # Use appropriate stream index based on stream type
                if self.player.stream_type == CommonEnum.StreamType.MAIN_STREAM or self.player.stream_type == CommonEnum.StreamType.SUB_STREAM:
                    target_stream_type = self.player.stream_type
                    self.controller.get_stream_url_thread(cameraId=self.player.camera_model.id,streamIndex=target_stream_type,callback=update_stream_url)
                elif self.player.stream_type == CommonEnum.StreamType.VIDEO_STREAM:
                    self.player.load_media(self.record_url, seek_time=self.seek_time,start_duration=self.start_duration,end_duration=self.end_duration)
                    self.timelinecontroller.register_player(player=self.player)
                    self.player.play_video()

            return True  # Return success indicator

        except Exception as e:
            import traceback
            logger.error(f"[VideoModel] Traceback: {traceback.format_exc()}")
            return False  # Return failure indicator
        
    @Slot()
    def clickedChanged(self):
        if self.timelinecontroller is None:
            startTime = time.time()
            self.create_timelinecontroller()
            self.timelinecontroller.openCalendarDialog.connect(self.cameraScreen.openCalendarDialog)
            self.cameraScreen.timeLineManager.timeLineController = self.timelinecontroller
            def callback(data):
                response,dateFrom,dateTo = data
                if response is not None:
                    if response.status_code == 200:
                        data = response.json()
                        record_model_manager.add_records(record_list = data,dateFrom = dateFrom,dateTo = dateTo)
                else:
                    record_model_manager.add_records(record_list = [],dateFrom = dateFrom,dateTo = dateTo)

                camera_id = self.cameraModel.get_property("id")
                self.timelinecontroller.setCameraName(self.cameraModel.get_property("name"))
                if self.record_data is not None:
                    self.record_data.recordDataChanged.connect(self.recordDataChanged)

                if self.record_data is None:
                    self.timelinecontroller.setIsTimeLine(False)
                    return
                camera_data = {
                    record.data.id: record
                    for record in self.record_data.data
                    if record.data.cameraId == camera_id
                }
                if camera_data:
                    self.timelinecontroller.setIsTimeLine(False)
                    self.timelinecontroller.setIsTimeLine(True)
                else:
                    self.timelinecontroller.setIsTimeLine(False)
                if camera_data:
                    self.timelinecontroller.initData(self.record_data.start_duration, self.record_data.end_duration)
                    self.timelinecontroller.updateRecordDuration(self.record_data)
            def process(cameraIds):
                data = self.controller.api_client.get_videos(cameraIds = cameraIds)
                while self.isAnimating:
                    logger.info(f'Camera {self.cameraModel.name} isAnimating {time.time() - startTime}')
                    time.sleep(0.01)
                    if (time.time() - startTime) > 0.5:
                        # isAnimating > 500ms
                        logger.info(f'isAnimating > 500ms')
                        return data
                return data
            if self.controller:
                threadPoolManager.run(self.uuid,process,callback = callback,args=(self.cameraModel.get_property("id"),))
            else:
                pass
        else:
            self.cameraScreen.timeLineManager.timeLineController = self.timelinecontroller
            
    @Slot()
    def unClickedChanged(self):
        self.cameraScreen.default_timelinecontroller()

    @Slot(int, QObject)
    def switchStreamTypeChanged(self,streamType, widget = None):
        def process(widget):
            if widget == self or widget is None:
                if streamType != self.player.stream_type  and self.cameraModel is not None:
                    video_player_manager.unregister_player(self)
                    self.streamType = streamType
                    video_player_manager.register_player(self, self.cameraModel, streamType)

                    # Set video playback mode when switching to VIDEO_STREAM
                    if streamType == CommonEnum.StreamType.VIDEO_STREAM:
                        self.setVideoPlaybackMode(True)
                    else:
                        self.setVideoPlaybackMode(False)

                    if not self.player.isRunning():
                        if self.player.stream_type != CommonEnum.StreamType.VIDEO_STREAM:
                            self.player.start_thread()
        threadPoolManager.run(self.uuid,process,args=(widget,))

    def create_timelinecontroller(self):
        self.timelinecontroller = TimeLineController(parent=self.cameraScreen)
        self.timelinecontroller.positionClicked.connect(self.positionClicked)
        self.timelinecontroller.isPlayChanged.connect(self.isPlayChanged)
        self.timelinecontroller.isLiveChanged.connect(self.isLiveChanged)
        self.timelinecontroller.isNextChunkClicked.connect(self.isNextChunkClicked)
        self.timelinecontroller.isPreviousChunkClicked.connect(self.isPreviousChunkClicked)
        self.timelinecontroller.speedStatusChanged.connect(self.speedStatusChanged)
        self.timelinecontroller.nextFrameChanged.connect(self.nextFrameChanged)
        self.timelinecontroller.hoverPositionChanged.connect(self.hoverPositionChanged)
        self.timelinecontroller.selectedDurationChanged.connect(self.selectedDurationChanged)
        self.timelinecontroller.showMenu.connect(self.show_menu)
        return self.timelinecontroller
    
    def recordDataChanged(self):
        if self.timelinecontroller is not None:
            if not self.timelinecontroller.isTimeLine:
                self.timelinecontroller.setIsTimeLine(True)
                self.timelinecontroller.initData(self.record_data.start_duration, self.record_data.end_duration)
            self.timelinecontroller.updateRecordDuration(self.record_data)

    def positionClicked(self, position):
        """Handle timeline position click with time sync"""
        self.connectionState = "buffering"
        self.percent = str(0) + "%"
        position = int(position)
        self.input_queue.put(position)

    def process_data(self)-> None:
        while True:
            position = self.input_queue.get()
            if position is None:
                break
            self.previous_time = None
            self.current_time = None
            self.target_time = None
            if self.record_data is not None and self.player is not None and self.cameraModel is not None:
                try:
                    data = self.record_data.get_record(position, self.cameraModel.get_property("id"))
                    if data is not None:
                        record, duration_time_need_seek, duration_to_move = data
                        # source = self.player.get_current_url()
                        self.record_url = record.data.url
                        self.seek_time = duration_time_need_seek
                        self.start_duration = record.data.start_duration
                        self.end_duration = record.data.end_duration
                        if duration_to_move != -1:
                            self.timelinecontroller.shift_for_duration(duration_to_move)
                        if not self.input_queue.empty():
                            continue
                        if self.player.stream_type != CommonEnum.StreamType.VIDEO_STREAM:
                            self.switchStreamTypeChanged(CommonEnum.StreamType.VIDEO_STREAM,self)
                            self.timelinecontroller.isNextChunk = True
                            self.timelinecontroller.isNextFrame = True
                        else:
                            source = self.player.get_current_url()
                            if (record.data.url is not None and record.data.url != source):
                                self.timelinecontroller.isNextChunk = True
                                self.timelinecontroller.isNextFrame = True
                                self.player.stop_video()
                                self.player.load_media(record.data.url, seek_time=duration_time_need_seek,start_duration=record.data.start_duration,end_duration=record.data.end_duration)
                                self.player.play_video()
                            else:
                                if self.player.get_length() != 0 and self.player.is_playing():
                                    position_seek = float(duration_time_need_seek)/float(self.player.get_length())
                                    self.player.set_position(position_seek)
                                elif self.player.get_length() != 0 and not self.player.is_playing():
                                    self.player.play_video()
                                    position_seek = float(duration_time_need_seek)/float(self.player.get_length())
                                    self.player.set_position(position_seek)

                        self.timelinecontroller.isLive = False
                        self.timelinecontroller.positionBubble = True
                except ValueError as e:
                    logger.error(f"Error parsing position timestamp: {e}")
                except Exception as e:
                    logger.error(f"Error handling position click: {e}")
            self.input_queue.task_done()

    def isPlayChanged(self):
        if self.timelinecontroller.isPlay:
            if self.player.stream_type == CommonEnum.StreamType.VIDEO_STREAM:
                self.player.start_video()
            else:
                self.player.play_live()
        else:
            if self.player.stream_type == CommonEnum.StreamType.VIDEO_STREAM:
                self.player.pause_video()
            else:
                self.player.pause_live()

    def isLiveChanged(self):
        if self.timelinecontroller.isLive:
            self.timelinecontroller.positionBubble = False
            self.setVideoPlaybackMode(False)

            # Use getStreamTypeForCurrentMode for live stream switching (size is stable here)
            target_stream_type = self.getStreamTypeForCurrentMode()
            self.switchStreamTypeChanged(target_stream_type,self)

    def isNextChunkClicked(self):
        self.next_chunk_signal('')

    def isPreviousChunkClicked(self):
        self.previous_chunk_signal('')

    def speedStatusChanged(self,data):
        logger.debug(f'speedStatusChanged = {data}')
        speed = 0
        if data == 1:
            if self.timelinecontroller.nextFrame == SpeedStatus.SpeedEnum.Pause:
                speed = SpeedStatus.SpeedEnum.Up1X
            elif self.timelinecontroller.nextFrame == SpeedStatus.SpeedEnum.Up1X:
                speed = SpeedStatus.SpeedEnum.Up2X
            elif self.timelinecontroller.nextFrame == SpeedStatus.SpeedEnum.Up2X:
                speed = SpeedStatus.SpeedEnum.Up4X
            elif self.timelinecontroller.nextFrame == SpeedStatus.SpeedEnum.Up4X:
                speed = SpeedStatus.SpeedEnum.Up8X
            elif self.timelinecontroller.nextFrame == SpeedStatus.SpeedEnum.Up8X:
                speed = SpeedStatus.SpeedEnum.Up8X
        elif data == -1:
            if self.timelinecontroller.nextFrame == SpeedStatus.SpeedEnum.Up1X:
                # speed = SpeedStatus.SpeedEnum.Pause
                # self.timelinecontroller.isPlay = False
                return
            elif self.timelinecontroller.nextFrame == SpeedStatus.SpeedEnum.Up2X:
                speed = SpeedStatus.SpeedEnum.Up1X
            elif self.timelinecontroller.nextFrame == SpeedStatus.SpeedEnum.Up4X:
                speed = SpeedStatus.SpeedEnum.Up2X
            elif self.timelinecontroller.nextFrame == SpeedStatus.SpeedEnum.Up8X:
                speed = SpeedStatus.SpeedEnum.Up4X
            else:
                speed = SpeedStatus.SpeedEnum.Pause
                self.timelinecontroller.isPlay = False
                return
        self.timelinecontroller.isPlay = True
        if self.player.stream_type == CommonEnum.StreamType.VIDEO_STREAM and self.player.is_playing():
            try:
                self.player.set_speed(speed)
                self.timelinecontroller.nextFrame = speed
            except ValueError as e:
                logger.error(f"Error parsing position timestamp: {e}")
            except Exception as e:
                logger.error(f"Error handling position click: {e}")

    def nextFrameChanged(self):

        speed = 0
        if self.timelinecontroller.nextFrame == SpeedStatus.SpeedEnum.Pause:
            speed = SpeedStatus.SpeedEnum.Up1X
        elif self.timelinecontroller.nextFrame == SpeedStatus.SpeedEnum.Up1X:
            speed = SpeedStatus.SpeedEnum.Up1X
        elif self.timelinecontroller.nextFrame == SpeedStatus.SpeedEnum.Up2X:
            speed = SpeedStatus.SpeedEnum.Up2X
        elif self.timelinecontroller.nextFrame == SpeedStatus.SpeedEnum.Up4X:
            speed = SpeedStatus.SpeedEnum.Up4X
        elif self.timelinecontroller.nextFrame == SpeedStatus.SpeedEnum.Up8X:
            speed = SpeedStatus.SpeedEnum.Up8X

        if self.player.stream_type == CommonEnum.StreamType.VIDEO_STREAM and self.player.is_playing():
            try:
                self.player.set_speed(speed)
                # self.timelinecontroller.nextFrame = speed
            except ValueError as e:
                logger.error(f"Error parsing position timestamp: {e}")
            except Exception as e:
                logger.error(f"Error handling position click: {e}")

    def hoverPositionChanged(self, position):
        pass

    def selectedDurationChanged(self, startTime, endTime):
        logger.info(f'selectedDurationChanged = {startTime,endTime}')
        self.selectedDuration["start"] = startTime
        self.selectedDuration["end"] = endTime

    def show_menu(self, position):
        logger.debug(f'showMenu = {position, type(position)}')
        def handle_clear_selection():
            logger.debug(f'handle_clear_selection')
            self.timelinecontroller.clearSelectionChanged.emit()
        def handle_zoom_to_selection():
            logger.debug(f'handle_zoom_to_selection')
            self.timelinecontroller.zoomToSelectionChanged.emit()
        def handle_export_video():
            logger.debug(f'handle_export_video')
        menu = QMenu()
        # menu.setStyleSheet(Style.StyleSheet.context_menu)
        menu.setStyleSheet(Style.PrimaryStyleSheet.get_context_menu_style(theme_instance=main_controller))
        clear_selection = menu.addAction(self.tr("Clear Selection"))
        clear_selection.triggered.connect(handle_clear_selection)
        zoom_to_selection = menu.addAction(self.tr('Zoom to Selection'))
        zoom_to_selection.triggered.connect(handle_zoom_to_selection)
        export_video = menu.addAction(self.tr('Export video'))
        export_video.triggered.connect(self.handle_export_video)
        current_mouse = QCursor.pos()
        logger.debug(f'current_mouse = {current_mouse}')
        position_of_mouse = self.cameraScreen.mapFromGlobal(current_mouse)
        logger.debug(f'position_of_mouse = {position_of_mouse}')
        menu.exec_(self.cameraScreen.mapToGlobal(position_of_mouse))
        self.timelinecontroller.closeMenu()

    def handle_export_video(self):
        logger.info(f'handle_export_video {self.selectedDuration}')
        if self.selectedDuration["start"] is None or self.selectedDuration["end"] is None:
            logger.error("thời gian export video không hợp lệ")
            return
        data = {
            "start": self.selectedDuration["start"],
            "end": self.selectedDuration["end"],
            "cameraId": self.cameraModel.get_property("id")
        }
        dialog = ExportVideoDialog(exportVideoData=ExportVideoData(data=data))
        result = dialog.exec()

    def next_chunk_signal(self, data):
        logger.debug(f'next_chunk_signal = {self.record_data}')
        if self.record_data is not None and self.player is not None:
            try:
                data = self.record_data.get_next_record(self.end_duration, self.cameraModel.get_property("id"))
                if data is not None:
                    record = data
                    # source = widget.record_capture.get_current_url()
                    self.record_url = record.data.url
                    self.seek_time = None
                    self.start_duration = record.data.start_duration
                    self.end_duration = record.data.end_duration
                    old_duration = self.player.current_duration
                    self.player.stop_video()
                    self.player.load_media(record.data.url, start_duration=record.data.start_duration,end_duration=record.data.end_duration)
                    self.player.play_video()
                    # self.timelinecontroller.scrollbarPositionChanged.emit()
                    self.timelinecontroller.shift_for_duration(self.start_duration - old_duration)
                    self.timelinecontroller.scrollbarPositionChanged.emit()
                    # self.timelinecontroller.isLive = False
                    # self.timelinecontroller.positionBubble = True
                else:
                    pass
                    # vào chế độ live
                    logger.debug(f'next_chunk_signal1')
                    self.timelinecontroller.isLive = True
                    self.timelinecontroller.positionBubble = False
                    self.timelinecontroller.isNextChunk = False
                    self.timelinecontroller.isNextFrame = False
                    self.timelinecontroller.nextFrame = SpeedStatus.SpeedEnum.Up1X

            except ValueError as e:
                logger.error(f"Error parsing position timestamp: {e}")
            except Exception as e:
                logger.error(f"Error handling position click: {e}")

    def previous_chunk_signal(self, data):
        logger.debug(f'previous_chunk_signal = {self.record_data}')
        if self.record_data is not None and self.player is not None and self.cameraModel is not None:
            if self.player.stream_type == CommonEnum.StreamType.VIDEO_STREAM:
                try:
                    data = self.record_data.get_previous_record(self.start_duration, self.cameraModel.get_property("id"))
                    logger.debug(f'previous_chunk_signal = {data}')
                    if data is not None:
                        record = data
                        # source = widget.record_capture.get_current_url()
                        self.record_url = record.data.url
                        self.seek_time = None
                        self.start_duration = record.data.start_duration
                        self.end_duration = record.data.end_duration
                        self.player.stop_video()
                        self.player.load_media(record.data.url, start_duration=record.data.start_duration,end_duration=record.data.end_duration)
                        self.player.play_video()
                        # self.timelinecontroller.isLive = False
                        # self.timelinecontroller.positionBubble = True
                    # else:
                    #     pass
                    #     # vào chế độ live
                    #     logger.debug(f'next_chunk_signal1')
                    #     self.timelinecontroller.isLive = True
                    #     self.timelinecontroller.positionBubble = False
                    #     self.timelinecontroller.isNextChunk = False
                    #     self.timelinecontroller.isNextFrame = False
                except ValueError as e:
                    logger.error(f"Error parsing position timestamp: {e}")
                except Exception as e:
                    logger.error(f"Error handling position click: {e}")

    @Slot("QVariant")
    def share_frame_signal(self, data):
        try:
            grab, mat_frame, pixmap_frame = data
            if grab and mat_frame is not None:
                if self.callback_post_process is not None:
                    pixmap_frame = self.callback_post_process(mat_frame)
                    for frameModel in self.frameModels:
                        frameModel.updateFrame(pixmap_frame)
            elif grab and pixmap_frame is not None:
                for frameModel in self.frameModels:
                    frameModel.updateFrame(pixmap_frame)

        except Exception as e:
            import traceback

    @Slot(str)
    def camera_state_signal(self, camera_state):
        try:
            camera_id = self.cameraModel.get_property("id") if self.cameraModel else "Unknown"
            current_state = self.connectionState
            logger.debug(f'🎬 [GRID_ITEM] camera_state_signal called for camera {camera_id}: {current_state} → {camera_state}')

            if current_state != camera_state:
                self.connectionState = camera_state
                logger.debug(f'🎬 [GRID_ITEM] connectionState updated for camera {camera_id}')
                # Clear frame when camera is stopped
                if camera_state == "stopped":
                    for frameModel in self.frameModels:
                        frameModel._q_image = None
                        frameModel._frame_count = 0
                        frameModel.update()  # Trigger repaint to clear the frame
        except Exception as e:
            logger.error(f'camera_state_signal error: {e}')
            import traceback
            traceback.print_exc()

    def buffering_signal(self, percent):
        temp = int(percent)
        if temp < 100:
            self.percent = str(temp) + "%"
            self.connectionState = "buffering"
        else:
            self.connectionState = "started"

    def start_threads(self,number: int, target: Callable, *args) -> List[threading.Thread]:
        threads = []
        for _ in range(number):
            thread = threading.Thread(target=target, args=args)
            thread.daemon = True
            threads.append(thread)
            thread.start()
        return threads
    
    @Slot()
    def closeEvent(self):
        self.input_queue.put(None)
        threadPoolManager.wait_for_done(pool_name=self.uuid)
        
    def __str__(self):
        """String representation for debugging"""
        camera_name = self.getCameraName()
        return f"CameraGridItem(row={self._row}, col={self._col}, camera='{camera_name}', playing={self._isPlaying}, state={self._connectionState})"
