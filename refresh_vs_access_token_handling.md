# Refresh Token vs Access Token Handling

## Requirement Analysis
**User Request**: "kiểm tra refresh_refresh_token, yêu cầu đăng nhập lại còn access_token thì k cần đăng nhập"

**Translation**: 
- **refresh_token expired** → Require re-login
- **access_token expired** → Auto-refresh, no re-login needed

## Implementation Overview

### **Token Lifecycle Management**

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   access_token  │    │  refresh_token   │    │   User Action   │
│   (Short-lived) │    │  (Long-lived)    │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
    Expires in                Expires in              Initial login
    15-30 minutes            7-30 days                required
         │                       │                       │
         ▼                       ▼                       ▼
   ✅ Auto-refresh          ❌ Require re-login      ✅ Get both tokens
   (Seamless UX)           (Security measure)       (Store securely)
```

## Enhanced Implementation

### **1. API Client - Smart Token Refresh**

**File**: `src/api/api_client.py`

#### Enhanced `refresh_access_token()` Method:

```python
def refresh_access_token(self):
    """
    ✅ SMART REFRESH: Handle both access_token and refresh_token expiration
    
    Returns:
        - New access_token (str): When refresh successful
        - "REFRESH_TOKEN_EXPIRED" (str): When refresh_token expired → Need re-login
        - None: When other errors occurred
    """
    try:
        # ... validation and setup ...
        
        response = requests.post(refresh_url, headers=headers, data=payload)
        
        # ✅ SUCCESS: Token refresh successful (access_token was expired)
        if response.status_code == HTTPStatus.OK.value:
            json_data = response.json()
            
            # Check for server-side error codes
            if json_data.get('code') == '400':
                error_message = json_data.get('message', 'Unknown error')
                
                # Detect refresh_token expiration from error message
                if 'refresh' in error_message.lower() or 'expired' in error_message.lower():
                    logger.error(f"🔴 [REFRESH] Refresh token expired - user needs to login again")
                    return "REFRESH_TOKEN_EXPIRED"  # ❌ Need re-login
                
                return None  # Other server errors
            
            # Update both tokens
            data = json_data.get("data")
            if data:
                self.refresh_token = data.get("refreshToken")  # ✅ Update refresh_token
                self.access_token = data.get("accessToken")    # ✅ Update access_token
                
                # Persist new tokens
                AuthQSettings.get_instance().save_access_token(self.access_token)
                
                logger.info(f"✅ [REFRESH] Tokens refreshed successfully")
                return self.access_token  # ✅ Success - no re-login needed
        
        # ✅ UNAUTHORIZED: Refresh token expired
        elif response.status_code == HTTPStatus.UNAUTHORIZED.value:
            logger.error(f"🔴 [REFRESH] Refresh token expired (401) - user needs to login again")
            return "REFRESH_TOKEN_EXPIRED"  # ❌ Need re-login
        
        # ✅ OTHER ERRORS: Network, server issues
        else:
            logger.error(f"🔴 [REFRESH] Token refresh failed - Status: {response.status_code}")
            return None  # Network/server errors
            
    except Exception as e:
        logger.error(f"🔴 [REFRESH] Exception during token refresh: {e}")
        return None
```

### **2. WebSocket Client - Re-login UI Integration**

**File**: `src/common/websocket/websocket_client.py`

#### Enhanced `refresh_token()` Method:

```python
def refresh_token(self) -> bool:
    """Handle WebSocket token refresh with re-login UI"""
    try:
        controller = controller_manager.get_controller(server_ip=self.server_ip)
        refresh_result = controller.refresh_access_token()
        
        # ❌ REFRESH TOKEN EXPIRED: Trigger re-login UI
        if refresh_result == "REFRESH_TOKEN_EXPIRED":
            logger.error(f"🔴 REFRESH TOKEN EXPIRED for server {self.server_ip}!")
            logger.error(f"🔴 User needs to login again - triggering re-login UI")
            
            self._trigger_relogin_ui()  # ✅ Show re-login dialog
            return False
        
        # ✅ ACCESS TOKEN REFRESHED: Continue seamlessly
        if refresh_result and isinstance(refresh_result, str) and refresh_result != "REFRESH_TOKEN_EXPIRED":
            self.header['Authorization'] = f"Bearer {refresh_result}"
            
            # Notify all components of token change
            # ... notification logic ...
            
            logger.info(f"✅ WebSocket token refreshed successfully")
            return True
            
        return False
        
    except Exception as e:
        logger.error(f"Token refresh error: {e}")
        return False
```

#### New `_trigger_relogin_ui()` Method:

```python
def _trigger_relogin_ui(self):
    """
    ✅ RELOGIN: Trigger re-login UI when refresh token expires
    """
    try:
        logger.info(f"🔑 [RELOGIN] Triggering re-login UI for server {self.server_ip}")
        
        def show_relogin_dialog():
            try:
                # Get main window
                app = QApplication.instance()
                main_window = app.activeWindow()
                
                # Show re-login dialog
                from src.common.widget.dialogs.relogin_dialog import ReLoginDialog
                from src.utils.auth_qsettings import AuthQSettings
                
                username = AuthQSettings.get_instance().settings.value("username", "")
                dialog = ReLoginDialog(parent=main_window, username=username, server=None)
                
                logger.info(f"🔑 [RELOGIN] Showing re-login dialog for user: {username}")
                result = dialog.exec()
                
                if result == dialog.Accepted:
                    logger.info(f"✅ [RELOGIN] User successfully re-logged in")
                else:
                    logger.warning(f"❌ [RELOGIN] User cancelled re-login")
                    
            except Exception as e:
                logger.error(f"🔴 [RELOGIN] Error showing re-login dialog: {e}")
        
        # Show dialog in main thread
        QTimer.singleShot(100, show_relogin_dialog)
        
    except Exception as e:
        logger.error(f"🔴 [RELOGIN] Error triggering re-login UI: {e}")
```

## Flow Diagrams

### **Access Token Expiration Flow (Seamless)**:

```
API Request with expired access_token
↓
401 Unauthorized Error
↓
WebSocket/API calls refresh_access_token()
↓
Server Response: 200 OK
├─ New access_token received ✅
├─ New refresh_token received ✅
├─ Tokens saved to storage ✅
└─ Continue operation seamlessly ✅
↓
User Experience: No interruption 🎉
```

### **Refresh Token Expiration Flow (Re-login Required)**:

```
API Request with expired access_token
↓
401 Unauthorized Error
↓
WebSocket/API calls refresh_access_token()
↓
Server Response: 401 Unauthorized OR 400 with "refresh expired"
├─ Return "REFRESH_TOKEN_EXPIRED" ❌
├─ WebSocket calls _trigger_relogin_ui() 🔑
├─ Show ReLoginDialog to user 📱
└─ Wait for user re-authentication ⏳
↓
User Action Required:
├─ ✅ Re-login successful → Continue with new tokens
└─ ❌ Cancel/Fail → Application limited functionality
```

## Benefits

### ✅ **Clear Token Lifecycle Management**
- **access_token expiration**: Handled automatically, seamless UX
- **refresh_token expiration**: Proper re-login flow, secure

### ✅ **Enhanced Security**
- Refresh tokens have longer expiration (security vs UX balance)
- Automatic re-login when refresh tokens expire
- No indefinite token usage

### ✅ **Improved User Experience**
- **Normal usage**: No interruptions from access_token refresh
- **Long idle periods**: Graceful re-login prompt when needed
- **Clear feedback**: User knows when re-authentication is required

### ✅ **Robust Error Handling**
- Distinguish between different token expiration scenarios
- Proper fallback mechanisms
- Comprehensive logging for debugging

## Testing Scenarios

1. **Normal Operation**: ✅ access_token expires → auto-refresh → continue
2. **Long Idle**: ✅ refresh_token expires → show re-login → user authenticates
3. **Network Issues**: ✅ refresh fails → retry logic → eventual re-login if needed
4. **Server Errors**: ✅ proper error handling → appropriate user feedback

## Expected User Experience

### **Short-term Usage (access_token expiration)**:
- ✅ **User sees**: No interruption, seamless operation
- ✅ **Behind scenes**: Auto token refresh every 15-30 minutes

### **Long-term Usage (refresh_token expiration)**:
- ✅ **User sees**: "Session expired, please login again" dialog
- ✅ **User action**: Enter password, continue using app
- ✅ **Security**: Prevents indefinite access without re-authentication

The implementation now properly handles both token types according to security best practices! 🎉
