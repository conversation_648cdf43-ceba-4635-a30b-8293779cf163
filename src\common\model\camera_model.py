from typing import List
from src.utils.utils import Utils
from src.common.model.group_model import Group
from src.common.model.aiflows_model import AiFlow
from src.common.model.user_model import Users
from src.common.onvif_api.camera_model import Resolution
from dataclasses import dataclass, asdict, fields
from PySide6.QtCore import QObject, Property, Signal
from src.common.model.aiflows_model import aiflow_model_manager
from src.common.qml.models.common_enum import CommonEnum
from src.common.model.model import Model
import enum
from enum import IntEnum
from src.styles.style import Style
import logging
logger = logging.getLogger(__name__)
class PtzCapType:
    zoom_limits = "zoom_limits"
    pan_tilt_limits = "pan_tilt_limits"
    default_ptz_speed = "default_ptz_speed"
    absolute_pan_tilt = "absolute_pan_tilt"
    absolute_zoom = "absolute_zoom"
    relative_pan_tilt = "relative_pan_tilt"
    relative_zoom = "relative_zoom"
    continuous_pan_tilt = "continuous_pan_tilt"
    continuous_zoom = "continuous_zoom"

@dataclass
class Parent:
    @classmethod
    def from_dict(cls, data: dict):
        filtered_data = {key: value for key,
                            value in data.items() if hasattr(cls, key)}
        return cls(**filtered_data)
class SizeCameraItem(enum.Enum):
    SMALL = 1
    MEDIUM = 2
    LARGE = 3

class FovModeItem(enum.Enum):
    ICON = 'ICON'
    RECTANGLE = 'RECTANGLE'
    CIRCLE = 'CIRCLE'
    POLYGON = 'POLYGON'

class TypeCameraItem(enum.Enum):
    PTZ = 0
    BULLET = 1
    DOME = 2

class CameraColor(enum.Enum):
    ORANGE = "#FF9A24"
    PURPLE = "#675DF0"
    RED = "#FF6969"
    BLUE = "#1191E3"
    PINK = "#EF5E9A"
    CYAN = "#109FA1"
    GREEN = "#31B23A"

@dataclass
class Camera:
    id: str = None
    name: str = None
    type: str = None
    active: bool = None
    check_box: bool = None
    btn_edit: bool = None
    btn_trash: bool = None
    activate: bool = None
    urlMainstream: str = None
    urlSubstream: str = None
    urlRestream: str = None
    urlRtmpstream: str = None
    username: str = None
    password: str = None
    ipAddress: str = None
    port: int = None
    mainstreamFps: int = None
    mainstreamResolution: str = None
    description: str = None
    onvifBrightness: str = None
    onvifSharpness: str = None
    onvifColorSaturation: str = None
    onvifContrast: str = None
    onvifDirectionFrom: str = None
    onvifDirectionTo: str = None
    onvifZoomIn: str = None
    onvifZoomOut: str = None
    polygonCoordinatesPoint1X1: int = None
    polygonCoordinatesPoint1Y1: int = None
    polygonCoordinatesPoint2X2: int = None
    polygonCoordinatesPoint2Y2: int = None
    polygonCoordinatesPoint3X3: int = None
    polygonCoordinatesPoint3Y3: int = None
    polygonCoordinatesPoint4X4: int = None
    polygonCoordinatesPoint4Y4: int = None
    profileToken: str = None
    recordSetting: bool = None
    recordingState: str = None
    recordQuality: str = None
    recordTimelapseSpeed: str = None
    recordSegmentInterval: int = None
    recordResolution: str = None
    mainstreamFps: int = None
    mainstreamResolution: str = None
    supportedMainFps: str = None
    supportedMainResolution: str = None
    substreamFps: int = None
    substreamResolution: str = None
    supportedSubFps: str = None
    supportedSubResolution: str = None
    state: str = None
    restreamEndpoint: str = None
    profileToken: str = None
    polygonDTOS: List[dict] = None
    recordResolution: str = None
    supportedMainResolution: List['Resolution'] = None
    supportedSubResolution: List['Resolution'] = None
    supportedMainFps: List['Resolution'] = None
    supportedSubFps: List['Resolution'] = None
    cameraGroupIds: List[str] = None
    cameraGroupDTOList: List[Group] = None
    aiFlowIds: List[int] = None
    aiFlowDTOList: List[AiFlow] = None
    userIds: List[int] = None
    userDTOList: List[Users] = None
    eventIds: int = None
    serviceIds: List[int] = None
    cameraBranch: str = None
    cameraType: str = None
    cameraModel: str = None
    coordinateLat: str = None
    coordinateLong: str = None
    address: str = None
    server_ip: str = None
    recognitionCount: int = None
    protectionCount: int = None
    frequencyCount: int = None
    accessCount: int = None
    motionCount: int = None
    trafficCount: int = None
    weaponCount: int = None
    ufoCount: int = None
    protectionRecognitionCount: int = None
    threatDetectionCount: int = None
    avigilonCameraId: str = None
    ptzCap: List[str] = None
    presets: str = None
    features: List[str] = None
    diffFeatures: List[str] = None
    mainRestream: str = None
    subRestream: str = None
    added: bool = None
    # Map ##############
    color: str = None
    fovEnable: bool = None
    fovMode: int = None
    fovData: str = None
    size: int = None
    nameEnable: bool = None
    mapId: str = None
    floorId: str = None
    #######################
    @classmethod
    def from_dict(cls, data_dict):
        field_names = {field.name for field in fields(cls)}
        filtered_dict = {key: value for key, value in data_dict.items() if key in field_names}
        return cls(**filtered_dict)

    def to_dict(self):
        # only return the fields that are not None
        result = {}
        for k, v in self.__dict__.items():
            if k == 'coordinateLat' or k == 'coordinateLong':
                result[k] = v
            elif v is None:
                continue
            else:
                result[k] = v
        return result

class CameraModel(Model):
    change_name_signal = Signal(str)
    change_check_box_signal = Signal(str)
    change_camera_group_signal = Signal(list)
    change_ai_flow_signal = Signal(int)
    change_model = Signal(tuple)
    delete_camera_signal = Signal(QObject)
    get_groups_signal = Signal(str)
    change_active_signal = Signal(tuple)
    add_rtmp_signal = Signal(str)
    add_ai_flow = Signal(tuple)
    # Map
    idChanged = Signal()
    nameChanged = Signal()
    stateChanged = Signal()
    cameraGroupIdsChanged = Signal()
    stateMergeChanged = Signal()
    longitudeChanged = Signal()
    latitudeChanged = Signal()
    locationChanged = Signal()
    urlMainstreamChanged = Signal()
    recordSettingChanged = Signal()
    recordingStateChanged = Signal()
    permissionGrantedChanged = Signal()
    accessTokenChanged = Signal()
    refreshTokenChanged = Signal()
    def __init__(self,data: dict = {}):
        super().__init__(data = data)

        # self.data.active = False
        # self.data.check_box = False
        # self.data.btn_edit = True
        # self.data.btn_trash = True
        self.list_parent_tree = []
        self.list_standard_item = []
        # Map
        self.serverIp = self.get_property('server_ip')
        self.fov_enable = self.get_property("fovEnable") if self.get_property("fovEnable") is not None else True
        self.fov_mode = FovModeItem(self.get_property("fovMode")) if self.get_property("fovMode") is not None else FovModeItem.ICON
        self.fov_data = self.get_property("fovData")
        self._state_merged =  CommonEnum.CameraState.CONNECTED_REC_PIN
        self._recordingState = False

        self.name_enable = self.get_property("nameEnable") if self.get_property("fovEnable") is not None else True
        self.color = CameraColor(self.get_property("color")) if self.get_property("color") is not None else CameraColor.BLUE
        self.size = SizeCameraItem(self.get_property("size")) if self.get_property("size") is not None else SizeCameraItem.MEDIUM

        self._aiflowTypes = []
        self.get_current_ai_flow_type()

        # PTZ capabilities
        self.onvifSupported = False
        self.ptzSupported = False
        self.ptz3DSupport = False
        self.update_ptz_capabilities()

    def get_current_ai_flow_type(self):
        for flow in self.get_property("aiFlowIds",[]):
            aiflow = aiflow_model_manager.get_aiflow_model(id = flow)
            if aiflow.data.type is not None and aiflow.data.type not in self._aiflowTypes:
                self._aiflowTypes.append(aiflow.data.type)

    @Property(str,notify=idChanged)
    def id(self):
        return self.get_property("id",None)

    @id.setter
    def id(self, value: str):
        if self.get_property("id",None) != value:
            self.set_property("id",value)
            self.idChanged.emit()

    def register_standard_item(self, standard_item):
        self.list_standard_item.append(standard_item)

    def unregister_standard_item(self,standard_item):
        try:
            standard_item.disconnect_slot()
            self.list_standard_item.remove(standard_item)
        except Exception as e:
            print(f'unregister_standard_item = {e}')

    def clear_list_standard_item(self):
        for standard_item in self.list_standard_item:
            standard_item.disconnect_slot()
        self.list_standard_item = []

    def set_name(self,name):
        self.set_property("name", name)
        self.change_model.emit(('name',name,self))

    def set_description(self, description):
        self.set_property("description", description)
        self.change_model.emit(('description', description, self))

    # def set_warning(self,event_ai = None):
    #     self.change_model.emit(('warning',event_ai,self))

    # def set_check_box(self,check_box):
    #     self.data.check_box = check_box
    #     self.change_model.emit(('check_box',check_box,self))

    def set_camera_group(self,cameraGroupIds = []):
        self.set_property("cameraGroupIds", cameraGroupIds)
        self.change_model.emit(('cameraGroupIds',cameraGroupIds,self))

    def set_ai_flow(self,ai_flow_id = None):
        self.set_property("aiFlowIds", ai_flow_id)
        self.change_model.emit(('aiFlowIds',ai_flow_id,self))

    def set_record_setting(self,flag = None):
        self.set_property("recordSetting", flag)
        self.change_model.emit(('recordSetting',flag,self))

    def set_state(self,flag = None):
        camera_id = self.get_property("id")
        old_state = self.get_property("state")
        is_phone = self.isPhoneCamera()
        logger.debug(f'📡 [CAMERA_STATE] set_state called for camera {camera_id}: {old_state} → {flag} (isPhone: {is_phone})')
        self.state = flag
        logger.debug(f'📡 [CAMERA_STATE] set_state completed for camera {camera_id}')
        self.change_model.emit(('state',flag,self))

    def set_active(self,flag = None):
        logger.debug(f"set_active = {flag}")
        self.set_property("active", flag)
        self.change_active_signal.emit(('active',flag,self))

    def set_aiFlowDTOList(self,aiFlowDTOList = None):
        logger.debug(f"set_aiFlowDTOList called with {len(aiFlowDTOList) if aiFlowDTOList else 0} items")
        self.set_property("aiFlowDTOList", aiFlowDTOList)
        logger.debug(f"Emitting change_model signal for aiFlowDTOList")
        self.change_model.emit(('aiFlowDTOList',aiFlowDTOList,self))

    def set_recognition_security_count(self, value = None):
        logger.debug(f"set_recognition_security_count called with value {value}")
        self.set_property("recognitionCount", value)
        self.set_property("protectionCount", value)
        self.set_property("frequencyCount", value)
        self.set_property("accessCount", value)
        self.set_property("motionCount", value)
        self.set_property("trafficCount", value)
        logger.debug(f"Emitting change_model signal for recognition_security_count")
        self.change_model.emit(('recognition_security_count', value, self))

    def set_risk_identification_count(self, value = None):
        logger.debug(f"set_risk_identification_count called with value {value}")
        self.set_property("weaponCount", value)
        self.set_property("ufoCount", value)
        logger.debug(f"Emitting change_model signal for risk_identification_count")
        self.change_model.emit(('risk_identification_count', value, self))

    def set_protection_recognition_count(self, value = None):
        logger.debug(f"set_protection_recognition_count called with value {value}")
        self.set_property("protectionRecognitionCount", value)
        logger.debug(f"Emitting change_model signal for protectionRecognitionCount")
        self.change_model.emit(('protectionRecognitionCount', value, self))

    def set_threat_detection_count(self, value = None):
        logger.debug(f"set_threat_detection_count called with value {value}")
        self.set_property("threatDetectionCount", value)
        logger.debug(f"Emitting change_model signal for threatDetectionCount")
        self.change_model.emit(('threatDetectionCount', value, self))
        
    def set_aiflow_count(self, key, value):
        logger.debug(f"set_aiflow_count called with key={key}, value={value}")
        if key == 'recognitionCount':
            self.set_property("recognitionCount", value)
        elif key == 'protectionCount':
            self.set_property("protectionCount", value)
        elif key == 'frequencyCount':
            self.set_property("frequencyCount", value)
        elif key == 'accessCount':
            self.set_property("accessCount", value)
        elif key == 'motionCount':
            self.set_property("motionCount", value)
        elif key == 'trafficCount':
            self.set_property("trafficCount", value)
        elif key == 'weaponCount':
            self.set_property("weaponCount", value)
        elif key == 'ufoCount':
            self.set_property("ufoCount", value)
        elif key == 'features':
            self.set_property("features", value)
        else:
            return False
        return True
    def diff_camera_model(self, camera:dict = {}):
        dict1 = self.data
        dict2 = camera
        # print(f'ahihi {self.data.server_ip}-----{self.data.aiFlowIds} -- {camera.aiFlowIds}')
        diff = []
        for field,value in dict2.items():
            if dict1[field] != value:
                logger.debug(f"Field {field} changed from {dict1[field]} to {value}")
                if field == 'name':
                    diff.append(field)
                    logger.debug(f"Updating name to {value}")
                    self.set_name(value)
                elif field == 'description':
                    diff.append(field)
                    logger.debug(f"Updating description to {value}")
                    self.set_description(value)
                elif field == 'cameraGroupIds':
                    diff.append(field)
                    logger.debug(f"Updating cameraGroupIds to {value}")
                    self.set_camera_group(value)
                elif field == 'aiFlowIds':
                    diff.append(field)
                    logger.debug(f"Updating aiFlowIds to {value}")
                    self.set_ai_flow(value)
                elif field == 'aiFlowDTOList':
                    diff.append(field)
                    logger.debug(f"Updating aiFlowDTOList to {value}")
                    self.set_aiFlowDTOList(value)
                elif field == 'recordSetting':
                    diff.append(field)
                    logger.debug(f"Updating recordSetting to {value}")
                    self.set_record_setting(value)

                elif field == 'coordinateLat':
                    diff.append(field)
                    logger.debug(f"Updating coordinateLat to {value}")
                    self.latitude = value
                    # self.set_property("coordinateLat", value)
                    self.change_model.emit(('coordinateLat',value,self))

                elif field == 'coordinateLong':
                    diff.append(field)
                    logger.debug(f"Updating coordinateLong to {value}")
                    self.longitude = value
                    # self.set_property("coordinateLong", value)
                    self.change_model.emit(('coordinateLong',value,self))

                elif field == 'state':
                    diff.append(field)
                    logger.debug(f"Updating state to {value}")
                    self.set_state(value)
                elif field == 'protectionRecognitionCount':
                    diff.append(field)
                    logger.debug(f"Updating protectionRecognitionCount to {value}")
                    self.set_protection_recognition_count(value)
                elif field == 'threatDetectionCount':
                    diff.append(field)
                    logger.debug(f"Updating threatDetectionCount to {value}")
                    self.set_threat_detection_count(value)
                elif self.set_aiflow_count(field,value):
                    logger.debug(f"Updating {field} to {value}")
                    self.change_model.emit((field,value,self))

                else:
                    diff.append(field)
                    if field != 'active' and field != 'check_box' and field != 'btn_edit' and field != 'btn_trash' and field != 'urlRtmpstream' and field != 'server_ip' and field != 'clientId':
                        # setattr(self.data, field, value)
                        self.data[field] = value
        # print(f'diff_camera_model = {diff}')
        return diff
    
    def syncFloorData(self,newData:dict):
        self.set_property("floorId",newData.get("floorId"))
        self.set_property("coordinateLat",newData.get("coordinateLat"))
        self.set_property("coordinateLong",newData.get("coordinateLong"))
        self.set_property("fovData",newData.get("fovData"))
        self.set_property("color",newData.get("color"))
        self.set_property("fovEnable",newData.get("fovEnable"))
        self.set_property("fovMode",newData.get("fovMode"))
        self.set_property("nameEnable",newData.get("nameEnable"))
        self.set_property("size",newData.get("size"))

    def isLatLogChanged(self,data:dict):
        if self.get_property("coordinateLat") != data.get("coordinateLat") or self.get_property("coordinateLong") != data.get("coordinateLong"):
            return True
        return False
    # Map
    @Property(str, notify=idChanged)
    def id(self)->str:
        return self.get_property('id')

    @Property(str,notify=nameChanged)
    def name(self):
        return self.get_property('name')

    @name.setter
    def name(self, value: str):
        if self.get_property('name') != value:
            self.set_property("name",value)
            self.nameChanged.emit()

    @Property(str,notify=stateChanged)
    def state(self):
        return self.get_property("state")

    @state.setter
    def state(self, value: str):
        if self.get_property("state") != value:
            old_state = self.get_property("state")
            logger.debug(f'📡 [CAMERA_STATE] Camera {self.get_property("id")} state change: {old_state} → {value}')
            self.set_property("state",value)
            self.stateChanged.emit()
            self.stateMergeChanged.emit()

    @Property(list, notify=cameraGroupIdsChanged)
    def cameraGroupIds(self):
        return self.get_property("cameraGroupIds")
    
    @cameraGroupIds.setter
    def cameraGroupIds(self, value: list):
        if self.get_property("cameraGroupIds") != value:
            self.set_property("cameraGroupIds",value)
            self.cameraGroupIdsChanged.emit()

    @Property(list, notify=change_ai_flow_signal)
    def aiFlowTypes(self):
        self.get_current_ai_flow_type()
        return self._aiflowTypes
            
    @Property(int,notify=stateMergeChanged)
    def state_merged(self):
        if self.get_property("state") == "CONNECTED":
            if self.get_property("coordinateLat") is not None and self.get_property("coordinateLong") is not None:
                if self.get_property("recordingState") == "ACTIVE":
                    self._state_merged = CommonEnum.CameraState.CONNECTED_REC_PIN
                else:
                    self._state_merged = CommonEnum.CameraState.CONNECTED_NOREC_PIN
            else:
                if self.get_property("recordingState") == "ACTIVE":
                    self._state_merged = CommonEnum.CameraState.CONNECTED_REC_UNPIN
                else:
                    self._state_merged = CommonEnum.CameraState.CONNECTED_NOREC_UNPIN
        else:
            if self.get_property("coordinateLat") is not None and self.get_property("coordinateLong") is not None:
                self._state_merged = CommonEnum.CameraState.DISCONNECTED_NOREC_PIN
            else:
                self._state_merged = CommonEnum.CameraState.DISCONNECTED_NOREC_UNPIN  
        return self._state_merged
    
    @state_merged.setter
    def state_merged(self, value: int):
        if self._state_merged != value:
            self._state_merged = value
            self.stateMergeChanged.emit() 

    @Property(float,notify=longitudeChanged)
    def longitude(self):
        return self.get_property("coordinateLong")

    @longitude.setter
    def longitude(self, value: float):
        if self.get_property("coordinateLong") != value:
            self.set_property("coordinateLong",value)
            self.longitudeChanged.emit()
            self.stateMergeChanged.emit()

    @Property(float,notify=latitudeChanged)
    def latitude(self):
        return self.get_property("coordinateLat")

    @latitude.setter
    def latitude(self, value: float):
        if self.get_property("coordinateLat") != value:
            self.set_property("coordinateLat",value)
            self.latitudeChanged.emit()
            self.stateMergeChanged.emit()

    @Property(str,notify= urlMainstreamChanged)
    def urlMainstream(self)->str:
        return self.get_property("urlMainstream")

    @Property(str,notify=locationChanged)
    def location(self):
        return self.get_property("address")

    @location.setter
    def location(self, value: str):
        if self.get_property("address") != value:
            self.set_property("address",value)
            self.locationChanged.emit()
    @Property(bool,notify=recordSettingChanged)
    def recordSetting(self):
        return self._recordSetting

    @recordSetting.setter
    def recordSetting(self, value: bool):
        if self._recordSetting != value:
            self._recordSetting = value
            self.recordSettingChanged.emit()

    @Property(str,notify=recordingStateChanged)
    def recordingState(self):
        return self.get_property("recordingState")
    
    @recordingState.setter
    def recordingState(self, value: str):
        if self.get_property("recordingState") != value:
            self.set_property("recordingState",value)
            self.change_model.emit(('recordingState',value,self))
            self.recordingStateChanged.emit()  
            self.stateMergeChanged.emit()
               
            self.recordingStateChanged.emit()

    @Property(bool, notify=permissionGrantedChanged)
    def permissionGranted(self):
        return self.get_property("permissionGranted", False)

    @permissionGranted.setter
    def permissionGranted(self, value: bool):
        if self.get_property("permissionGranted") != value:
            old_value = self.get_property("permissionGranted", False)
            camera_id = self.get_property("id")
            logger.info(f'🔐 [PERMISSION] Camera {camera_id} permissionGranted: {old_value} → {value}')
            self.set_property("permissionGranted", value)
            self.permissionGrantedChanged.emit()
            logger.info(f'🔐 [PERMISSION] permissionGrantedChanged signal emitted for camera {camera_id}')

    def isPhoneCamera(self) -> bool:
        return self.get_property("type") == "PHONE"

    @classmethod
    def get_icon(self, camera_state:CommonEnum.CameraState):
        if camera_state == CommonEnum.CameraState.CONNECTED_REC_PIN:
            return Style.PrimaryImage.rec_pin
        elif camera_state == CommonEnum.CameraState.CONNECTED_REC_UNPIN:
            return Style.PrimaryImage.rec_unpin
        elif camera_state == CommonEnum.CameraState.CONNECTED_NOREC_PIN:
            return Style.PrimaryImage.norec_pin
        elif camera_state == CommonEnum.CameraState.CONNECTED_NOREC_UNPIN:
            return Style.PrimaryImage.norec_unpin
        elif camera_state == CommonEnum.CameraState.DISCONNECTED_REC_PIN:
            return Style.PrimaryImage.rec_pin
        elif camera_state == CommonEnum.CameraState.DISCONNECTED_REC_UNPIN:
            return Style.PrimaryImage.rec_unpin
        elif camera_state == CommonEnum.CameraState.DISCONNECTED_NOREC_PIN:
            return Style.PrimaryImage.norec_pin
        elif camera_state == CommonEnum.CameraState.DISCONNECTED_NOREC_UNPIN:
            return Style.PrimaryImage.norec_unpin

    @classmethod
    def get_qml_icon(self, camera_state:CommonEnum.CameraState):
        if camera_state == CommonEnum.CameraState.CONNECTED_REC_PIN:
            return Style.PrimaryImage.rec_pin_qml
        elif camera_state == CommonEnum.CameraState.CONNECTED_REC_UNPIN:
            return Style.PrimaryImage.rec_unpin_qml
        elif camera_state == CommonEnum.CameraState.CONNECTED_NOREC_PIN:
            return Style.PrimaryImage.norec_pin_qml
        elif camera_state == CommonEnum.CameraState.CONNECTED_NOREC_UNPIN:
            return Style.PrimaryImage.disconnected_norec_unpin_qml
        elif camera_state == CommonEnum.CameraState.DISCONNECTED_REC_PIN:
            return Style.PrimaryImage.rec_pin_qml
        elif camera_state == CommonEnum.CameraState.DISCONNECTED_REC_UNPIN:
            return Style.PrimaryImage.rec_unpin_qml
        elif camera_state == CommonEnum.CameraState.DISCONNECTED_NOREC_PIN:
            return Style.PrimaryImage.norec_pin_qml
        elif camera_state == CommonEnum.CameraState.DISCONNECTED_NOREC_UNPIN:
            return Style.PrimaryImage.disconnected_norec_unpin_qml
        return Style.PrimaryImage.disconnected_norec_unpin_qml      
        
    def update_ptz_capabilities(self):
        # """Update PTZ capabilities based on camera properties"""
        if hasattr(self, 'data') and self.data:
            ptz_caps = self.get_property("ptzCap", [])
            if ptz_caps is not None and len(ptz_caps) > 0:
                self.onvifSupported = True
                if PtzCapType.continuous_pan_tilt in ptz_caps and PtzCapType.pan_tilt_limits in ptz_caps:
                    self.ptzSupported = True
                if PtzCapType.relative_pan_tilt in ptz_caps and PtzCapType.default_ptz_speed in ptz_caps and PtzCapType.pan_tilt_limits in ptz_caps: 
                    self.ptz3DSupport = True
class CameraModelManager(QObject):
    add_camera_list_signal = Signal(tuple)
    delete_camera_model_signal = Signal(list)
    add_camera_signal = Signal(QObject)
    add_cameras_signal = Signal(tuple)
    complete_fetching_data = Signal(tuple)
    __instance = None
    def __init__(self):
        super().__init__()
        self.camera_list = {}
        self.complete_fetching_data.connect(self.complete_fetching_data_slot)

    @staticmethod
    def get_instance():
        if CameraModelManager.__instance is None:
            CameraModelManager.__instance = CameraModelManager()
        return CameraModelManager.__instance
    
    def complete_fetching_data_slot(self,data):
        data,controller = data
        data_list = []
        for index, item in enumerate(data):
            camera = CameraModel(data = item)
            camera.set_property("server_ip",controller.server.data.server_ip)
            camera.set_property("clientId",Utils.clientId)
            data_list.append(camera)
        camera_list = camera_model_manager.get_camera_list(server_ip=controller.server.data.server_ip) 
        if camera_list:
            if len(camera_list) == len(data_list):
                camera_model_manager.update_camera_list(camera_list=data_list)
            elif len(camera_list) > len(data_list):
                # sử dụng get_cameras() trong trường hợp delete camera
                camera_model_manager.find_camera_deleted(controller = controller,camera_list=data_list)
                # sau khi xóa Camera thì cần cập nhật Group từ server
                # self.get_cameras()

            else:
                # sử dụng get_cameras() trong trường hợp add camera
                # su dung trong websocket
                camera_list_added:CameraModel = camera_model_manager.find_camera_added(controller = controller, camera_list=data_list)
                is_cameraGroupIds = False
                camera_list = []
                for camera in camera_list_added:
                    if camera.get_property("cameraGroupIds") is not None and len(camera.get_property("cameraGroupIds")) > 0:
                        is_cameraGroupIds = True
                if is_cameraGroupIds:
                    controller.get_groups()    
        else:
            # sử dụng get_cameras() trong trường hợp init app
            camera_model_manager.add_camera_list(camera_list=data_list,controller = controller)

    def add_camera_list(self,camera_list:List[CameraModel] = [],controller = None):
        output = {}
        for camera_model in camera_list:
            camera_model.set_property("server_ip",controller.server.data.server_ip)
            output[camera_model.id] = camera_model
        # print(f'add_camera_list = {camera_list}')
        self.camera_list[controller.server.data.server_ip] = output
        self.add_camera_list_signal.emit((controller,camera_list))

    def add_camera(self,controller = None,camera:CameraModel = None):
        camera_result = self.get_camera_model(id=camera.id)
        if camera_result is None:
            print(f'add_camera AA = {camera.id}')
            self.camera_list[camera.get_property('server_ip')][camera.id] = camera
            self.add_camera_signal.emit(camera)

    def add_cameras(self,camera_list:List[CameraModel] = None):
        # Do đang mở websocket lắng nghe add camera, lên dùng thêm output_camera_list quản lý camera chưa được add vào self._camera_list
        output_camera_list = []
        for camera_model in camera_list:
            print(f'add_cameras BB = {camera_list}')
            if not any(camera_model.id == item.id for item in self.camera_list):
                self.camera_list.append(camera_model)
                output_camera_list.append(camera_model)
        self.add_cameras_signal.emit(output_camera_list)

    def update_camera_list(self, camera_list:List[CameraModel] = []):
        for camera in camera_list:
            for camera_model in self.camera_list[camera.get_property('server_ip')].values():
                if camera.id == camera_model.id:
                    # print(f'update_camera_list = {camera.data.name} -- {camera.data.aiFlowIds} -- {camera_model.data.aiFlowIds}')
                    camera_model.diff_camera_model(camera=camera.data)
                    break

    def find_camera_deleted(self,controller = None, camera_list:List[CameraModel] = []):
        temp = []
        for camera_model in self.camera_list[controller.server.data.server_ip].values():
            check = False
            for camera in camera_list:
                if camera_model.id == camera.id:
                    check = True
            if not check:
                temp.append(camera_model)

        for camera_model in temp:
            camera_model.delete_camera_signal.emit(camera_model)
            del self.camera_list[camera_model.get_property('server_ip')][camera_model.id]
        self.delete_camera_model_signal.emit(temp)

    def find_camera_added(self, camera_list:List[CameraModel] = [],controller = None):
        temp = []
        for camera_model in camera_list:
            check = False
            for camera in self.camera_list[camera_model.get_property('server_ip')].values():
                # print(f'camera === {camera}')
                if camera.id == camera_model.id:
                    check = True
            if not check:
                temp.append(camera_model)
        for camera_model in temp:
            # self.camera_list.append(camera_model)
            self.camera_list[camera_model.get_property('server_ip')][camera_model.id] = camera_model
        self.add_cameras_signal.emit((controller,temp))
        return temp

    def delete_camera(self,camera_model:CameraModel = None):
        self.camera_list.remove(camera_model)

    def get_camera_model(self,id = None, name = None):
        if id is not None:
            for idx,camera_list in self.camera_list.items():
                camera_model = camera_list.get(id,None)
                if camera_model is not None:
                    return camera_model
        elif name is not None:
            for idx,camera_list in self.camera_list.items():
                for key,camera_model in camera_list.items():
                    if name == camera_model.data.name:
                        return camera_model
        return None

    def get_camera_list(self,server_ip = None):
        if server_ip in self.camera_list:
            return self.camera_list[server_ip]
        else:
            return {}

    def delete_server(self,server_ip = None):
        if server_ip in self.camera_list:
            del self.camera_list[server_ip]

    def clear(self):
        self.camera_list = []

    def clear_list_standard_item(self):
        for camera_list in self.camera_list.values():
            for camera_model in camera_list.values():
                camera_model.clear_list_standard_item()

camera_model_manager = CameraModelManager.get_instance()

def filter_camera_model(camera_list: List[Camera]=[],camera_list_full:List[CameraModel] = []):
    # filter rtsp
    temp_camera_list = []
    for index, camera in enumerate(camera_list):
        try:
            # logger.debug(f'Camera ID: {camera.id}\n Camera Name: {camera.to_dict()}')
            
            # Kiểm tra ID trùng lặp
            is_duplicate = False
            for existing_camera in camera_list_full:
                try:
                    if isinstance(existing_camera, dict):
                        if existing_camera.get('id') == camera.id:
                            is_duplicate = True
                            logger.debug(f'Found duplicate camera ID: {camera.id}')
                            break
                    else:
                        if existing_camera.get_property("id") == camera.id:
                            is_duplicate = True
                            logger.debug(f'Found duplicate camera ID: {camera.id}')
                            break
                except AttributeError as e:
                    logger.warning(f'Error accessing camera data: {e}')
                    continue
                    
            if not is_duplicate:
                temp_camera_list.append(camera)
                logger.debug(f'Added camera to temp list: {camera.name}')
                
        except Exception as e:
            logger.error(f'Error processing camera at index {index}: {e}')
            continue

    # filter camera name
    flag = False
    while not flag:
        temp_flag = False
        for index, camera in enumerate(temp_camera_list):
            try:
                if any(isinstance(x, dict) and x.get('name') == camera.name or 
                      not isinstance(x, dict) and x.data.get("name") == camera.name 
                      for x in camera_list_full):
                    camera.name = camera.name + '_1'
                    temp_flag = True
            except AttributeError as e:
                logger.warning(f'Error checking camera name: {e}')
                continue

        if not temp_flag:
            flag = True

    return temp_camera_list

class AddCameraType:
    ONVIF = 'Onvif'
    RTSP_LINK = 'Rtsp Link'
