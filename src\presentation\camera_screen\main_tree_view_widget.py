import copy
import ast
from dataclasses import replace
from src.common.widget.dialogs.add_group_dialog import AddGroupDialog
from src.presentation.camera_screen.connect_3rd_party_server_dialog import Connect3rdPartyServerDialog
from src.common.model.device_models import TabType, AiType
from src.common.model.event_data_model import event_manager, EventModel
from src.common.widget.custom_standard_item import CustomStandardItem
from src.common.widget.dialogs.base_dialog import NewBaseDialog, FooterType
from src.common.widget.dialogs.add_floor_dialog import AddFloorDialog
from src.common.widget.dialogs.remove_map_item_dialog import RemoveMapItemDialog
from src.presentation.device_management_screen.widget.ai_state import AIType,AIFlowType
from src.common.model.aiflows_model import Ai<PERSON>low, aiflow_model_manager
from src.common.widget.menus.custom_menus import CustomMenuWithCheckbox, SubMenuOpenCameraInTab, CustomSubMenu, SubMenuOpenMapInGrid,SubMenuOpenFloorInGrid
from src.common.widget.custom_tree_view import TreeViewBase, EditDelegate, CustomStandardItemModel
from PySide6.QtCore import Qt, QSize, QCoreApplication, QTimer
from src.common.widget.notifications.notify import Notifications
from src.common.qml.models.map_controller import FloorModel,floor_manager,BuildingModel,building_manager,MapModel,map_manager
from src.common.threads.sub_thread import SubThread
from src.common.model.main_tree_view_model import ListDynamicCamera, MainTreeViewModel, TreeType, Server, ListItem,BaseItem, ListCamera,ListMap, \
    Status
from src.presentation.camera_screen.virtual_camera_grid_widget import VirtualCameraGridWidget
from src.presentation.camera_screen.camera_grid_widget import CameraGridWidget
from src.presentation.device_management_screen.widget.list_custom_widgets import InputWithTitle
from src.styles.style import Style
from src.common.controller.main_controller import main_controller,connect_slot
from src.common.widget.button_state import GridButtonModel, original_list_data_grid
from PySide6.QtWidgets import (
    QWidget,
    QVBoxLayout,
    QApplication,
    QMenu,QDialog, QToolTip, QTreeView, QLabel
)
from PySide6.QtGui import QStandardItem, QIcon, QAction, QCursor, QKeySequence, QColor, QBrush
from functools import partial
from typing import List
from src.common.model.camera_model import Camera, CameraModel, camera_model_manager
from src.common.model.group_model import Group, GroupModel, group_model_manager
from src.presentation.camera_screen.border_screen import BorderScreen
from queue import Queue
from src.common.onvif_api.worker_thread import WorkerThread
import re
import json
from src.common.key_board.key_board_manager import key_board_manager
from src.common.key_board.shortcut_key import shortcut_key_model_manager, ShortCutKey
from src.presentation.device_management_screen.widget.list_custom_widgets import LabelLineEdit
from src.common.widget.dialogs.camera_info_dialog import CameraInfoDialog
from src.common.widget.dialogs.warning_dialog import WarningDialog
from src.common.controller.controller_manager import Controller, controller_manager
from src.common.widget.notifications.listen_message_notifications import listen_show_notification
from src.common.qml.models.grid_model import GridModel
from src.presentation.camera_screen.managers.grid_manager import gridManager
from src.common.qml.models.common_enum import CommonEnum
from src.utils.utils import Utils
from unidecode import unidecode
import uuid
from src.utils.config import Config
import logging

logger = logging.getLogger(__name__)
class MainTreeViewWidget(TreeViewBase, MainTreeViewModel):

    def __init__(self, parent=None):
        super().__init__(parent)
        delegate = EditDelegate(parent=self, model_data=self.tree_data,
                                callback_setModelData=self.callback_setModelData)
        self.tree_view.setItemDelegateForColumn(0, delegate)
        self.tree_view.model_data = self.tree_data
        self.tree_view.setSelectionMode(QTreeView.SelectionMode.ExtendedSelection)
        main_controller.list_parent['MainTreeView'] = self
        key_board_manager.set_func(start_key=Qt.Key.Key_Slash,func=self.process_shortcut_id)
        key_board_manager.set_func(start_key=Qt.Key.Key_Asterisk,func=self.process_shortcut_id)
        key_board_manager.set_func(start_key= Qt.Key.Key_Alt,func=self.func_start_key,func_type ='func_start_key')
        self.list_standard_items = {}
        self.list_translate_item = []

        # Thêm timer để debounce selection events và tránh lag
        self.selection_debounce_timer = QTimer()
        self.selection_debounce_timer.setSingleShot(True)
        self.selection_debounce_timer.timeout.connect(self._process_delayed_selection)
        self._pending_selection_data = None
        self.filter_queue = Queue()
        self.fitler_tree_view_thread = WorkerThread(parent=self, target=self.process_treeview_data,
                                                    callback=self.update_treeview_ui)
        self.first_create_treeview_model = False
        self.previous_screen_index = None
        self.screen_index_widget = None
        self.connect_slot()

    def connect_slot(self):
        connect_slot(
            (event_manager.add_event_signal,self.add_event_signal),
            (camera_model_manager.delete_camera_model_signal,self.delete_camera_model),
            (group_model_manager.delete_group_model_signal,self.delete_group_model),
            (group_model_manager.add_group_signal,self.add_group_signal),
            (camera_model_manager.add_camera_signal,self.add_camera_signal),
            (camera_model_manager.add_cameras_signal,self.add_cameras_signal),
            (shortcut_key_model_manager.add_shortcut_key_list_signal,self.add_shortcut_key_list_signal),
            (shortcut_key_model_manager.add_shortcut_key_signal,self.add_shortcut_key_signal),
            (building_manager.createBuildingWSSignal,self.createBuildingWSSignal),
            (building_manager.removeBuildingWSSignal,self.removeBuildingWSSignal),
            (building_manager.updateBuildingWSSignal,self.updateBuildingWSSignal),
            (floor_manager.createFloorWSSignal,self.createFloorWSSignal),
            (floor_manager.removeFloorWSSignal,self.removeFloorWSSignal),
            # (floor_manager.updateFloorWSSignal,self.updateBuildingWSSignal),
            (controller_manager.exit_controller_signal,self.exit_controller_signal))

    def create_server(self,controller: Controller = None):
        server = Server(name=controller.server.data.server_ip,
                             list_cameras=ListCamera(name=self.tr('Camera List'), list_groups=[],
                                                     list_camera_non_group=[]),
                             list_virtual_windows=ListItem(type=TreeType.List_Virtual_Window,
                                                           name=self.tr('Virtual Window List')),
                             list_saved_views=ListItem(type=TreeType.List_Saved_View, name=self.tr('Saved View List')),
                             list_maps=ListMap(type=TreeType.List_Map, name=self.tr('Map List'))
                             )
        self.add_server(server)

    def put_filter_queue(self, msg):
        self.filter_queue.put(msg)
        self.process_treeview_data()

    def find_item_by_name(self, root_item, name, tree_type=TreeType.Camera):
        if root_item.text() == name and root_item.data(Qt.UserRole) == tree_type:
            return root_item
        for row in range(root_item.rowCount()):
            child = root_item.child(row)
            found_item = self.find_item_by_name(child, name, tree_type)
            if found_item:
                return found_item
        return None

    def change_item_color(self, item_name, color):
        # Start searching from the root item
        root_item = self.model.invisibleRootItem()
        item = self.find_item_by_name(root_item, item_name)
        if item:
            index = item.index()
            # self.tree_view.scrollTo(index, QAbstractItemView.ScrollHint.PositionAtTop)
            item.setBackground(color)

    def add_item(self, item=None, name=None, tree_type=TreeType.Server, model=None, is_tracking_item=False,is_add_group=False,server_ip = None, is_insert_top=False):
        root_item = CustomStandardItem(text=name, item_model=model,server_ip = server_ip)
        root_item.setData(tree_type, Qt.UserRole)
        if tree_type == TreeType.Server or tree_type == TreeType.Invalid:
            root_item.setIcon(QIcon(main_controller.get_theme_attribute('Image', 'treeview_server')))
        elif tree_type == TreeType.List_Camera:
            self.list_translate_item.append(root_item)
            root_item.setIcon(QIcon(main_controller.get_theme_attribute('Image', 'list_devices')))
        elif tree_type == TreeType.List_Virtual_Window:
            self.list_translate_item.append(root_item)

            root_item.setIcon(QIcon(main_controller.get_theme_attribute('Image', 'list_virtual_window')))
        elif tree_type == TreeType.List_Saved_View:
            self.list_translate_item.append(root_item)

            root_item.setIcon(QIcon(main_controller.get_theme_attribute('Image', 'list_save_view')))
        elif tree_type == TreeType.List_Map:
            self.list_translate_item.append(root_item)

            root_item.setIcon(QIcon(main_controller.get_theme_attribute('Image', 'list_map')))
        elif tree_type == TreeType.Virtual_Window_Item or tree_type == TreeType.Saved_View_Item:
            root_item.setIcon(QIcon(main_controller.get_theme_attribute('Image', 'close_all_virtual')))
        elif tree_type == TreeType.BuildingItem:
            if model is not None:
                if model.latitude is not None:
                    root_item.setIcon(QIcon(main_controller.get_theme_attribute('Image', 'building_on')))
                else:
                    root_item.setIcon(QIcon(main_controller.get_theme_attribute('Image', 'building_off')))
        elif tree_type == TreeType.FloorItem:
            root_item.setIcon(QIcon(Style.PrimaryImage.icon_floor))
        elif tree_type == TreeType.Group:
            if is_tracking_item:
                root_item.setBackground(QColor(0, 255, 0))
                root_item.setData('tracking_group', Qt.UserRole+1)
            else:
                root_item.setIcon(QIcon(main_controller.get_theme_attribute('Image', 'group_camera_treeview')))
        elif tree_type == TreeType.Camera:
            if model is not None:
                icon = CameraModel.get_icon(model.state_merged)
                if icon is not None:
                    root_item.setIcon(QIcon(icon))
                
        else:
            root_item.setIcon(QIcon(main_controller.get_theme_attribute('Image', 'group_camera_treeview')))
            
        if is_add_group or is_insert_top:
            if item is not None:
                item.insertRow(0, root_item)
        else:
            if item is not None:
                item.appendRow(root_item)
        if tree_type == TreeType.Virtual_Window_Item or tree_type == TreeType.Saved_View_Item or tree_type == TreeType.FloorItem:
            self.tree_view.expand(self.model.indexFromItem(item))
        elif tree_type == TreeType.Server:
            self.tree_view.expand(self.model.indexFromItem(item))

        if tree_type == TreeType.Camera and (model.state_merged == CommonEnum.CameraState.DISCONNECTED_REC_PIN or \
            model.state_merged == CommonEnum.CameraState.DISCONNECTED_REC_UNPIN or \
            model.state_merged == CommonEnum.CameraState.DISCONNECTED_NOREC_PIN or \
            model.state_merged == CommonEnum.CameraState.DISCONNECTED_NOREC_UNPIN):
            root_item.setForeground(QBrush(QColor(169, 169, 169)))
        else:
            root_item.setForeground(QBrush(QColor(main_controller.get_theme_attribute('Color', 'text_color_all_app'))))
        return root_item

    def get_item(self, name=None, tree_type=TreeType.Server,server_ip = None, model = None):
        if name is not None:
            for i in range(self.root_tree_view.rowCount()):
                item_child = self.root_tree_view.child(i, 0)
                item_data = item_child.data(Qt.UserRole)
                if item_data == tree_type and tree_type == TreeType.Server and item_child.server_ip == server_ip:
                    return item_child
                else:
                    for j in range(item_child.rowCount()):
                        item_child_1 = item_child.child(j, 0)
                        item_data = item_child_1.data(Qt.UserRole)
                        if item_data == tree_type and tree_type == TreeType.List_Virtual_Window:
                            return item_child_1
                        elif item_data == tree_type and tree_type == TreeType.List_Saved_View:
                            return item_child_1
                        elif item_data == tree_type and tree_type == TreeType.List_Map:
                            return item_child_1
                        elif item_data == tree_type and tree_type == TreeType.List_Camera:
                            return item_child_1
                        elif item_child_1.text() == name and item_data == tree_type:
                            return item_child_1
                        else:
                            for k in range(item_child_1.rowCount()):
                                item_child_2 = item_child_1.child(k, 0)
                                item_data = item_child_2.data(Qt.UserRole)
                                if item_child_2.text() == name and item_data == tree_type:
                                    return item_child_2
                                else:
                                    for h in range(item_child_2.rowCount()):
                                        item_child_3 = item_child_2.child(h, 0)
                                        item_data = item_child_3.data(Qt.UserRole)
                                        if item_child_3.text() == name and item_data == tree_type:
                                            return item_child_3
        if model is not None:
            for i in range(self.root_tree_view.rowCount()):
                item_child = self.root_tree_view.child(i, 0)
                item_data = item_child.data(Qt.UserRole)
                if item_data == tree_type and tree_type == TreeType.Server and item_child.server_ip == server_ip:
                    return item_child
                else:
                    for j in range(item_child.rowCount()):
                        item_child_1 = item_child.child(j, 0)
                        item_data = item_child_1.data(Qt.UserRole)
                        for k in range(item_child_1.rowCount()):
                            item_child_2 = item_child_1.child(k, 0)
                            item_data = item_child_2.data(Qt.UserRole)
                            if item_child_2.item_model == model:
                                return item_child_2
                            else:
                                for h in range(item_child_2.rowCount()):
                                    item_child_3 = item_child_2.child(h, 0)
                                    item_data = item_child_3.data(Qt.UserRole)
                                    if item_child_3.item_model == model:
                                        return item_child_3
        return None

    def add_camera_signal(self, camera_model: CameraModel = None):
        for server in self.tree_data.servers:
            if server.name == camera_model.get_property('server_ip'):
                camera_model.change_model.connect(self.update_camera_model)
                self.add_camera(camera_model=camera_model, server=server)
                standard_item = self.get_item('Camera List', tree_type=TreeType.List_Camera,server_ip=camera_model.get_property('server_ip'))
                root_item = self.add_camera_item(camera_model=camera_model,server_ip =server.name)
                standard_item.appendRow(root_item)
                break

    def add_cameras_signal(self, data):
        controller,camera_list = data
        for camera_model in camera_list:
            if camera_model.get_property("cameraGroupIds") is None or len(camera_model.get_property("cameraGroupIds")) == 0:
                for server in self.tree_filter_mode.servers:
                    if server.name == camera_model.get_property('server_ip'):
                        camera_model.change_model.connect(self.update_camera_model)
                        self.add_camera(camera_model=camera_model, server=server)
                        standard_item = self.get_item('Camera List', tree_type=TreeType.List_Camera,server_ip=camera_model.get_property('server_ip'))
                        root_item = self.add_camera_item(camera_model=camera_model,server_ip=server.name)
                        standard_item.appendRow(root_item)

    def add_group_signal(self, group):
        for server in self.tree_data.servers:
            if server.name == group.get_property('server_ip'):
                self.add_groups(list_groups={group.get_property('id'): group},server = server)
                standard_item = self.get_item('Camera List', tree_type=TreeType.List_Camera,server_ip=group.get_property('server_ip'))
                group.change_model.connect(self.update_group_model)
                root_item = self.add_group_item(group_model=group,server_ip =server.name)
                if standard_item is not None:
                    standard_item.appendRow(root_item)
                break

    def delete_group_model(self, group_list: List[GroupModel] = []):
        server = None
        for group_model in group_list:
            if server is None:
                server = self.get_server(server_ip = group_model.get_property('server_ip'))
            for standard_item in group_model.list_standard_item:
                try:
                    self.model.removeRow(standard_item.row(), standard_item.parent().index())
                except Exception as e:
                    logger.debug(f'except delete_group_model = {e}')
        
        # Dọn dẹp danh sách translate items sau khi xóa
        self.cleanup_translate_items()

    def delete_camera_model(self, camera_list: List[CameraModel] = []):
        server = None
        for camera_model in camera_list:
            # Xóa camera trên TreeView
            if server is None:
                server = self.get_server(server_ip = camera_model.get_property('server_ip'))
            for standard_item in camera_model.list_standard_item:
                try:
                    logger.debug(f'delete_camera_model = {standard_item.text()}')
                    self.model.removeRow(standard_item.row(), standard_item.parent().index())
                except Exception as e:
                    logger.debug(f'except delete_camera_model = {e}')
            self.delete_camera(camera_model=camera_model, server = server)
            # Xóa ShortcutID trong shortcut_key_model_manager
            controller:Controller = controller_manager.get_controller(server_ip = camera_model.get_property('server_ip'))
            id = shortcut_key_model_manager.delete_shortcutkey_model(model_id=camera_model.get_property('id'))
            if id:
                controller.delete_shortcut_key(parent=self,ids = id)
        
        # Dọn dẹp danh sách translate items sau khi xóa
        self.cleanup_translate_items()

    def add_event_signal(self, id):
        event: EventModel = event_manager.get_event(id=id)
        camera_model = camera_model_manager.get_camera_model(id = event.get_property("cameraId"))
        if camera_model is not None:
            self.change_item_color(camera_model.get_property('name'), QColor(236, 122, 26))
            QTimer.singleShot(7000, lambda: self.change_item_color(camera_model.get_property('name'), QColor(f"{main_controller.get_theme_attribute('Color', 'main_background')}")))

    ############ Camera Group ############################################
    def exit_controller_signal(self,data):
        controller:Controller = data
        for server in self.tree_data.servers:
            if server.name == controller.server.data.server_ip:
                standard_item = self.model.invisibleRootItem()
                for i in range(standard_item.rowCount()):
                    child_item = standard_item.child(i, 0)
                    text = child_item.text()
                    if text == server.name:
                        standard_item.removeRow(child_item.row())
                        break
                self.tree_data.servers.remove(server)
                break

    def camera_open_in_tab(self, data):
        model, tab_name, row_col, item_standard, list_camera_selection = data
        row = row_col[0]
        col = row_col[1]
        new_data = (model, tab_name, row, col, item_standard, list_camera_selection)
        main_controller.open_camera_in_tab_signal.emit(new_data)

    def floor_open_in_tab(self, data):
        model, tab_name, row_col, item_standard, server_ip = data
        row = row_col[0]
        col = row_col[1]
        new_data = (model, tab_name, row, col, item_standard, server_ip)
        main_controller.open_floor_in_tab_signal.emit(new_data)

    def map_open_in_tab(self, data):
        model, tab_name, row_col, item_standard, list_camera_selection, server_ip = data
        row = row_col[0]
        col = row_col[1]
        new_data = (model, tab_name, row, col, item_standard, list_camera_selection, server_ip)
        main_controller.open_map_in_tab_signal.emit(new_data)

    ################################################################
    def auto_open_virtual_window(self, item: QStandardItem):
        ######## check co virtual window nao dang mo ##########
        list_screen_index = {}
        for server in self.tree_data.servers:
            for base_item in server.list_virtual_windows.child_item:
                logger.info(f'auto_open_virtual_window = {main_controller.list_parent}')
                if base_item.name in main_controller.list_parent:
                    index = main_controller.list_parent[base_item.model.data.id][0]
                    list_screen_index[index] = base_item.name
        ############## check man hinh #######################
        current_screen = self.screen()
        screens = QApplication.screens()
        screen_index_selected = None
        if len(screens) > 1:
            for screen in screens:
                screen_index = QApplication.screens().index(screen)
                if screen != current_screen:
                    if screen_index not in list_screen_index:
                        screen_index_selected = screen_index
                else:
                    pass
            ########################################################
            if screen_index_selected is not None:
                return self.open_to_window_triggered(screen_index_selected, item)
            else:
                for screen in screens:
                    screen_index = QApplication.screens().index(screen)
                    if screen != current_screen:
                        screen_index_selected = screen_index
                        pre_notify = self.tr("This screen already contains a virtual window named")
                        suf_notify = self.tr("Do you want to replace this virtual window?")
                        # notify = f"This screen already contains a virtual window named '{list_screen_index[screen_index_selected]}'. Do you want to replace this virtual window"
                        dialog = WarningDialog(
                            warning_message=f"{pre_notify} '{list_screen_index[screen_index_selected]}' {suf_notify}")
                        result = dialog.exec()
                        if result == QDialog.Accepted:
                            return self.open_to_window_triggered(screen_index_selected, item)
                        elif result == QDialog.Rejected:
                            pass

                        break

        else:
            return self.open_to_window_triggered(0, item)

    def on_tree_view_doubleclicked(self, index):
        item = self.model.itemFromIndex(index)
        if item is None:
            return
        item_data = item.data(Qt.UserRole)
        server = self.get_server(server_ip=item.server_ip)
        mouse_event = QApplication.instance().mouseButtons()
        if mouse_event == Qt.MouseButton.LeftButton:
            if item_data == TreeType.Virtual_Window_Item:
                ######## check co virtual window nao dang mo ##########
                list_screen_index = {}
                for virtual_window_name in server.list_virtual_windows.child_item:
                    if virtual_window_name.name in main_controller.list_parent:
                        index = main_controller.list_parent[virtual_window_name.name][0]
                        list_screen_index[index] = virtual_window_name.name
                ############## check man hinh #######################
                current_screen = self.screen()
                screens = QApplication.screens()
                screen_index_selected = None
                if len(screens) > 1:
                    for screen in screens:
                        screen_index = QApplication.screens().index(screen)
                        if screen != current_screen:
                            if screen_index not in list_screen_index:
                                screen_index_selected = screen_index
                        else:
                            pass

                    ########################################################
                    if screen_index_selected is not None:
                        self.open_to_window_triggered(screen_index_selected, item)
                    else:
                        for screen in screens:
                            screen_index = QApplication.screens().index(screen)
                            if screen != current_screen:
                                screen_index_selected = screen_index
                                pre_notify = self.tr("This screen already contains a virtual window named")
                                suf_notify = self.tr("Do you want to replace this virtual window?")
                                # notify = f"This screen already contains a virtual window named '{list_screen_index[screen_index_selected]}'. Do you want to replace this virtual window"
                                dialog = WarningDialog(
                                    warning_message=f"{pre_notify} '{list_screen_index[screen_index_selected]}' {suf_notify}")
                                result = dialog.exec()
                                if result == QDialog.Accepted:
                                    self.open_to_window_triggered(screen_index_selected, item)
                                elif result == QDialog.Rejected:
                                    pass
                                break
                else:
                    self.open_to_window_triggered(0, item)
            elif item_data == TreeType.Saved_View_Item:
                self.open_saved_view_triggered(item)

    def on_tree_view_entered(self, index):
        item_data = self.model.data(index, Qt.UserRole)
        if item_data == TreeType.Group or item_data == TreeType.Camera:
            global_pos = QCursor.pos()
            tooltip_text = index.data(Qt.DisplayRole)
            QToolTip.showText(global_pos, tooltip_text, self.tree_view)

    def on_tree_view_pressed(self, index):
        pass

    def on_tree_view_clicked(self, index):
        item = self.model.itemFromIndex(index)

        # Tối ưu hóa: chỉ xử lý khi không phải multi-select để tránh lag
        selected_indexes = self.tree_view.selectedIndexes()
        if len(selected_indexes) > 1:
            # Đang trong multi-select mode, skip heavy operations
            return

        # if item.item_model is not None:
        #     # logger.info(f'on_tree_view_clicked = {item.item_model.to_dict()}')
        #     if isinstance(item.item_model,MapModel):
        #         controller:Controller = controller_manager.get_controller(server_ip=item.item_model.serverIp)
        #         response = controller.api_client.get_map()
        #         logger.info(f'MapModel = {response.json()}')
        #         logger.info(f'MapModel1 = {item.item_model.to_dict()}')
        #     if isinstance(item.item_model,BuildingModel):
        #         controller:Controller = controller_manager.get_controller(server_ip=item.item_model.serverIp)
        #         response = controller.api_client.get_buildings(ids = item.item_model.id)
        #         logger.info(f'BuildingModel = {response.json()}')
        #         logger.info(f'BuildingModel1 = {item.item_model.to_dict()}')
        #     if isinstance(item.item_model,FloorModel):
        #         controller:Controller = controller_manager.get_controller(server_ip=item.item_model.serverIp)
        #         response = controller.api_client.get_floors(ids = item.item_model.id)
        #         logger.info(f'FloorModel = {response.json()}')
        #         logger.info(f'FloorModel1 = {item.item_model.to_dict()}')
        # if item.item_model is not None and isinstance(item.item_model,CameraModel):
        #     controller:Controller = controller_manager.get_controller(server_ip=item.item_model.get_property('server_ip'))
        #     response = controller.api_client.get_camera1(ids = item.item_model.get_property('id'))
        #     logger.debug(f'CameraModel = {response.json()}')
        #     logger.debug(f'CameraModel1 = {item.item_model.data}')
        #     main_controller.camera_clicked_signal.emit(item.item_model.id, item.item_model.name)

    def update_group_model(self, data):
        # sử dụng khi người dùng  thêm/xóa camera ở API Update_Group cần cập nhật lại camera trong Group item Treeview
        key, value, group_model = data
        if key == 'cameraIds':
            list_id = {}
            for standard_item in group_model.list_standard_item:
                try:
                    # xóa những camera không nằm trong group này nữa
                    for child_index in range(standard_item.rowCount()):
                        child_item = standard_item.child(child_index)
                        if isinstance(child_item.item_model,CameraModel):
                            list_id[child_item.item_model.get_property('id')] = child_item

                    for id,item in list_id.items():
                        if id not in value:
                            item.item_model.unregister_standard_item(item)
                            self.model.removeRow(item.row(), item.parent().index())
                    # them camera moi vao group
                    for id in value:
                        if id not in list_id:
                            camera_model = camera_model_manager.get_camera_model(id = id)
                            if camera_model is not None:
                                self.add_camera_item(item=standard_item, camera_model=camera_model,server_ip=group_model.get_property('server_ip'))
                except Exception as e:
                    logger.debug(f'Exception update_group_model = {e}')
        elif key == 'childGroupIds':
            logger.debug(f'update_group_model childGroupIds= {group_model.list_standard_item}')
            list_id = {}
            for standard_item in group_model.list_standard_item:
                # try:
                for child_index in range(standard_item.rowCount()):
                    child_item = standard_item.child(child_index)
                    if isinstance(child_item.item_model,GroupModel):
                        list_id[child_item.item_model.get_property('id')] = child_item
                for id in list_id:
                    if id not in value:
                        list_id[id].item_model.unregister_standard_item(list_id[id])
                        self.model.removeRow(list_id[id].row(), list_id[id].parent().index())
                for id in value:
                    if id not in list_id:
                        aibox_model = group_model_manager.get_group_model(id = id)
                        if aibox_model is not None:
                            self.add_aibox_item(item = standard_item,aibox_model=aibox_model,server_ip=group_model.get_property('server_ip'))

    def update_camera_model(self, data):
        # sử dụng khi người dùng  thêm/xóa camera ở API Update_Group cần cập nhật lại camera item không có trong Group nào
        key, value, camera_model = data
        server = self.get_server(server_ip = camera_model.get_property('server_ip'))
        if key == 'cameraGroupIds':
            logger.debug(f'update_camera_model = {data}')
            standard_item = self.get_item('Camera List', tree_type=TreeType.List_Camera,server_ip=camera_model.get_property('server_ip'))
            if len(value) == 0:
                self.add_camera(camera_model=camera_model, server=server)
                self.add_camera_item(item=standard_item, camera_model=camera_model,server_ip=camera_model.get_property('server_ip'))
            else:
                for child_index in range(standard_item.rowCount()):
                    child_item = standard_item.child(child_index)
                    if camera_model == child_item.item_model:
                        self.delete_camera(camera_model=camera_model, server = server)
                        self.model.removeRow(child_item.row(), child_item.parent().index())
                        break

    def add_group_item(self, group_model: GroupModel = None, is_tracking_group=False,server_ip = None):
        group_item = self.add_item(name=group_model.get_property('name'),tree_type=TreeType.Group, model=group_model, is_tracking_item=is_tracking_group,server_ip =server_ip)
        if group_model.get_property("type") == 'AI_BOX':
            if group_model.get_property("cameraIds") is not None and len(group_model.get_property("cameraIds")) > 0:
                camera_list = camera_model_manager.get_camera_list(server_ip=server_ip)
                for id,camera_model in camera_list.items():
                    if camera_model.get_property('id') in group_model.get_property("cameraIds"):
                        self.add_camera_item(item=group_item, camera_model=camera_model,server_ip = server_ip)
        else:
            if group_model.get_property("cameraIds") is not None and len(group_model.get_property("cameraIds")) > 0:
                camera_list = camera_model_manager.get_camera_list(server_ip=server_ip)
                for id,camera_model in camera_list.items():
                    if camera_model.get_property('id') in group_model.get_property("cameraIds"):
                        self.add_camera_item(item=group_item, camera_model=camera_model,server_ip = server_ip)
            if group_model.get_property("childGroupIds") is not None and len(group_model.get_property("childGroupIds")) > 0:
                for id in group_model.get_property("childGroupIds"):
                    aibox = group_model_manager.get_group_model(id = id)
                    if aibox is not None:
                        self.add_aibox_item(item = group_item,aibox_model=aibox,server_ip=server_ip)
        return group_item

    def add_building_item(self, building_model: BuildingModel = None, server_ip = None):
        building_item = self.add_item(name=building_model.name,tree_type=TreeType.BuildingItem, model=building_model, server_ip =server_ip)
        for item in building_model._floorIds:
            floor_item = self.add_item(name=item.name,tree_type=TreeType.FloorItem, model=item, server_ip =server_ip)
            building_item.appendRow(floor_item)
        return building_item

    # def add_camera_on_floor_item(self, floor_model: FloorModel = None, server_ip = None):
    #     floor_item = self.add_item(name=floor_model.name,tree_type=TreeType.FloorItem, model=floor_model, server_ip =server_ip)
    #     for item in floor_model.get_camera_models():
    #         camera_item = self.add_item(name=item["name"], tree_type=TreeType.Camera_On_Floor_Item, model=item, server_ip=server_ip)
    #         floor_item.appendRow(camera_item)
    #     return floor_item

    def add_aibox_item(self, item: CustomStandardItem = None, aibox_model: CameraModel = None,server_ip = None):
        if item is not None:
            aibox_item = self.add_item(item=item, tree_type=TreeType.Group, name=aibox_model.get_property('name'),
                                        model=aibox_model,server_ip = server_ip)
            if aibox_model.get_property("cameraIds") is not None and len(aibox_model.get_property("cameraIds")) > 0:
                camera_list = camera_model_manager.get_camera_list(server_ip=server_ip)
                for id,camera_model in camera_list.items():
                    if camera_model.get_property('id') in aibox_model.get_property("cameraIds"):
                        self.add_camera_item(item=aibox_item, camera_model=camera_model,server_ip = server_ip)

    def add_camera_item(self, item: CustomStandardItem = None, camera_model: CameraModel = None,server_ip = None):
        if item is not None:
            camera_item = self.add_item(item=item, tree_type=TreeType.Camera, name=camera_model.get_property('name'),
                                        model=camera_model,server_ip = server_ip)
        else:
            camera_item = self.add_item(tree_type=TreeType.Camera, name=camera_model.get_property('name'), model=camera_model,server_ip = server_ip)
        return camera_item

    def create_treeview_model(self, filter: TreeType = TreeType.Server, is_search=False):
        model = CustomStandardItemModel()
        root_tree_view = model.invisibleRootItem()
        if not self.first_create_treeview_model:
            if filter == TreeType.Server or filter == TreeType.Invalid:
                if self.tree_filter_mode:
                    for server in self.tree_filter_mode.servers:
                        root_item = self.add_item(root_tree_view, server.name, tree_type=filter,server_ip = server.name)
                        if server.list_cameras:
                            root_item1 = self.add_item(root_item, server.list_cameras.name,
                                                       tree_type=TreeType.List_Camera,server_ip = server.name)
                            if server.list_cameras.list_groups:
                                for group in server.list_cameras.list_groups.values():
                                    root_item2 = self.add_group_item(group_model=group,server_ip =server.name)
                                    root_item1.appendRow(root_item2)

                            if server.list_cameras.list_aibox_non_group:
                                for group in server.list_cameras.list_aibox_non_group:
                                    root_item2 = self.add_group_item(group_model=group,server_ip =server.name)
                                    root_item1.appendRow(root_item2)

                            if server.list_cameras.list_camera_non_group:
                                for camera in server.list_cameras.list_camera_non_group:
                                    root_item2 = self.add_camera_item(server_ip =server.name, camera_model=camera)
                                    root_item1.appendRow(root_item2)

                        if server.list_dynamic_cameras:
                            for external_server in server.list_dynamic_cameras:
                                root_item1 = self.add_item(root_item, external_server.name,
                                                        tree_type=TreeType.List_Camera,server_ip = server.name)
                                if external_server.list_groups:
                                    for group in external_server.list_groups.values():
                                        root_item2 = self.add_group_item(group_model=group,server_ip =server.name)
                                        root_item1.appendRow(root_item2)

                        if server.list_virtual_windows:
                            root_item1 = self.add_item(root_item, server.list_virtual_windows.name,
                                                       tree_type=TreeType.List_Virtual_Window,server_ip = server.name)
                            if server.list_virtual_windows.child_item:
                                for base_item in server.list_virtual_windows.child_item:
                                    root_item2 = self.add_item(root_item1, base_item.name,
                                                               tree_type=TreeType.Virtual_Window_Item,server_ip = server.name,model=base_item.model)
                        if server.list_saved_views:
                            root_item1 = self.add_item(root_item, server.list_saved_views.name,
                                                       tree_type=TreeType.List_Saved_View,server_ip = server.name)
                            if server.list_saved_views.child_item:
                                for base_item in server.list_saved_views.child_item:
                                    root_item2 = self.add_item(root_item1, base_item.name,
                                                               tree_type=TreeType.Saved_View_Item,server_ip = server.name,model=base_item.model)
                        if server.list_maps:
                            map_model = map_manager.get_map_model(serverIp=server.name)
                            root_item1 = self.add_item(root_item, server.list_maps.name, tree_type=TreeType.List_Map,server_ip = server.name,model=map_model)
                            if server.list_maps.list_buildings:
                                for building_model in server.list_maps.list_buildings.values():
                                    root_item2 = self.add_building_item(building_model=building_model,server_ip=server.name)
                                    root_item1.appendRow(root_item2)
                            # if server.list_maps.list_cameras:
                            #     for cameraModel in server.list_maps.list_cameras.values():
                            #         self.add_item(item=root_item1, name=cameraModel.name,tree_type=TreeType.CameraOnMap, model=cameraModel, server_ip = server.name)

                self.first_create_treeview_model = True
            elif filter == TreeType.List_Camera:
                if self.tree_filter_mode:
                    for server in self.tree_filter_mode.servers:
                        root_item = self.add_item(root_tree_view, server.name, tree_type=filter,server_ip = server.name)
                        if server.list_cameras:
                            root_item1 = self.add_item(root_item, server.list_cameras.name,
                                                       tree_type=TreeType.List_Camera,server_ip = server.name)
                            if server.list_cameras.list_groups:
                                for group in server.list_cameras.list_groups.values():
                                    root_item2 = self.add_group_item(group_model=group,server_ip =server.name)
                                    root_item1.appendRow(root_item2)

                            if server.list_cameras.list_aibox_non_group:
                                for group in server.list_cameras.list_aibox_non_group:
                                    root_item2 = self.add_group_item(group_model=group,server_ip =server.name)
                                    root_item1.appendRow(root_item2)

                            if server.list_cameras.list_camera_non_group:
                                for camera in server.list_cameras.list_camera_non_group:
                                    root_item2 = self.add_camera_item(server_ip =server.name, camera_model=camera)
                                    root_item1.appendRow(root_item2)

                        if server.list_dynamic_cameras:
                            for external_server in server.list_dynamic_cameras:
                                root_item1 = self.add_item(root_item, external_server.name,
                                                        tree_type=TreeType.List_Camera,server_ip = server.name)
                                if external_server.list_groups:
                                    for group in external_server.list_groups.values():
                                        root_item2 = self.add_group_item(group_model=group,server_ip =server.name)
                                        root_item1.appendRow(root_item2)
                return model
            elif filter == TreeType.List_Virtual_Window:
                if self.tree_filter_mode:
                    for server in self.tree_filter_mode.servers:
                        root_item = self.add_item(root_tree_view, server.name, tree_type=filter,server_ip = server.name)
                        if server.list_virtual_windows:
                            root_item1 = self.add_item(root_item, server.list_virtual_windows.name,
                                                       tree_type=TreeType.List_Virtual_Window,server_ip = server.name)
                            if server.list_virtual_windows.child_item:
                                for base_item in server.list_virtual_windows.child_item:
                                    root_item2 = self.add_item(root_item1, base_item.name,
                                                               tree_type=TreeType.Virtual_Window_Item,server_ip = server.name,model=base_item.model)
                return model
            elif filter == TreeType.List_Saved_View:
                if self.tree_filter_mode:
                    for server in self.tree_filter_mode.servers:
                        root_item = self.add_item(root_tree_view, server.name, tree_type=filter,server_ip = server.name)
                        if server.list_saved_views:
                            root_item1 = self.add_item(root_item, server.list_saved_views.name,
                                                       tree_type=TreeType.List_Saved_View,server_ip = server.name)
                            if server.list_saved_views.child_item:
                                for base_item in server.list_saved_views.child_item:
                                    root_item2 = self.add_item(root_item1, base_item.name,
                                                               tree_type=TreeType.Saved_View_Item,server_ip = server.name,model=base_item.model)
                return model
            return model
        else:
            if filter == TreeType.Server or filter == TreeType.Invalid:
                if self.tree_filter_mode:
                    for server in self.tree_filter_mode.servers:
                        root_item = self.add_item(root_tree_view, server.name, tree_type=filter,server_ip = server.name)
                        if server.list_cameras:
                            root_item1 = self.add_item(root_item, server.list_cameras.name,
                                                       tree_type=TreeType.List_Camera,server_ip = server.name)
                            if server.list_cameras.list_groups:
                                for group in server.list_cameras.list_groups.values():
                                    root_item2 = self.add_group_item(group_model=group,server_ip =server.name)
                                    root_item1.appendRow(root_item2)

                            if server.list_cameras.list_aibox_non_group:
                                for group in server.list_cameras.list_aibox_non_group:
                                    root_item2 = self.add_group_item(group_model=group,server_ip =server.name)
                                    root_item1.appendRow(root_item2)

                            if server.list_cameras.list_camera_non_group:
                                for camera in server.list_cameras.list_camera_non_group:
                                    root_item2 = self.add_camera_item(server_ip =server.name,camera_model=camera)
                                    root_item1.appendRow(root_item2)

                        if server.list_dynamic_cameras:
                            for external_server in server.list_dynamic_cameras:
                                root_item1 = self.add_item(root_item, external_server.name,
                                                        tree_type=TreeType.List_Camera,server_ip = server.name)
                                if external_server.list_groups:
                                    for group in external_server.list_groups.values():
                                        root_item2 = self.add_group_item(group_model=group,server_ip =server.name)
                                        root_item1.appendRow(root_item2)

                        if server.list_virtual_windows:
                            root_item1 = self.add_item(root_item, server.list_virtual_windows.name,
                                                       tree_type=TreeType.List_Virtual_Window,server_ip = server.name)
                            if server.list_virtual_windows.child_item:
                                for base_item in server.list_virtual_windows.child_item:
                                    root_item2 = self.add_item(root_item1, base_item.name,
                                                               tree_type=TreeType.Virtual_Window_Item,server_ip = server.name,model = base_item.model)
                        if server.list_saved_views:
                            root_item1 = self.add_item(root_item, server.list_saved_views.name,
                                                       tree_type=TreeType.List_Saved_View,server_ip = server.name)
                            if server.list_saved_views.child_item:
                                for base_item in server.list_saved_views.child_item:
                                    root_item2 = self.add_item(root_item1, base_item.name,
                                                               tree_type=TreeType.Saved_View_Item,server_ip = server.name,model=base_item.model)

                        if server.list_maps:
                            map_model = map_manager.get_map_model(serverIp=server.name)
                            root_item1 = self.add_item(root_item, server.list_maps.name, tree_type=TreeType.List_Map,server_ip = server.name,model=map_model)
                            if server.list_maps.list_buildings:
                                for building_model in server.list_maps.list_buildings.values():
                                    # root_item2 = self.add_item(root_item1, building_model.data.name,
                                    #                            tree_type=TreeType.BuildingItem,server_ip = server.name,model=building_model)
                                    root_item2 = self.add_building_item(building_model=building_model,server_ip=server.name)
                                    root_item1.appendRow(root_item2)
                            # if server.list_maps.list_cameras:
                            #     for cameraModel in server.list_maps.list_cameras.values():
                            #         self.add_item(name=cameraModel.name,tree_type=TreeType.CameraOnMap, model=cameraModel, server_ip = server.name)
                return model
            elif filter == TreeType.List_Camera:
                if self.tree_filter_mode:
                    for server in self.tree_filter_mode.servers:
                        root_item = self.add_item(root_tree_view, server.name, tree_type=filter,server_ip = server.name)
                        if server.list_cameras:
                            root_item1 = self.add_item(root_item, server.list_cameras.name,
                                                       tree_type=TreeType.List_Camera,server_ip = server.name)
                            if server.list_cameras.list_groups:
                                for group in server.list_cameras.list_groups.values():
                                    root_item2 = self.add_group_item(group_model=group,server_ip =server.name)
                                    root_item1.appendRow(root_item2)

                            if server.list_cameras.list_aibox_non_group:
                                for group in server.list_cameras.list_aibox_non_group:
                                    root_item2 = self.add_group_item(group_model=group,server_ip =server.name)
                                    root_item1.appendRow(root_item2)

                            if server.list_cameras.list_camera_non_group:
                                for camera in server.list_cameras.list_camera_non_group:
                                    root_item2 = self.add_camera_item(server_ip =server.name,camera_model=camera)
                                    root_item1.appendRow(root_item2)
                        if server.list_dynamic_cameras:
                            for external_server in server.list_dynamic_cameras:
                                root_item1 = self.add_item(root_item, external_server.name,
                                                        tree_type=TreeType.List_Camera,server_ip = server.name)
                                if external_server.list_groups:
                                    for group in external_server.list_groups.values():
                                        root_item2 = self.add_group_item(group_model=group,server_ip =server.name)
                                        root_item1.appendRow(root_item2)

                        if server.list_virtual_windows:
                            root_item1 = self.add_item(root_item, server.list_virtual_windows.name,
                                                       tree_type=TreeType.List_Virtual_Window,server_ip = server.name)
                            if server.list_virtual_windows.child_item:
                                for base_item in server.list_virtual_windows.child_item:
                                    root_item2 = self.add_item(root_item1, base_item.name,
                                                               tree_type=TreeType.Virtual_Window_Item,server_ip = server.name,model = base_item.model)
                        if server.list_saved_views:
                            root_item1 = self.add_item(root_item, server.list_saved_views.name,
                                                       tree_type=TreeType.List_Saved_View,server_ip = server.name)
                            if server.list_saved_views.child_item:
                                for base_item in server.list_saved_views.child_item:
                                    root_item2 = self.add_item(root_item1, base_item.name,
                                                               tree_type=TreeType.Saved_View_Item,server_ip = server.name,model=base_item.model)
                        # if server.list_maps:
                        #     root_item1 = self.add_item(root_item, server.list_maps.name, tree_type=TreeType.List_Map,server_ip = server.name)
                        #     if server.list_maps.list_buildings:
                        #         for base_item in server.list_maps.list_buildings:
                        #             root_item2 = self.add_item(root_item1, base_item.name,
                        #                                        tree_type=TreeType.FloorItem,server_ip = server.name,model=base_item.model)
                return model
            elif filter == TreeType.List_Virtual_Window:
                if self.tree_filter_mode:
                    for server in self.tree_filter_mode.servers:
                        root_item = self.add_item(root_tree_view, server.name, tree_type=filter,server_ip = server.name)
                        if server.list_virtual_windows:
                            root_item1 = self.add_item(root_item, server.list_virtual_windows.name,
                                                       tree_type=TreeType.List_Virtual_Window,server_ip = server.name)
                            if server.list_virtual_windows.child_item:
                                for base_item in server.list_virtual_windows.child_item:
                                    root_item2 = self.add_item(root_item1, base_item.name,
                                                               tree_type=TreeType.Virtual_Window_Item,server_ip = server.name,model = base_item.model)
                return model
            elif filter == TreeType.List_Saved_View:
                if self.tree_filter_mode:
                    for server in self.tree_filter_mode.servers:
                        root_item = self.add_item(root_tree_view, server.name, tree_type=filter,server_ip = server.name)
                        if server.list_saved_views:
                            root_item1 = self.add_item(root_item, server.list_saved_views.name,
                                                       tree_type=TreeType.List_Saved_View,server_ip = server.name)
                            if server.list_saved_views.child_item:
                                for base_item in server.list_saved_views.child_item:
                                    root_item2 = self.add_item(root_item1, base_item.name,
                                                               tree_type=TreeType.Saved_View_Item,server_ip = server.name,model=base_item.model)
                return model

            return model
        
        # Xây dựng lại danh sách translate items sau khi tạo model
        self.rebuild_translate_items()
        return model
    
    def safe_retranslate_ui(self):
        """Gọi retranslateUi một cách an toàn, xử lý lỗi nếu có"""
        try:
            self.retranslateUi()
        except Exception as e:
            logger.warning(f"Lỗi khi gọi retranslateUi: {e}")
            # Thử dọn dẹp và gọi lại
            try:
                self.cleanup_translate_items()
                self.retranslateUi()
            except Exception as e2:
                logger.error(f"Vẫn lỗi sau khi dọn dẹp: {e2}")
                # Nếu vẫn lỗi, xây dựng lại hoàn toàn
                try:
                    self.rebuild_translate_items()
                    self.retranslateUi()
                except Exception as e3:
                    logger.error(f"Không thể khôi phục retranslateUi: {e3}")

    def update_treeview_ui(self, data=True):
        self.set_model(self.model)
        # Cập nhật danh sách translate items khi model thay đổi
        self.rebuild_translate_items()
        
        for i in range(self.root_tree_view.rowCount()):
            item_child = self.root_tree_view.child(i, 0)
            item_data = item_child.data(Qt.UserRole)
            if item_data == TreeType.Server or item_data == TreeType.List_Camera or self.filter_mode_status != TreeType.Server or self.search_text_status:
                self.tree_view.expand(self.model.indexFromItem(item_child))
            for j in range(item_child.rowCount()):
                item_child1 = item_child.child(j, 0)
                item_data = item_child1.data(Qt.UserRole)
                if item_data == TreeType.List_Camera or self.filter_mode_status != TreeType.Server or self.search_text_status:
                    self.tree_view.expand(self.model.indexFromItem(item_child1))
                for k in range(item_child1.rowCount()):
                    item_child2 = item_child1.child(k, 0)
                    if item_child2 is not None:
                        item_data = item_child2.data(Qt.UserRole)
                        if self.filter_mode_status != TreeType.Server or self.search_text_status:
                            self.tree_view.expand(self.model.indexFromItem(item_child2))

    def update_treeview_data(self,server_ip=None):
        group_list = group_model_manager.get_group_list(server_ip=server_ip)
        camera_list = camera_model_manager.get_camera_list(server_ip=server_ip)
        building_list = building_manager.getBuildingList(serverIp=server_ip)
        group_normal_list = {}
        aibox_non_group_list = []
        camera_non_group_list = []

        camera_dynamic_list: dict[str, ListDynamicCamera] = {}
        for id,camera_model in camera_list.items():
            camera_model.change_model.connect(self.update_camera_model)
            if camera_model.get_property("cameraGroupIds") is None or len(camera_model.get_property("cameraGroupIds")) == 0:
                camera_non_group_list.append(camera_model)

        list_aibox_id = []
        for id, group_model in group_list.items():
            group_model.change_model.connect(self.update_group_model)
            if group_model.get_property("type") == 'AI_BOX':
                if group_model.get_property('id') not in list_aibox_id:
                    aibox_non_group_list.append(group_model)
            elif group_model.get_property("type") == 'THIRD_PARTY_SERVER':
                if group_model.get_property("clientName") in camera_dynamic_list:
                    camera_dynamic = camera_dynamic_list[group_model.get_property("clientName")]
                    if group_model.get_property('id') not in camera_dynamic.list_groups:
                        camera_dynamic.list_groups[group_model.get_property('id')] = group_model
                else:
                    group_other_server = {}
                    group_other_server[id] = group_model
                    camera_dynamic = ListDynamicCamera(type=group_model.get_property("serverType"), name=group_model.get_property("clientName"), list_groups=group_other_server)
                    camera_dynamic_list[group_model.get_property("clientName")] = camera_dynamic
            else:
                group_normal_list[id] = group_model
                if group_model.get_property("childGroupIds") is not None and len(group_model.get_property("childGroupIds")) > 0:
                    list_aibox_id = list_aibox_id + group_model.get_property("childGroupIds")

        list_virtual_item = []
        for id, gridModel in gridManager.data.items():
            if gridModel.get_property("type") == CommonEnum.TabType.VIRTUALWINDOW and gridModel.get_property('server_ip') == server_ip:
                list_virtual_item.append(BaseItem(name=gridModel.get_property('name'),type=TreeType.Virtual_Window_Item,model=gridModel))
        list_savedview_item = []
        for id, gridModel in gridManager.data.items():
            if gridModel.get_property("type") == CommonEnum.TabType.SAVEDVIEW and gridModel.get_property('server_ip') == server_ip:
                list_savedview_item.append(BaseItem(name=gridModel.get_property('name'),type=TreeType.Saved_View_Item,model=gridModel))

        list_buildings_item = {}
    
        for i, map_model in map_manager.data.items():
            if map_model.serverIp == server_ip:
                list_building = map_model.getbuildingIds()
                for item in list_building:
                    list_buildings_item[item.id] = item

        list_cameras_item = {}
        for i, map_model in map_manager.data.items():
            if map_model.serverIp == server_ip:
                list_cameras = map_model.cameraIds.getItems()
                # map_model.removeCameraChanged.connect(self.removeCameraChanged)
                # map_model.addCameraChanged.connect(self.addCameraChanged)
                map_model.removeListCameraChanged.connect(self.removeListCameraChanged)
                for item in list_cameras:
                    list_cameras_item[item.id] = item

        list_cameras_map2d_item = {}
        for floor_server_ip, floor_models in floor_manager.data.items():
            if floor_server_ip == server_ip:
                for i, floor_model in floor_models.items():
                    list_cameras = floor_model.get_camera_models()
                    for item in list_cameras:
                        list_cameras_map2d_item[item["id"]] = item

        #dynamic camera
        if self.tree_data:
            for server in self.tree_data.servers:
                if server.name == server_ip:
                    if len(group_normal_list) > 0:
                        self.add_groups(group_normal_list, server=server)
                    if len(aibox_non_group_list) > 0:
                        self.add_aiboxs(aibox_non_group_list,server=server)
                    if len(camera_non_group_list) > 0:
                        self.add_cameras(camera_non_group_list, server=server)
                    if len(list_virtual_item) > 0:
                        self.add_base_items(list_base_items=list_virtual_item,server=server,tree_type=TreeType.List_Virtual_Window)
                    if len(list_savedview_item) > 0:
                        self.add_base_items(list_base_items=list_savedview_item,server=server,tree_type=TreeType.List_Saved_View)
                    # if len(list_map_item) > 0:
                    #     self.add_base_items(list_base_items=list_map_item,server=server,tree_type=TreeType.List_Map)
                    if len(list_buildings_item) > 0:
                        self.add_buildings(list_buildings_item, server=server)
                    if len(list_cameras_item) > 0:
                        self.add_camCfgs(list_cameras_item, server=server)
                    if len(list_cameras_map2d_item) > 0:
                        self.add_camOnFloor(list_cameras_map2d_item,server=server)

                    logger.debug(f'camera_dynamic_list = {camera_dynamic_list}')
                    if len(camera_dynamic_list) > 0:
                        for external_server in camera_dynamic_list.values():
                            logger.debug(f'external_server = {external_server} - server: {server}')
                            self.add_dynamic_groups(external_server, server=server)

    def removeListCameraChanged(self,serverIp,listCameras):
        list_map = self.get_item('Map List', tree_type=TreeType.List_Map,server_ip=serverIp)
        if list_map is not None:
            list_item = []
            for i in range(list_map.rowCount()):
                item_child = list_map.child(i, 0)
                if item_child.item_model in listCameras:
                    list_item.append(item_child)
            for item in list_item:
                if isinstance(item.item_model, CameraModel):
                    self.model.removeRow(item.row(), item.parent().index())

    def removeCameraChanged(self, cameraModel:CameraModel):
        list_map = self.get_item('Map List', tree_type=TreeType.List_Map,server_ip=cameraModel.serverIp)
        if list_map is not None:
            for i in range(list_map.rowCount()):
                item_child = list_map.child(i, 0)
                if cameraModel == item_child.item_model:
                    list_map.removeRow(i)
                    break

    # def addCameraChanged(self, cameraModel:CameraModel):
    #     logger.info(f'addCameraChanged')
    #     list_map = self.get_item('Map List', tree_type=TreeType.List_Map,server_ip=cameraModel.serverIp)
    #     if list_map is not None:
    #         self.add_item(item= list_map,name=cameraModel.name, tree_type=TreeType.CameraOnMap,model=cameraModel,server_ip=cameraModel.serverIp)

    def process_treeview_data(self):
        while not self.filter_queue.empty():
            msg = self.filter_queue.get()
            text = msg['text']
            status = msg['status']
            camera_model_manager.clear_list_standard_item()
            group_model_manager.clear_list_standard_item()
            if text != '':
                self.search_text_status = True
                if status == Status.Change_Filter_Mode:
                    self.tree_filter_mode = self.tree_data
                self.model = self.create_treeview_model(filter=self.filter_mode_status)
                unaccented_text = unidecode(text)
                self.filter_recursive(self.model.invisibleRootItem(), text=text, unaccented_text=unaccented_text)
                if self.model.rowCount() == 0 or not self.has_visible_items(self.model.invisibleRootItem()):
                    self.show_not_found_message()
                else:
                    self.hide_not_found_message()
            else:
                self.search_text_status = False
                if status == Status.Change_Filter_Mode:
                    self.tree_filter_mode = self.tree_data
                self.model = self.create_treeview_model(filter=self.filter_mode_status)
                self.hide_not_found_message()
            self.filter_queue.task_done()
        self.update_treeview_ui()

    def has_visible_items(self, item):
        if item.rowCount() == 0:
            return False
        for row in range(item.rowCount()):
            child = item.child(row)
            if child is not None:
                if child.rowCount() > 0:
                    if self.has_visible_items(child):
                        return True
                else:
                    return True
        return False

    def show_not_found_message(self):
        if not hasattr(self, 'not_found_container'):
            self.not_found_container = QWidget(self.tree_view)
            container_layout = QVBoxLayout(self.not_found_container)
            container_layout.setAlignment(Qt.AlignCenter)
            icon_label = QLabel()
            icon = QIcon(main_controller.get_theme_attribute('Image', 'search_not_found'))
            icon_label.setPixmap(icon.pixmap(QSize(48, 48))) # Adjust size as needed
            icon_label.setAlignment(Qt.AlignCenter)
            self.not_found_label = QLabel(self.tr("No search results"))
            self.not_found_label.setAlignment(Qt.AlignCenter)
            self.not_found_label.setStyleSheet(f"""
                QLabel {{
                    color: {main_controller.get_theme_attribute('Color', 'text_color_all_app')};
                    font-size: 14px;
                    background-color: transparent;
                }}
            """)
            container_layout.addWidget(icon_label)
            container_layout.addWidget(self.not_found_label)
            container_layout.setSpacing(8)
        rect = self.tree_view.viewport().rect()
        self.not_found_container.setGeometry(rect)
        self.not_found_container.show()

    def hide_not_found_message(self):
        if hasattr(self, 'not_found_container'):
            self.not_found_container.hide()

    def filter_recursive(self, item: QStandardItem, text: str, unaccented_text: str):
        try:
            for row in range(item.rowCount() - 1, -1, -1):
                child_item = item.child(row)
                pattern: re.Pattern = re.compile(text, re.IGNORECASE | re.UNICODE)
                if (
                        pattern.search(child_item.text())
                        or unaccented_text.lower() in unidecode(child_item.text()).lower()
                ):
                    self.filter_recursive(child_item, text, unaccented_text)
                else:
                    self.filter_recursive(child_item, text, unaccented_text)
                    if not self.item_has_matching_child(child_item, text, unaccented_text):
                        item.removeRow(row)
        except Exception as e:
            logger.critical(f"filter_recursive: {e}")

    def item_has_matching_child(
            self, item: QStandardItem, text: str, unaccented_text: str
    ):
        for row in range(item.rowCount()):
            child_item = item.child(row)
            pattern: re.Pattern = re.compile(text, re.IGNORECASE | re.UNICODE)
            if (
                    pattern.search(child_item.text())
                    or unaccented_text.lower() in unidecode(child_item.text()).lower()
            ):
                return True
            elif self.item_has_matching_child(child_item, text, unaccented_text):
                return True
        return False

################### Virtual Window ##################################
    def find_name_selected(self, tab_type=CommonEnum.TabType.VIRTUALWINDOW,server_ip = None):
        name_list = []
        if tab_type == CommonEnum.TabType.VIRTUALWINDOW:
            for id, gridModel in gridManager.data.items():
                if gridModel.get_property("type") == CommonEnum.TabType.VIRTUALWINDOW:
                    name_list.append(gridModel.get_property('name'))
            is_name_selected = False
            count = 0
            new_name = self.tr('Virtual Window ') + f'{count}'
            while not is_name_selected:
                new_name = self.tr('Virtual Window ') + f'{count}'
                if new_name not in name_list:
                    is_name_selected = True
                else:
                    count += 1
            return new_name
        else:
            for id, gridModel in gridManager.data.items():
                if gridModel.get_property("type") == CommonEnum.TabType.NORMAL or gridModel.get_property("type") == CommonEnum.TabType.SAVEDVIEW:
                    name_list.append(gridModel.get_property('name'))
            is_name_selected = False
            count = 0
            new_name = self.tr('View ') + f'{count}'
            while not is_name_selected:
                new_name = self.tr('View ') + f'{count}'
                if new_name not in name_list:
                    is_name_selected = True
                else:
                    count += 1
            return new_name

    def new_virtual_window_triggered(self, item: QStandardItem):
        server = self.get_server(server_ip = item.server_ip)
        controller:Controller = controller_manager.get_controller(server_ip = item.server_ip)
        new_name = self.find_name_selected(tab_type=CommonEnum.TabType.VIRTUALWINDOW,server_ip = item.server_ip)

        data = {
            # "id": None,
            "name": new_name,
            "type": CommonEnum.TabType.VIRTUALWINDOW,
            "isShow": False,
            "currentGrid": "{}",
            "listGridData": "{}",
            "listGridCustomData": "NewGrid",
            "direction": str({'id': str(uuid.uuid4())})
        }
        def callback(data):
            if data is not None:
                data["server_ip"] = item.server_ip
                data["type"] = int(data["type"])
                gridModel = GridModel(data=data)
                gridManager.addGridModel(data=gridModel)
                self.add_data(name=new_name, tab_type=TreeType.Virtual_Window_Item, server=server,model=gridModel)
                root_item = self.add_item(item=item, name=new_name, tree_type=TreeType.Virtual_Window_Item,server_ip = server.name,model=gridModel)
                index = root_item.index()
                self.tree_view.edit(index)
                return root_item
        subThread = SubThread(parent=self, target=controller.create_tabmodel, args=(data,),callback=callback)
        subThread.start()

    def add_shortcut_key_list_signal(self,data):
        start_key = int(Qt.Key.Key_Asterisk)
        if start_key in shortcut_key_model_manager.shortcut_key_list:
            shortcut_key_model_manager.shortcut_key_list[start_key]['func'] = self.process_shortcut_id

    def add_shortcut_key_signal(self,key):
        if key == int(Qt.Key.Key_Asterisk):
            shortcut_key_model_manager.shortcut_key_list[key]['func'] = self.process_shortcut_id

    def close_all_virtual_window_triggered(self, item: QStandardItem):
        for i in range(item.rowCount()):
            item_child = item.child(i, 0)
            self.close_virtual_window_triggered(item_child)

    def func_start_key(self):
        virtual_window_list = []
        for server in self.tree_filter_mode.servers:
            if server.list_virtual_windows is not None:
                if server.list_virtual_windows.child_item is not None:
                    for base_item in server.list_virtual_windows.child_item:
                        base_item.model.get_property("id")
                        if base_item.model.get_property("id") in main_controller.list_parent:
                            index = main_controller.list_parent[base_item.model.get_property("id")][0]
                            virtual_window_list.append(main_controller.list_parent[base_item.model.get_property("id")])
        return virtual_window_list

    def process_shortcut_id(self,id = None, tree_type = None):
        pass
        # tab_model = tab_model_manager.get_tab_model(id = id)
        # item = self.get_item(name=tab_model.get_property('name'),tree_type=tree_type,server_ip=tab_model.get_property('server_ip'))
        # logger.debug(f'process_shortcut_id = {tab_model} {item}')
        # if tree_type == TreeType.Virtual_Window_Item:
        #     ######## check co virtual window nao dang mo ##########
        #     list_screen_index = {}
        #     for server in self.tree_data.servers:
        #         for base_item in server.list_virtual_windows.child_item:
        #             if base_item.model.data.id in main_controller.list_parent:
        #                 index = main_controller.list_parent[base_item.model.data.id][0]
        #                 list_screen_index[index] = base_item.name
        #     ############## check man hinh #######################
        #     current_screen = self.screen()
        #     screens = QApplication.screens()
        #     screen_index_selected = None
        #     if len(screens) > 1:
        #         for screen in screens:
        #             screen_index = QApplication.screens().index(screen)
        #             if screen != current_screen:
        #                 if screen_index not in list_screen_index:
        #                     screen_index_selected = screen_index
        #             else:
        #                 pass
        #         ########################################################
        #         if screen_index_selected is not None:
        #             self.open_to_window_triggered(screen_index_selected,item)
        #         else:
        #             for screen in screens:
        #                 screen_index = QApplication.screens().index(screen)
        #                 if screen != current_screen:
        #                     screen_index_selected = screen_index
        #                     pre_notify = self.tr("This screen already contains a virtual window named")
        #                     suf_notify = self.tr("Do you want to replace this virtual window?")
        #                     dialog = WarningDialog(
        #                         warning_message=f"{pre_notify} '{list_screen_index[screen_index_selected]}' {suf_notify}")
        #                     result = dialog.exec()
        #                     if result == QDialog.Accepted:
        #                         self.open_to_window_triggered(screen_index_selected,item)
        #                     elif result == QDialog.Rejected:
        #                         pass
        #                     break
        #     else:
        #         self.open_to_window_triggered(0,item)
        # elif tree_type == TreeType.Saved_View_Item:
        #     self.open_saved_view_triggered(item)

    def shortcut_id_triggered(self, item: QStandardItem, tree_type=None):
        controller:Controller = controller_manager.get_controller(server_ip = item.server_ip)
        self.shortcut_id_dialog = ShortcutIDDialog(
        name='', ok_title='Save')
        result = self.shortcut_id_dialog.exec()
        if result == QDialog.Accepted:
            id = self.shortcut_id_dialog.shortcut_id.text()
            if id != '':
                id = int(id)
                if tree_type == TreeType.Virtual_Window_Item or tree_type == TreeType.Saved_View_Item:
                    if shortcut_key_model_manager.find_shortcut_id(start_key=int(Qt.Key.Key_Asterisk),id = id) is None:

                        shortcut_key = ShortCutKey(startKey=int(Qt.Key.Key_Asterisk),shortcutId=id,name=item.item_model.get_property('id'),treeType = tree_type)
                        shortcut_key_model = shortcut_key_model_manager.get_shortcutkey_model(start_key=int(Qt.Key.Key_Asterisk),name = item.item_model.get_property('id'))
                        logger.debug(f'shortcut_id_triggered2 id = {shortcut_key_model}')
                        if shortcut_key_model is None:
                            controller.create_shortcut_key(parent=self,shortcut_key=shortcut_key)
                        else:
                            shortcut_key_model_manager.delete_shortcutkey_model(shortcutkey_model = shortcut_key_model)
                            shortcut_key_model.data.shortcutId = id
                            shortcut_key_model.data.direction = {'id': str(uuid.uuid4())}
                            shortcut_key_model_manager.add_shortcut_key(shortcut_key = shortcut_key_model)
                            controller.update_shortcut_key_by_put(parent=self, shortcut_key=shortcut_key_model.data)
                    else:
                        Notifications(parent=main_controller.list_parent['CameraScreen'],
                                    title=self.tr('This Shortcut id already exists'), icon=Style.PrimaryImage.info_result)
                elif tree_type == TreeType.Camera:
                    if shortcut_key_model_manager.find_shortcut_id(start_key=int(Qt.Key.Key_Slash),id = id) is None:
                        shortcut_key = ShortCutKey(startKey=int(Qt.Key.Key_Slash),shortcutId=id,name=item.item_model.get_property('id'),treeType = tree_type)
                        shortcut_key_model = shortcut_key_model_manager.get_shortcutkey_model(start_key=int(Qt.Key.Key_Slash),name = item.item_model.get_property('id'))
                        if shortcut_key_model is None:
                            controller.create_shortcut_key(parent=self,shortcut_key=shortcut_key)
                        else:
                            shortcut_key_model_manager.delete_shortcutkey_model(shortcutkey_model = shortcut_key_model)
                            shortcut_key_model.data.shortcutId = id
                            shortcut_key_model.data.direction = {'id':str(uuid.uuid4())}
                            shortcut_key_model_manager.add_shortcut_key(shortcut_key = shortcut_key_model)
                            controller.update_shortcut_key_by_put(parent=self,shortcut_key=shortcut_key_model.data)
                    else:
                        Notifications(parent=main_controller.list_parent['CameraScreen'],
                                    title=self.tr('This Shortcut id already exists'), icon=Style.PrimaryImage.info_result)
            else:
                Notifications(parent=main_controller.list_parent['CameraScreen'],
                              title=self.tr('Please Enter Complete Information'), icon=Style.PrimaryImage.info_result)
            if main_controller.key_filter is not None:
                QApplication.instance().installEventFilter(main_controller.key_filter)
        elif result == QDialog.Rejected:
            logger.debug(f'cancel shortcut_id_triggered')

    def remove_all_virtual_window_triggered(self, item: QStandardItem):
        controller:Controller = controller_manager.get_controller(server_ip = item.server_ip)
        list_id = []
        for id, gridModel in gridManager.data.items():
            if gridModel.get_property("type") == CommonEnum.TabType.VIRTUALWINDOW:
                list_id.append(gridModel.get_property("id"))
        while item.rowCount() > 0:
            for i in range(item.rowCount()):
                item_child = item.child(i, 0)
                self.remove_virtual_window_triggered(item_child)
                break
        if len(list_id) > 0:
            controller.delete_tabmodel(parent=self,ids = list_id)

    def remove_virtual_window_triggered(self, item: QStandardItem,remove_all = False):
        server = self.get_server(server_ip = item.server_ip)
        controller:Controller = controller_manager.get_controller(server_ip = item.server_ip)
        gridModel = item.item_model
        if gridModel.get_property('id') in main_controller.list_parent:
            main_controller.list_parent[gridModel.get_property('id')][1].close()
        new_custom_tab_widget = main_controller.list_parent['CustomTabWidget']
        if gridModel.get_property("isShow"):
            for index in range(new_custom_tab_widget.tab_widget.count()):
                widget = new_custom_tab_widget.getWidgetByIndex(index)
                if widget.gridModel.get_property("id") == gridModel.get_property("id"):
                    new_custom_tab_widget.tab_widget.removeTab(index)
        gridManager.removeGridModel(gridModel)
        id = shortcut_key_model_manager.delete_shortcutkey_model(model_id=gridModel.get_property('id'))
        if id is not None:
            controller.delete_shortcut_key(parent=self,ids = id)
        if not remove_all:
            controller.delete_tabmodel(parent=self, ids = gridModel.get_property('id'))
        self.remove_data(id=gridModel.get_property('id'), tab_type=TreeType.Virtual_Window_Item, server=server)
        self.model.removeRow(item.row(), item.parent().index())

    def switch_to_window_triggered(self, screen_index, item: QStandardItem):
        gridModel = item.item_model
        if self.screen_index_virtual_window_opened(item) is None:
            # đây là trường hợp Virtual này chưa được mở màn hình nào, Cần check màn hình screen_index có đang mở virtual nào không, nếu có thì đóng lại và thay thế Virtual mới vào màn hình này
            ######## check co virtual window nao dang mo ##########
            self.clear_virtual_window(screen_index)
        elif self.screen_index_virtual_window_opened(item) == screen_index:
            # đây là trường hợp Virtual window này đã được mở tại màn này rồi
            # Khi người dùng switch thì tại thời điểm đó cần ưu tiên hiển thị Virtual lên trên các ứng dụng khác trên màn hình
            # virtual_camera_grid_widget: VirtualCameraGridWidget = main_controller.list_parent[tab_model.data.id][1]
            # virtual_camera_grid_widget.raise_()
            return
        else:

            # đây là trường hợp Virtual window này đã được mở màn hình khác, và người dùng có nhu cầu switch sang màn hình có index là screen_index
            ######## check co virtual window nao dang mo ##########
            self.clear_virtual_window(screen_index)
            virtual_camera_grid_widget: VirtualCameraGridWidget = main_controller.list_parent[gridModel.get_property("id",None)][1]
            virtual_camera_grid_widget.switch_window(screen_index)
            main_controller.list_parent[gridModel.get_property("id",None)][0] = screen_index

    def screen_index_virtual_window_opened(self, item: QStandardItem):
        if item.item_model.get_property('id') in main_controller.list_parent:
            logger.info(f"screen_index_virtual_window_opened")
            return main_controller.list_parent[item.item_model.get_property('id')][0]
        else:
            return None

    def clear_virtual_window(self, screen_index):
        list_screen_index = {}
        for server in self.tree_data.servers:
            for base_item in server.list_virtual_windows.child_item:
                if base_item.model.get_property('id') in main_controller.list_parent:
                    index = main_controller.list_parent[base_item.model.get_property('id')][0]
                    list_screen_index[index] = base_item.model
        if screen_index in list_screen_index:
            old_gridModel = list_screen_index[screen_index]
            new_custom_tab_widget = main_controller.list_parent['CustomTabWidget']
            # for id, gridModel in gridManager.data.items():
            for id, gridModel in gridManager.data.items():
                if id == old_gridModel.get_property("id"):
                    for index in range(new_custom_tab_widget.tab_widget.count()):
                        widget = new_custom_tab_widget.getWidgetByIndex(index)
                        if id == widget.gridModel.get_property("id"):
                            new_custom_tab_widget.tab_widget.removeTab(index)
                            break
                    break
        else:
            pass

    def apply_tab_to_virtual_window(self,screen_index, item: QStandardItem,widget = None):
        text = item.text()
        gridModel = item.item_model
        camera_screen = main_controller.list_parent['CameraScreen']
        if self.screen_index_virtual_window_opened(item) is None:
            # đây là trường hợp Virtual này chưa được mở màn hình nào, Cần check màn hình screen_index có đang mở virtual nào không, nếu có thì đóng lại và thay thế Virtual mới vào màn hình này
            ######## check co virtual window nao dang mo ##########
            self.clear_virtual_window(screen_index)
        elif self.screen_index_virtual_window_opened(item) == screen_index:
            # đây là trường hợp Virtual window này đã được mở tại màn này rồi
            for index in range(camera_screen.new_custom_tab_widget.tab_widget.count()):
                tab_name = camera_screen.new_custom_tab_widget.tab_widget.tabText(
                    index)
                widget = camera_screen.center_stacked_widget.widget(index)
                if widget.gridModel == gridModel:
                    camera_screen.new_custom_tab_widget.tab_widget.setCurrentIndex(index)
                    return
        else:
            # đây là trường hợp Virtual window này đã được mở màn hình khác, và người dùng có nhu cầu switch sang màn hình có index là screen_index
            ######## check co virtual window nao dang mo ##########
            self.clear_virtual_window(screen_index)
        virtual_window_widget = VirtualCameraGridWidget(gridModel = gridModel,
                                    screen_index=screen_index, item=item)
        virtual_window_widget.showFullScreen()

    def show_border_triggered(self, screen_index, screen = None,action = None):
        if self.previous_screen_index is None:
            self.previous_screen_index = screen_index
            self.screen_index_widget = BorderScreen(screen=screen)
            self.screen_index_widget.show()
        elif self.previous_screen_index != screen_index:
            if self.screen_index_widget is not None:
                self.screen_index_widget.hide()
            self.previous_screen_index = screen_index
            self.screen_index_widget = BorderScreen(screen=screen)
            self.screen_index_widget.show()

    def handle_menu_about_to_hide(self):
        if self.screen_index_widget is not None:
            self.screen_index_widget.close()
            self.screen_index_widget = None
            self.previous_screen_index = None

    def open_to_window_triggered(self, screen_index, item: QStandardItem):
        text = item.text()
        gridModel = item.item_model
        camera_screen = main_controller.list_parent['CameraScreen']

        if self.screen_index_virtual_window_opened(item) is None:
            # đây là trường hợp Virtual này chưa được mở màn hình nào, Cần check màn hình screen_index có đang mở virtual nào không,
            # nếu có thì đóng lại và thay thế Virtual mới vào màn hình này
            ######## check co virtual window nao dang mo ##########
            self.clear_virtual_window(screen_index)
        elif self.screen_index_virtual_window_opened(item) == screen_index:
            # đây là trường hợp Virtual window này đã được mở tại màn này rồi
            logger.debug(f'screen_index = {screen_index} item.text()sssssssssssssss = {item.text()}')
            for index in range(camera_screen.new_custom_tab_widget.tab_widget.count()):
                tab_name = camera_screen.new_custom_tab_widget.tab_widget.tab_bar.tabText(
                    index)
                widget = camera_screen.new_custom_tab_widget.getWidgetByIndex(index)
                if widget.gridModel == gridModel:
                    camera_screen.new_custom_tab_widget.tab_widget.setCurrentIndex(index)
                    return
        else:
            # đây là trường hợp Virtual window này đã được mở màn hình khác, và người dùng có nhu cầu switch sang màn hình có index là screen_index
            ######## check co virtual window nao dang mo ##########
            self.clear_virtual_window(screen_index)
        camera_grid_widget = camera_screen.add_tab_widget(gridModel = gridModel)
        # camera_grid_widget.timeout()
        # time.sleep(3)
        virtual_window_widget = VirtualCameraGridWidget(screen_index=screen_index, item=item,gridModel = gridModel)
        virtual_window_widget.showFullScreen()
        return camera_grid_widget

    def edit_virtual_window_triggered(self, item: QStandardItem):
        index = self.tree_view.currentIndex()
        if index.isValid():
            self.tree_view.edit(index)

    def close_virtual_window_triggered(self, item: QStandardItem):
        new_custom_tab_widget = main_controller.list_parent['CustomTabWidget']
        for id, gridModel in gridManager.data.items():
            if item.item_model is not None and item.item_model == gridModel and gridModel.get_property("type") == CommonEnum.TabType.VIRTUALWINDOW:
                for index in range(new_custom_tab_widget.tab_widget.count()):
                    widget = new_custom_tab_widget.getWidgetByIndex(index)
                    if isinstance(widget,CameraGridWidget) and widget.gridModel ==  gridModel:
                        logger.debug(f"close_virtual_window_triggered gridModel = {gridModel.get_property('name')}")
                        new_custom_tab_widget.tab_widget.removeTab(index)
                        break
                break

    ################### Saved View ################################
    def add_saved_view_triggered(self, item: QStandardItem):
        server = self.get_server(server_ip = item.server_ip)
        controller:Controller = controller_manager.get_controller(server_ip = item.server_ip)
        new_name = self.find_name_selected(tab_type=CommonEnum.TabType.SAVEDVIEW,server_ip=item.server_ip)
        data = {
            # "id": None,
            "name": new_name,
            "type": CommonEnum.TabType.SAVEDVIEW,
            "isShow": False,
            "currentGrid": "{}",
            "listGridData": "{}",
            "listGridCustomData": "NewGrid",
            "direction": str({'id': str(uuid.uuid4())})
        }
        def callback(data):
            if data is not None:
                data["server_ip"] = item.server_ip
                data["type"] = int(data["type"])
                gridModel = GridModel(data=data)
                gridManager.addGridModel(data=gridModel)
                self.add_data(name=new_name, tab_type=TreeType.Saved_View_Item, server=server,model=gridModel)
                root_item = self.add_item(item=item, name=new_name, tree_type=TreeType.Saved_View_Item,server_ip = server.name,model=gridModel)
                index = root_item.index()
                self.tree_view.edit(index)
                return root_item
        subThread = SubThread(parent=self, target=controller.create_tabmodel, args=(data,),callback=callback)
        subThread.start()

    def auto_add_savedview(self,data, server_ip):
        server = self.get_server(server_ip = server_ip)
        controller:Controller = controller_manager.get_controller(server_ip = server_ip)
        list_map = self.get_item('Saved View List', tree_type=TreeType.List_Saved_View,server_ip= server_ip)
        if list_map:
            data["server_ip"] = server_ip
            gridModel = GridModel(data=data)
            gridManager.addGridModel(data=gridModel)
            self.add_data(name=data["name"], tab_type=TreeType.Saved_View_Item, server=server,model=gridModel)
            root_item = self.add_item(item=list_map, name=data["name"], tree_type=TreeType.Saved_View_Item,server_ip = server.name,model=gridModel)
            self.open_saved_view_triggered(root_item)

    def auto_add_virtualwindow(self, data, server_ip):
        server = self.get_server(server_ip = server_ip)
        controller:Controller = controller_manager.get_controller(server_ip = server_ip)
        list_map = self.get_item('Virtual Window List', tree_type=TreeType.List_Virtual_Window,server_ip= server_ip)
        if list_map:
            data["server_ip"] = server_ip
            gridModel = GridModel(data=data)
            gridManager.addGridModel(data=gridModel)
            self.add_data(name=data["name"], tab_type=TreeType.Virtual_Window_Item, server=server,model=gridModel)
            root_item = self.add_item(item=list_map, name=data["name"], tree_type=TreeType.Virtual_Window_Item,server_ip = server.name,model=gridModel)
            self.auto_open_virtual_window(root_item)

    def open_all_saved_view_triggered(self, item: QStandardItem):
        for i in range(item.rowCount()):
            item_child = item.child(i, 0)
            self.open_saved_view_triggered(item_child)

    def close_all_saved_view_triggered(self, item: QStandardItem):
        for i in range(item.rowCount()):
            item_child = item.child(i, 0)
            self.close_saved_view_triggered(item_child)

    def remove_all_saved_view_triggered(self, item: QStandardItem):
        controller:Controller = controller_manager.get_controller(server_ip = item.server_ip)
        list_saved_item = []
        list_id = []
        for id, gridModel in gridManager.data.items():
            if gridModel.get_property("type") == CommonEnum.TabType.SAVEDVIEW:
                list_id.append(gridModel.get_property("id"))
        for i in range(item.rowCount()):
            item_child = item.child(i, 0)
            list_saved_item.append(item_child)
        for item in list_saved_item:
            self.remove_saved_view_triggered(item,remove_all=True)
        if len(list_id) > 0:
            controller.delete_tabmodel(parent = self, ids = list_id)
        list_saved_item.clear()

    def remove_saved_view_triggered(self, item: QStandardItem, remove_all = False):
        # Xoa tab nay neu no dang duoc view
        server = self.get_server(server_ip = item.server_ip)
        controller:Controller = controller_manager.get_controller(server_ip=item.server_ip)
        gridModel = item.item_model
        new_custom_tab_widget = main_controller.list_parent['CustomTabWidget']
        if gridModel.get_property("isShow"):
            for index in range(new_custom_tab_widget.tab_widget.count()):
                widget = new_custom_tab_widget.getWidgetByIndex(index)
                if widget is not None:
                    if widget.gridModel.get_property("id") == gridModel.get_property("id"):
                        new_custom_tab_widget.tab_widget.removeTab(index)
        if not remove_all:
            controller.delete_tabmodel(parent=self,ids = gridModel.get_property('id'))
        gridManager.removeGridModel(gridModel)
        id = shortcut_key_model_manager.delete_shortcutkey_model(model_id=gridModel.get_property('id'))
        if id is not None:
            controller.delete_shortcut_key(parent=self,ids = id)
        key_board_manager.remove_data(start_key=Qt.Key.Key_Asterisk,name=item.text(),tree_type=TreeType.Saved_View_Item)
        self.remove_data(id=gridModel.get_property('id'), tab_type=TreeType.Saved_View_Item, server=server)
        self.model.removeRow(item.row(), item.parent().index())

    def add_to_tab_triggered(self,from_gridModel = None, to_gridModel = None, from_tab_name=None, from_tab_type=None, to_tab_name=None, to_tab_type=None, from_widget=None):
        camera_screen = main_controller.list_parent['CameraScreen']
        controller:Controller = controller_manager.get_controller(server_ip=to_gridModel.get_property('server_ip'))
        for index in range(camera_screen.new_custom_tab_widget.tab_widget.count()):
            to_widget = camera_screen.center_stacked_widget.widget(index)
            if to_widget.gridModel == to_gridModel:
                Notifications(parent=main_controller.list_parent['CameraScreen'],
                                          title=self.tr("Trường hợp này xử lý sau khi refactor code"),
                                          icon=Style.PrimaryImage.info_result)
                return
        data = copy.deepcopy(to_gridModel._data)
        logger.info(f'to_gridModel = {data}')
        data["currentGrid"] = from_gridModel._currentData.get("currentGrid")
        data["listGridCustomData"] = from_gridModel._currentData.get("listGridCustomData")
        data["listGridData"] = from_gridModel._currentData.get("listGridData")
        logger.info(f'to_gridModel1 = {data}')
        def callback(data):
            if data is not None:
                to_gridModel.set_property("currentGrid",data["currentGrid"])
                to_gridModel.set_property("listGridCustomData",data["listGridCustomData"])
                to_gridModel.set_property("listGridData",data["listGridData"])
                logger.info(f'to_gridModel = {to_gridModel._data}')
                if to_gridModel.get_property("type") == CommonEnum.TabType.SAVEDVIEW:
                    
                    item = self.get_item(name=to_gridModel.get_property('name'),tree_type=TreeType.Saved_View_Item,server_ip = to_gridModel.get_property('server_ip'))
                    self.open_saved_view_triggered(item)
                elif to_gridModel.get_property("type") == CommonEnum.TabType.VIRTUALWINDOW:
                    item = self.get_item(name=to_gridModel.get_property('name'),tree_type=TreeType.Virtual_Window_Item,server_ip = to_gridModel.get_property('server_ip'))
                    self.auto_open_virtual_window(item)
        
        subThread = SubThread(parent=self, target=controller.update_tabmodel_by_put, args=(data,),callback=callback)
        subThread.start()

    def virtual_window_triggered(self, screen_index=None, item: QStandardItem = None, virtual_window_model=None):
        logger.info(f'virtual_window_model = {virtual_window_model}')
        sVGridModel:GridModel = item.item_model
        vWGridModel:GridModel = virtual_window_model
        controller = controller_manager.get_controller(server_ip=item.server_ip)
        camera_screen = main_controller.list_parent['CameraScreen']
        if screen_index is None:
            # trường hợp người dùng chọn mở 1 savedview vào 1 virtual window chưa mở
            # sao chép dữ liệu trong savedview sang virtual window
            data = copy.deepcopy(vWGridModel._data)
            data["currentGrid"] = sVGridModel.get_property("currentGrid",str({}))
            data["listGridData"] = sVGridModel.get_property("listGridData",str({}))
            def callback(data):
                if data is not None:
                    vWGridModel.set_property("currentGrid",data["currentGrid"])
                    vWGridModel.set_property("listGridData",data["listGridData"])
                    virtual_window_item = self.get_item(name=vWGridModel.get_property('name'), tree_type=TreeType.Virtual_Window_Item,server_ip=item.server_ip)
                    self.auto_open_virtual_window(virtual_window_item)
            subThread = SubThread(parent=self, target=controller.update_tabmodel_by_put, args=(data,),callback=callback)
            subThread.start()
        else:
            # trường hợp người dùng chọn mở 1 savedview vào 1 virtual window đã mở
            for index in range(camera_screen.new_custom_tab_widget.tab_widget.count()):
                tab_name = camera_screen.new_custom_tab_widget.tab_widget.tab_bar.tabText(
                    index)
                widget = camera_screen.new_custom_tab_widget.getWidgetByIndex(index)
                if widget.gridModel.get_property("id") == vWGridModel.get_property("id"):
                    Notifications(parent=main_controller.list_parent['CameraScreen'],
                                            title=self.tr("Trường hợp này xử lý sau khi refactor code"),
                                            icon=Style.PrimaryImage.info_result)
                    pass
  
    def new_tab_savedview_triggered(self, item: QStandardItem):
        item.setIcon(QIcon(main_controller.get_theme_attribute('Image', 'open_all_virtual')))
        gridModel = item.item_model
        camera_screen = main_controller.list_parent['CameraScreen']
        camera_grid_widget = camera_screen.add_tab_widget(gridModel = gridModel)
        return camera_grid_widget

    def open_saved_view_triggered(self, item: QStandardItem):
        gridModel = item.item_model
        item.setIcon(QIcon(main_controller.get_theme_attribute('Image', 'open_all_virtual')))
        camera_screen = main_controller.list_parent['CameraScreen']
        for index in range(camera_screen.new_custom_tab_widget.tab_widget.count()):
            widget = camera_screen.new_custom_tab_widget.getWidgetByIndex(index)
            if widget.gridModel == gridModel:
                camera_screen.new_custom_tab_widget.tab_widget.setCurrentIndex(index)
                return
        camera_screen.add_tab_widget(gridModel = gridModel)
        # virtual_window_widget = VirtualCameraGridWidget(screen_index=0, item=item,gridModel = gridModel)
        # virtual_window_widget.showFullScreen()

    def edit_saved_view_triggered(self, item: QStandardItem):
        index = self.tree_view.currentIndex()
        if index.isValid():
            self.tree_view.edit(index)

    def close_saved_view_triggered(self, item: QStandardItem):
        item.setIcon(QIcon(main_controller.get_theme_attribute('Image', 'close_all_virtual')))
        new_custom_tab_widget = main_controller.list_parent['CustomTabWidget']
        gridModel = item.item_model
        for index in range(new_custom_tab_widget.tab_widget.count()):
            widget = new_custom_tab_widget.getWidgetByIndex(index)
            if widget.gridModel.get_property("id",None) == gridModel.get_property("id",None):
                new_custom_tab_widget.tab_widget.removeTab(index)
                break

    def save_as_triggered(self, from_gridModel = None, to_tab_type=None, to_tab_name=None, widget=None,server_ip = None):
        logger.info(f'save_as_triggered = {from_gridModel} - {to_tab_type} - {widget.gridModel.get_property("name")} - {server_ip}')
        controller:Controller = controller_manager.get_controller(server_ip=server_ip)
        server = self.get_server(server_ip = server_ip)
        self.new_window = NewVirtualWindowDialog(tab_type=to_tab_type)
        if to_tab_type == CommonEnum.TabType.VIRTUALWINDOW:
            new_name = self.find_name_selected(tab_type=CommonEnum.TabType.VIRTUALWINDOW,server_ip = server_ip)
            self.new_window.virtual_window_name.setText(new_name)
        else:
            new_name = self.find_name_selected(tab_type=CommonEnum.TabType.SAVEDVIEW,server_ip = server_ip)
            self.new_window.virtual_window_name.setText(new_name)
        custom_tab_widget = main_controller.list_parent['CustomTabWidget']
        result = self.new_window.exec()
        if result == QDialog.Accepted:
            name = self.new_window.virtual_window_name.text()
            if to_tab_type == CommonEnum.TabType.VIRTUALWINDOW:
                list_virtual_window_item = self.get_item('Virtual Window List', tree_type=TreeType.List_Virtual_Window,server_ip=server_ip)
                if list_virtual_window_item:
                    for i in range(list_virtual_window_item.rowCount()):
                        item_child = list_virtual_window_item.child(i, 0)
                        if item_child and name == item_child.text():
                            Notifications(parent=main_controller.list_parent['CameraScreen'],
                                          title=self.tr('This Virtual Screen already exists'),
                                          icon=Style.PrimaryImage.info_result)
                            return
                if name != '' and list_virtual_window_item is not None:
                    # merge thong tin grid từ saved view sang virtual window mới
                    if from_gridModel.get_property("type") == CommonEnum.TabType.NORMAL:
                        data = copy.deepcopy(from_gridModel._currentData)
                        del data["id"]
                        data["type"] = CommonEnum.TabType.VIRTUALWINDOW
                        data["name"] = name
                        def callback(data):
                            if data is not None:
                                index = from_gridModel.get_property("index",None)
                                from_gridModel._data = data
                                from_gridModel.set_property("server_ip",server_ip)
                                from_gridModel.set_property("index",index)
                                from_gridModel._currentData = from_gridModel._data
                                custom_tab_widget.tab_widget.tab_bar.setTabText(from_gridModel.get_property("index",None),name)
                                root_item = self.add_item(item=list_virtual_window_item, name=name, tree_type=TreeType.Virtual_Window_Item,server_ip=server_ip,model=from_gridModel)
                                root_item.setIcon(QIcon(main_controller.get_theme_attribute('Image', 'open_all_virtual')))
                                self.add_data(name=name,tab_type=TreeType.Virtual_Window_Item,server=server,model=from_gridModel)
                                screen_index_selected = self.get_screen_index_selected()
                                if screen_index_selected is not None:
                                    logger.info(f'screen_index_selected = {from_gridModel._data} - {widget.gridModel}')
                                    self.apply_tab_to_virtual_window(screen_index=screen_index_selected, item = root_item,widget=widget)
                                else:
                                    virtual_window_widget = VirtualCameraGridWidget(gridModel = from_gridModel,
                                                                screen_index=0, item=root_item)
                                    virtual_window_widget.showFullScreen()
                        subThread = SubThread(parent=self, target=controller.create_tabmodel, args=(data,),callback=callback)
                        subThread.start()
                    else:
                        from_gridModel.set_property("type",CommonEnum.TabType.VIRTUALWINDOW)
                        from_gridModel.set_property("name",name)
                        from_gridModel.set_property("server_ip",server_ip)
                        from_gridModel.set_property("id",name)
                        custom_tab_widget.tab_widget.tab_bar.setTabText(from_gridModel.data.index,name)
                        root_item = self.add_item(item=list_virtual_window_item, name=name, tree_type=TreeType.Virtual_Window_Item,server_ip=server_ip,model=from_gridModel)
                        root_item.setIcon(QIcon(main_controller.get_theme_attribute('Image', 'open_all_virtual')))
                        self.add_data(name=name,tab_type=TreeType.Virtual_Window_Item,server=server,model=from_gridModel)
                        screen_index_selected = self.get_screen_index_selected()
                        if screen_index_selected is not None:
                            self.apply_tab_to_virtual_window(screen_index=screen_index_selected, item = root_item,widget=widget)
                        else:
                            custom_tab_widget.tab_widget.removeTab(widget.tab_model.data.index)
                        direction = {'id': str(uuid.uuid4()),'data':{}}
                        from_gridModel.set_property("direction",direction)
                        from_gridModel.set_property("id",None)
                        controller.create_tabmodel(parent= self, tab = from_gridModel.data)
            elif to_tab_type == CommonEnum.TabType.SAVEDVIEW:
                list_savedview_item = self.get_item('Saved View List', tree_type=TreeType.List_Saved_View,server_ip=server_ip)
                if list_savedview_item:
                    for i in range(list_savedview_item.rowCount()):
                        item_child = list_savedview_item.child(i, 0)
                        if item_child and name == item_child.text():
                            Notifications(parent=main_controller.list_parent['CameraScreen'],
                                          title=self.tr('This saved view already exists'), icon=Style.PrimaryImage.info_result)
                            return
                if name != '' and list_savedview_item is not None:
                    data = copy.deepcopy(from_gridModel._currentData)
                    del data["id"]
                    data["type"] = CommonEnum.TabType.SAVEDVIEW
                    data["name"] = name
                    def callback(data):
                        if data is not None:
                            index = from_gridModel.get_property("index",None)
                            from_gridModel._data = data
                            from_gridModel.set_property("server_ip",server_ip)
                            from_gridModel.set_property("index",index)
                            from_gridModel._currentData = from_gridModel._data
                            custom_tab_widget.tab_widget.tab_bar.setTabText(from_gridModel.get_property("index",None),name)
                            root_item = self.add_item(item=list_savedview_item, name=name, tree_type=TreeType.Saved_View_Item,server_ip=server_ip,model=from_gridModel)
                            root_item.setIcon(QIcon(main_controller.get_theme_attribute('Image', 'open_all_virtual')))
                            self.add_data(name=name,tab_type=TreeType.Saved_View_Item,server=server,model=from_gridModel)
                    subThread = SubThread(parent=self, target=controller.create_tabmodel, args=(data,),callback=callback)
                    subThread.start()

        elif result == QDialog.Rejected:
            logger.debug(f'cancel create_virtual_window_triggered')

    def create_virtual_window_triggered(self, item: QStandardItem):
        server = self.get_server(server_ip = item.server_ip)
        gridModel = item.item_model
        controller:Controller = controller_manager.get_controller(server_ip = item.server_ip)
        self.new_window = NewVirtualWindowDialog()
        new_name = self.find_name_selected(tab_type=CommonEnum.TabType.VIRTUALWINDOW,server_ip=item.server_ip)
        self.new_window.virtual_window_name.setText(new_name)
        result = self.new_window.exec()
        if result == QDialog.Accepted:
            name = self.new_window.virtual_window_name.text()
            virtual_window_widget = main_controller.list_parent['MainTreeView']
            list_virtual_window_item = virtual_window_widget.get_item('Virtual Window List',
                                                                      tree_type=TreeType.List_Virtual_Window)
            if list_virtual_window_item:
                for i in range(list_virtual_window_item.rowCount()):
                    item_child = list_virtual_window_item.child(i, 0)
                    if item_child and name == item_child.text():
                        Notifications(parent=main_controller.list_parent['CameraScreen'],
                                      title=self.tr('This Virtual Screen already exists'), icon=Style.PrimaryImage.info_result)
                        return
            if name != '' and list_virtual_window_item is not None:
                data = {
                    # "id": None,
                    "name": name,
                    "type": CommonEnum.TabType.VIRTUALWINDOW,
                    "isShow": False,
                    "currentGrid": str(gridModel.get_property("currentGrid",{})),
                    "listGridData": str(gridModel.get_property("listGridData",{})),
                    "listGridCustomData": "NewGrid",
                    "direction": str({'id': str(uuid.uuid4())})
                }
                def callback(data):
                    if data is not None:
                        data['server_ip'] = item.server_ip
                        data["type"] = int(data["type"])
                        gridModel = GridModel(data=data)
                        gridManager.addGridModel(data=gridModel)
                        virtual_window_widget.add_data(name=self.new_window.virtual_window_name.text(
                        ), tab_type=TreeType.Virtual_Window_Item, server=server,model = gridModel)
                        self.add_item(item=list_virtual_window_item, name=self.new_window.virtual_window_name.text(
                        ), tree_type=TreeType.Virtual_Window_Item,model=gridModel,server_ip=item.server_ip)
                subThread = SubThread(parent=self, target=controller.create_tabmodel, args=(data,),callback=callback)
                subThread.start()

        elif result == QDialog.Rejected:
            logger.debug(f'cancel create_virtual_window_triggered')
    
    ########### Map ##################################################
    def edit_floor_triggered(self,item: QStandardItem,tab_type = CommonEnum.TabType.MAPVIEW):
        main_controller.edit_floor_signal.emit((item, tab_type))

    def removeFloorWSSignal(self, data):
        serverIp, data = data
        floorModel = floor_manager.getFloor(id = data.get("id"))
        if floorModel is not None:
            controller: Controller = controller_manager.get_controller(server_ip = serverIp)
            list_map = self.get_item('Map List', tree_type=TreeType.List_Map,server_ip=serverIp)
            for i in range(list_map.rowCount()):
                buildingItem = list_map.child(i, 0)
                for j in range(buildingItem.rowCount()):
                    floorItem = buildingItem.child(j, 0)
                    if floorItem.item_model == floorModel:
                        buildingModel:BuildingModel = buildingItem.item_model
                        buildingModel.removeFloor(item = floorModel,controller = controller)
                        self.model.removeRow(floorItem.row(), floorItem.parent().index())
                        listen_show_notification.show_local_message(message=self.tr('The map data has been updated.'))
                        break

    def remove_floor_triggered(self,item: QStandardItem):
        controller: Controller = controller_manager.get_controller(server_ip = item.server_ip)
        dialog = RemoveMapItemDialog(title=self.tr('Delete Floor'),ok_title=self.tr('Confirm'), description=self.tr('Are you sure you want to delete this floor?'))
        result = dialog.exec()
        if result == QDialog.Accepted:
            model = item.item_model
            if model is not None and isinstance(model,FloorModel):
                def callback(data):
                    if data:
                        building_manager.removeFloor(floor_model=model,controller = controller)
                        self.model.removeRow(item.row(), item.parent().index())
                        listen_show_notification.show_local_message(message=self.tr('Delete floor successfully'))
                    else:
                        Notifications(parent=main_controller.list_parent['CameraScreen'],
                                        title=self.tr('Failed to delete the floor.'), icon=Style.PrimaryImage.fail_result)
                subThread = SubThread(parent=self,target=controller.delete_floor,args=(model.id,),callback=callback)
                subThread.start()

        elif result == QDialog.Rejected:
            logger.debug(f'cancel remove_floor_triggered')

    def createFloorWSSignal(self, data):
        server_ip, data, buildingModel = data
        logger.info(f"createFloorWSSignal = {server_ip,data}")
        controller: Controller = controller_manager.get_controller(server_ip = server_ip)
        id = data.get("id", None)
        floorModel = floor_manager.getFloor(id = id)
        if floorModel is None:
            server = self.get_server(server_ip = server_ip)
            list_map = self.get_item('Map List', tree_type=TreeType.List_Map,server_ip=server_ip)
            for i in range(list_map.rowCount()):
                item_child = list_map.child(i, 0)
                if item_child.item_model == buildingModel:
                    floor_model = FloorModel(data=data, controller=controller)
                    floor_model.set_building_model(buildingModel)
                    floor_model.serverIp = server_ip
                    buildingModel.appendfloorIds(floor_model)
                    self.add_item(item=item_child, name=floor_model.name, tree_type=TreeType.FloorItem,server_ip=server_ip,model=floor_model)
                    Notifications(parent=main_controller.list_parent['CameraScreen'],
                                title=self.tr('The map data has been updated.'), icon=Style.PrimaryImage.sucess_result)
                    break
            
    def create_floor(self,item: QStandardItem):
        server = self.get_server(server_ip = item.server_ip)
        controller: Controller = controller_manager.get_controller(server_ip = item.server_ip)
        dialog = AddFloorDialog(ok_title=self.tr('Create Floor'),name = item.text())
        result = dialog.exec()
        if result == QDialog.Accepted:
            name = dialog.floor_name.text()
            file_path = dialog.image_url
            if name != '' and file_path is not None:
                def callback(data):
                    if data is not None:
                        floor_model = FloorModel(data=data,controller=controller)
                        floor_model.set_building_model(item.item_model)
                        floor_model.serverIp = item.server_ip
                        item.item_model.appendfloorIds(floor_model)
                        self.add_item(item=item, name=floor_model.name, tree_type=TreeType.FloorItem,server_ip=item.server_ip,model=floor_model)
                        # Notifications(parent=main_controller.list_parent['CameraScreen'],
                        #             title=self.tr('Floor added successfully.'), icon=Style.PrimaryImage.sucess_result)
                    else:
                        Notifications(parent=main_controller.list_parent['CameraScreen'],
                                        title=self.tr('Failed to add the floor.'), icon=Style.PrimaryImage.fail_result)
                def process():
                    fileLink = controller.update_file(file_path)
                    if fileLink is not None and fileLink != "":
                        data = controller.create_floor(data = {"name": name,"fileLink": fileLink,"cameraIds":[],"clientId": Utils.clientId})
                        
                        if data is not None:
                            result = controller.add_floor_to_building(id=item.item_model.id,ids=[data["id"]])
                            if result is not None:
                                return data
                    return None
                subThread = SubThread(parent=self,target=process,callback=callback)
                subThread.start()

            else:
                listen_show_notification.show_local_message(message=self.tr('Create floor failed'))
        elif result == QDialog.Rejected:
            logger.debug(f'cancel new_virtual_window_triggered')

    def updateBuildingWSSignal(self, data):
        server_ip, data = data
        Notifications(parent=main_controller.list_parent['CameraScreen'],
                    title=self.tr('The map data has been updated.'), icon=Style.PrimaryImage.sucess_result)
            
    def edit_building_triggered(self, item: QStandardItem):
        controller:Controller = controller_manager.get_controller(server_ip = item.server_ip)
        self.dialog = CreateBuildingDialog(name=item.text(),ok_title="Edit Building")
        result = self.dialog.exec()
        if result == QDialog.Accepted:
            name = self.dialog.content_name.text()
            if name != '':
                building_model:BuildingModel = item.item_model
                if building_model:
                    def callback(data):
                        if data is not None:
                            buildingModel:BuildingModel = building_manager.getBuilding(data.get("id"))
                            if buildingModel is not None:
                                buildingModel.diffBuildingModel(data)
                                Notifications(parent=main_controller.list_parent['CameraScreen'],
                                        title=self.tr('Updated successfully.'), icon=Style.PrimaryImage.sucess_result)
                        else:
                            Notifications(parent=main_controller.list_parent['CameraScreen'],
                                        title=self.tr('Failed to update the building.'), icon=Style.PrimaryImage.sucess_result)
                    def process():
                        data = {"id": building_model.id, "name": name, "clientId": Utils.clientId}
                        result = controller.update_building_by_patch(data)
                        if result is not None:
                            return result
                    subThread = SubThread(parent=self,target=process,callback=callback)
                    subThread.start()
            else:
                pass
        elif result == QDialog.Rejected:
            logger.debug(f'cancel create_virtual_window_triggered')

    def removeBuildingWSSignal(self, data):
        server_ip, data = data
        try:
            id = data.get("id", None)
            buildingModel = building_manager.getBuilding(id)
            if buildingModel is not None:
                server = self.get_server(server_ip = server_ip)
                list_map = self.get_item('Map List', tree_type=TreeType.List_Map,server_ip=server_ip)
                self.remove_data(id=buildingModel.id, tab_type=TreeType.BuildingItem, server=server)
                map_manager.removeBuilding(buildingModel)
                for i in range(list_map.rowCount()):
                    item_child = list_map.child(i, 0)
                    if item_child is not None:
                        if item_child.item_model == buildingModel:
                            self.model.removeRow(item_child.row(), item_child.parent().index())
                Notifications(parent=main_controller.list_parent['CameraScreen'],
                                title=self.tr('The map data has been updated.'), icon=Style.PrimaryImage.sucess_result)
        except Exception as e:
              logger.info(f'removeBuildingWSSignal error: {e}')

    def remove_building_triggered(self, item: QStandardItem):
        server = self.get_server(server_ip = item.server_ip)
        controller:Controller = controller_manager.get_controller(server_ip=item.server_ip)
        dialog = RemoveMapItemDialog(title=self.tr('Delete Building'),ok_title=self.tr('Confirm'), description=self.tr('Are you sure you want to delete this building?'))
        result = dialog.exec()
        if result == QDialog.Accepted:
            model = item.item_model
            if isinstance(model,BuildingModel):
                def callback(data):
                    if data:
                        try:
                            self.remove_data(id=model.id, tab_type=TreeType.BuildingItem, server=server)
                            map_manager.removeBuilding(model)
                            self.model.removeRow(item.row(), item.parent().index())
                            Notifications(parent=main_controller.list_parent['CameraScreen'],
                                            title=self.tr('Building deleted successfully.'), icon=Style.PrimaryImage.sucess_result)
                        except Exception as e:
                            logger.info(f'removeBuildingWSSignal error: {e}')
                    else:
                        Notifications(parent=main_controller.list_parent['CameraScreen'],
                                        title=self.tr('Failed to delete the building.'), icon=Style.PrimaryImage.fail_result)
                def process():
                    return controller.delete_building(ids=model.id)

            subThread = SubThread(parent=self,target=process,callback=callback)
            subThread.start()

    def remove_all_building_triggered(self, item: QStandardItem):
        controller:Controller = controller_manager.get_controller(server_ip = item.server_ip)
        list_item = []
        list_ids = []
        for i in range(item.rowCount()):
            item_child = item.child(i, 0)
            list_ids.append(item_child.item_model.id)
        #     list_item.append(item_child)
        # for item in list_item:
        #     self.remove_building_triggered(item)
        # list_item.clear()
        subThread = SubThread(parent=self,target=controller.delete_building(ids=list_ids))
        subThread.start()

    def edit_map_triggered(self, item, tab_type):
        main_controller.edit_map_signal.emit((item, tab_type))

    def createBuildingWSSignal(self, data):
        server_ip, data = data
        id = data.get("id", None)
        buidingModel = building_manager.getBuilding(id)
        if buidingModel is None:
            server = self.get_server(server_ip = server_ip)
            list_map = self.get_item('Map List', tree_type=TreeType.List_Map,server_ip=server_ip)
            building_model = BuildingModel(data=data)
            building_model.serverIp = server_ip
            map_manager.addBuilding(building_model=building_model)
            self.add_buildings(list_buildings={building_model.id: building_model},server = server)
            self.add_item(item=list_map, name=data.get("name", "null"), tree_type=TreeType.BuildingItem,server_ip=server_ip,model=building_model)
            Notifications(parent=main_controller.list_parent['CameraScreen'],
                            title=self.tr('The map data has been updated.'), icon=Style.PrimaryImage.sucess_result)

    def create_building(self, item):
        server = self.get_server(server_ip = item.server_ip)
        controller:Controller = controller_manager.get_controller(server_ip = item.server_ip)
        self.dialog = CreateBuildingDialog()
        result = self.dialog.exec()
        if result == QDialog.Accepted:
            name = self.dialog.content_name.text()
            list_map = self.get_item('Map List', tree_type=TreeType.List_Map,server_ip=item.server_ip)
            if name != '' and list_map is not None:
                def callback(data):
                    if data is not None:
                        id = data.get("id", None)
                        buidingModel = building_manager.getBuilding(id)
                        if buidingModel is None:
                            building_model = BuildingModel(data=data)
                            building_model.serverIp = item.server_ip
                            map_manager.addBuilding(building_model=building_model)
                            self.add_buildings(list_buildings={building_model.id: building_model},server = server)
                            self.add_item(item=item, name=data.get("name", "null"), tree_type=TreeType.BuildingItem,server_ip=item.server_ip,model=building_model)
                            Notifications(parent=main_controller.list_parent['CameraScreen'],
                                            title=self.tr('Building added successfully.'), icon=Style.PrimaryImage.sucess_result)
                    else:
                        Notifications(parent=main_controller.list_parent['CameraScreen'],
                                        title=self.tr('Failed to add the building.'), icon=Style.PrimaryImage.fail_result)
                def process():
                    data = {"name":name, "latitude": None,"longitude": None, "location": None, "floorIds": [], "clientId": Utils.clientId}
                    data = controller.create_building(data = data)
                    if data is not None:
                        return data
                    return None
                subThread = SubThread(parent=self,target=process,callback=callback)
                subThread.start()
            else:
                pass
        elif result == QDialog.Rejected:
            logger.debug(f'cancel create_virtual_window_triggered')

    def keyPressEvent(self, event):
        selected_indexes = self.tree_view.selectedIndexes()
        if event.key() == Qt.Key_F2:
            if len(selected_indexes) == 1:
                # Check xem item có thuộc type là TreeType.Virtual_Window_Item hay TreeType.Saved_View_Item thì mới cho edit
                item = self.tree_view.model().itemFromIndex(selected_indexes[0])
                item_data = item.data(Qt.UserRole)
                if item_data == TreeType.Virtual_Window_Item or item_data == TreeType.Saved_View_Item \
                        or item_data == TreeType.Camera or item_data == TreeType.Group:
                    if selected_indexes[0].isValid():
                        self.tree_view.edit(selected_indexes[0])
        elif event.key() == Qt.Key.Key_Return:
            index = self.tree_view.currentIndex()
        elif event.key() == Qt.Key_Delete:
            if len(selected_indexes) == 1:
                item = self.model.itemFromIndex(selected_indexes[0])
                item_data = item.data(Qt.UserRole)
                if item_data == TreeType.Virtual_Window_Item:
                    self.remove_virtual_window_triggered(item)
                elif item_data == TreeType.Saved_View_Item:
                    self.remove_saved_view_triggered(item)
                elif item_data == TreeType.Camera:
                    self.delete_single_item_trigger(item, controller_manager.get_controller(server_ip=item.server_ip))
                elif item_data == TreeType.Group:
                    self.delete_single_item_trigger(item, controller_manager.get_controller(server_ip=item.server_ip))
            else:
                # Delete multi selection
                selected_items = []
                for index in selected_indexes:
                    item = self.model.itemFromIndex(index)
                    if item is not None:
                        # Get item data
                        item_data = item.data(Qt.UserRole)
                        if item_data is not None:
                            selected_items.append(item)
                if isinstance(selected_items[0].item_model,CameraModel):
                    server_ip = selected_items[0].item_model.get_property('server_ip')
                else:
                    server_ip = selected_items[0].item_model.get_property('server_ip')
                self.delete_multi_item(selected_items, server_ip)
        elif event.key() == Qt.Key.Key_I and event.modifiers() == Qt.ControlModifier:
            if len(selected_indexes) == 1:
                item = self.tree_view.model().itemFromIndex(selected_indexes[0])
                self.open_setting_trigger(item)
        elif event.key() == Qt.Key.Key_Escape:
            self.tree_view.clearSelection()
            self.tree_view.selectionModel().clearSelection()
            self.tree_view.selectionModel().clear()
            self.tree_view.selectionModel().clearCurrentIndex()

    def get_screen_index_selected(self):
        ######## check co virtual window nao dang mo ##########
        list_screen_index = {}
        for server in self.tree_data.servers:
            for base_item in server.list_virtual_windows.child_item:
                if base_item.model.get_property("id") in main_controller.list_parent:
                    index = main_controller.list_parent[base_item.model.get_property("id")][0]
                    list_screen_index[index] = base_item.name
        ############## check man hinh #######################
        current_screen = self.screen()
        screens = QApplication.screens()
        screen_index_selected = None
        if len(screens) > 1:
            for screen in screens:
                screen_index = QApplication.screens().index(screen)
                if screen != current_screen:
                    if screen_index not in list_screen_index:
                        screen_index_selected = screen_index
                else:
                    pass

            ########################################################
            if screen_index_selected is not None:
                return screen_index_selected
            else:
                #
                for screen in screens:
                    screen_index = QApplication.screens().index(screen)
                    if screen != current_screen:
                        screen_index_selected = screen_index
                        pre_notify = self.tr("This screen already contains a virtual window named")
                        suf_notify = self.tr("Do you want to replace this virtual window?")
                        # notify = f"This screen already contains a virtual window named '{list_screen_index[screen_index_selected]}'. Do you want to replace this virtual window"
                        dialog = WarningDialog(
                            warn=f"{pre_notify} '{list_screen_index[screen_index_selected]}' {suf_notify}")
                        result = dialog.exec()
                        if result == QDialog.Accepted:
                            return screen_index_selected
                        elif result == QDialog.Rejected:
                            return None

                        break
        else:
            return 0
        return None

    def callback_setModelData(self, editor, model, index):
        new_text = editor.text()
        old_text = model.data(index)
        standard_item = model.itemFromIndex(index)
        standard_item.setText(old_text)
        item_data = model.data(index, Qt.UserRole)
        controller = controller_manager.get_controller(server_ip=standard_item.server_ip)
        # Check xem có trùng tên item không
        if item_data == TreeType.Virtual_Window_Item:
            # Khi edit thì cần thay đổi data trong MainTreeViewModel
            server = self.get_server(server_ip=standard_item.server_ip)
            data = server.list_virtual_windows.child_item
            if data is not None:
                check = False
                for item in data:
                    if item.name == new_text:
                        logger.debug(f'trung ten')
                        check = True
                if check:
                    return
                else:
                    for item in data:
                        if item.name == old_text:
                            item.name = new_text
            # Khi edit thì cần thay đổi tên Tab và tên cửa sổ Virtual Window nếu nó đang được mở
            camera_screen = main_controller.list_parent['CameraScreen']
            is_show = False
            for i in range(camera_screen.new_custom_tab_widget.tab_widget.count()):
                widget = camera_screen.new_custom_tab_widget.getWidgetByIndex(i)
                if widget.gridModel.get_property("id") == standard_item.item_model.get_property("id") and old_text == widget.gridModel.get_property('name'):
                    # if tab_name == old_text:
                    is_show = True
                    camera_screen.new_custom_tab_widget.tab_widget.tab_bar.setTabText(
                        i, new_text)
                    # widget.tab_name = new_text
                    gridModel = widget.gridModel
                    gridModel.set_property("name",new_text)

                    def callback(data):
                        if data is not None:
                            pass
                    subThread = SubThread(parent=self, target=controller.update_tabmodel_by_put, args=(gridModel._data,),callback=callback)
                    subThread.start()
                    break

            if standard_item.item_model.get_property("id") in main_controller.list_parent:
                camera_grid_base = main_controller.list_parent[standard_item.item_model.get_property("id")][1]
                camera_grid_base.setWindowTitle(new_text)
            # Khi edit thì cần lưu thông tin lại trong QSetting
            if not is_show:
                gridModel = standard_item.item_model
                gridModel.set_property("name",new_text)
                def callback(data):
                    if data is not None:
                        pass
                subThread = SubThread(parent=self, target=controller.update_tabmodel_by_put, args=(gridModel._data,),callback=callback)
                subThread.start()
            model.setData(index, new_text, Qt.DisplayRole)

        elif item_data == TreeType.Saved_View_Item:
            server = self.get_server(server_ip=standard_item.server_ip)
            data = server.list_saved_views.child_item
            if data is not None:
                check = False
                for item in data:
                    if item.name == new_text:
                        logger.debug(f'trung ten')
                        check = True
                if check:
                    return
                else:
                    for item in data:
                        if item.name == old_text:
                            item.name = new_text
            # Khi edit thì cần thay đổi tên Tab và tên cửa sổ Virtual Window nếu nó đang được mở
            camera_screen = main_controller.list_parent['CameraScreen']
            is_show = False
            for i in range(camera_screen.new_custom_tab_widget.tab_widget.count()):
                widget = camera_screen.new_custom_tab_widget.getWidgetByIndex(i)
                if widget.gridModel.get_property("id") == standard_item.item_model.get_property("id") and old_text == widget.gridModel.get_property("name"):
                    is_show = True
                    camera_screen.new_custom_tab_widget.tab_widget.tab_bar.setTabText(
                        i, new_text)
                    gridModel = widget.gridModel
                    gridModel.set_property("name",new_text)
                    def callback(data):
                        if data is not None:
                            pass
                    subThread = SubThread(parent=self, target=controller.update_tabmodel_by_put, args=(gridModel._data,),callback=callback)
                    subThread.start()
                    break
            if not is_show:
                gridModel = standard_item.item_model
                gridModel.set_property("name",new_text)
                def callback(data):
                    if data is not None:
                        pass
                subThread = SubThread(parent=self, target=controller.update_tabmodel_by_put, args=(gridModel._data,),callback=callback)
                subThread.start()
            model.setData(index, new_text, Qt.DisplayRole)
        elif item_data == TreeType.Group:
            group_model = standard_item.item_model
            model_group = copy.deepcopy(group_model.data)
            if model_group is not None:
                model_group["name"] = new_text
                controller.update_camera_group_by_put(data=model_group)
        elif item_data == TreeType.Camera:
            camera_model = standard_item.item_model
            camera_data = copy.deepcopy(camera_model.data)
            if camera_data is not None:
                camera_data["name"] = new_text
                controller.update_camera_by_put(data=camera_data)

    def is_saved_view_opened(self, item):
        gridModel = item.item_model
        camera_screen = main_controller.list_parent['CameraScreen']
        for index in range(camera_screen.new_custom_tab_widget.tab_widget.count()):
            widget = camera_screen.new_custom_tab_widget.getWidgetByIndex(index)
            if widget.gridModel == gridModel:
                return True
        return False
    
    def find_name_group(self, server_ip):
        list_group_name = []
        group_list = group_model_manager.get_group_list(server_ip=server_ip)
        for key, group in group_list.items():
            list_group_name.append(group.get_property('name'))
        is_name_existing = False
        count = 0
        new_name = f'New group {count}'
        while not is_name_existing:
            new_name = f'New group {count}'
            if new_name not in list_group_name:
                is_name_existing = True
            else:
                count += 1
        return new_name

    def connect_3rd_party_server_triggered(self, item: QStandardItem):
        item_data = item.data(Qt.UserRole)
        if item_data == TreeType.Server:
            server = self.get_server(server_ip=item.server_ip)
            dialog = Connect3rdPartyServerDialog(server=server)
            result = dialog.exec()
            if result == QDialog.Accepted:
                pass

    def create_group_single_item_trigger(self, item=None):
        item_data = item.data(Qt.UserRole)
        if item_data == TreeType.List_Camera:
            new_name_group = self.find_name_group(item.server_ip)
            new_group = Group(name=new_name_group)
            controller = controller_manager.get_controller(server_ip=item.server_ip)
            controller.create_camera_group(parent=self, data=new_group, is_insert_group=True)
        elif item_data == TreeType.Camera:
            camera_model = item.item_model
            camera_data = replace(camera_model.data)
            new_name_group = self.find_name_group(item.server_ip)
            new_group = Group(name=new_name_group, cameraIds=[camera_data.id])
            controller = controller_manager.get_controller(server_ip=item.server_ip)
            controller.create_camera_group(parent=self, data=new_group, is_insert_group=True)

    def create_group_with_multi_item(self, list_camera_id, server_ip):
        if len(list_camera_id) > 0:
            new_name_group = self.find_name_group(server_ip)
            new_group = Group(name=new_name_group, cameraIds=list_camera_id)
            controller = controller_manager.get_controller(server_ip=server_ip)
            controller.create_camera_group(parent=self, data=new_group, is_insert_group=True)

    def delete_single_item_trigger(self, item, controller):
        dialog = RemoveMapItemDialog(title=self.tr('Delete Item'),ok_title=self.tr('Confirm'), description=self.tr('Do you want to delete this item?'))
        result = dialog.exec()
        if result == QDialog.Accepted:
            item_data = item.data(Qt.UserRole)
            if item_data == TreeType.Camera:
                camera_model = item.item_model
                controller.delete_camera(data = camera_model.data)
            elif item_data == TreeType.Group:
                group_model = item.item_model
                controller.delete_camera_group(data=group_model.data)
            elif item_data == TreeType.Saved_View_Item:
                self.remove_saved_view_triggered(item=item)
            elif item_data == TreeType.Virtual_Window_Item:
                listen_show_notification.show_local_message(message=self.tr('Delete item successfully'))
                self.remove_virtual_window_triggered(item=item)
            else:
                Notifications(parent=main_controller.list_parent['CameraScreen'],
                                title=self.tr('Failed to delete item.'), icon=Style.PrimaryImage.fail_result)

        elif result == QDialog.Rejected:
            logger.debug(f'cancel remove tree item')

    def delete_multi_item(self, list_item, server_ip):
        controller = controller_manager.get_controller(server_ip=server_ip)
        for item in list_item:
            self.delete_single_item_trigger(item, controller)

    def remove_camera_from_group(self,item,controller):
        parent = item.parent()
        if isinstance(parent.item_model,GroupModel):
            group_model = parent.item_model
            list_id = []
            for id in group_model.get_property("cameraIds"):
                if id != item.item_model.get_property('id'):
                    list_id.append(id)
            group = GroupModel(data=copy.deepcopy(group_model.data))
            group.set_property("cameraIds",list_id)
            controller.update_camera_group_by_put(data=group.data)

    def rename_trigger(self, item):
        index = self.tree_view.currentIndex()
        if index.isValid():
            self.tree_view.edit(index)

    def open_setting_trigger(self, item):
        item_data = item.data(Qt.UserRole)
        if item_data == TreeType.Camera:
            if item.item_model is not None:
                if main_controller.key_filter is not None:
                    QApplication.instance().removeEventFilter(main_controller.key_filter)
                camera_info = CameraInfoDialog(parent=main_controller.list_parent['CameraScreen'], title=item.item_model.get_property('name'), data=item.item_model)
                camera_info.exec()
                if main_controller.key_filter is not None:
                    QApplication.instance().installEventFilter(main_controller.key_filter)
        elif item_data == TreeType.Group:
            if item.item_model is not None:
                group_info = AddGroupDialog(parent=main_controller.list_parent['CameraScreen'], title=self.tr("Edit group"),
                                            group_model=item.item_model)
                group_info.exec()

    def action_change_ai_single_camera_click(self, data_emitted):
        index, ai_flow_id_emitted, camera_model = data_emitted
        controller = controller_manager.get_controller(server_ip=camera_model.get_property('server_ip'))
        list_ai: dict[str, AiFlow] = self.get_list_ai_flow(camera_model.get_property("aiFlowIds"), controller)
        ai_flows = None
        if len(list_ai) > 0:
            for aiFlowId, aiflow_model in list_ai.items():
                aiflow = aiflow_model.data
                if ai_flow_id_emitted == aiFlowId:
                    ai_flows = aiflow
        ai_flows.apply = not ai_flows.apply
        self.enable_disable_ai_flow(ai_flows, controller)

    def enable_disable_ai_flow(self, ai_flows, controller):
        if ai_flows is not None:
            def enable(data):
                result = controller.update_aiflows(data=data)
                if result is not None:
                    return True
                return False
            def callback(result):
                if not result:
                    pass
            WorkerThread(parent=self, target=enable, callback=callback, args=(ai_flows,)).start()

    def action_aitype_signal(self, data_emitted):
        key, model = data_emitted
        controller = controller_manager.get_controller(server_ip=model.get_property('server_ip'))
        if key == "Turn off AI":
            for ai_type in model.get_property("features"):
                data = {
                    "id": model.get_property('id'),
                    "apply": False,
                    "type": ai_type
                }
                controller.apply_ai_flow_camera(data = data)
            return
        data = {
            "id": model.get_property('id'),
            "apply": False if key in model.get_property("features") else True,
            "type": key
        }
        controller.apply_ai_flow_camera(data = data)

    def change_ai_multi_item_clicked(self, data):
        thread = WorkerThread(parent=self, target=self.change_ai_multi_item, args=(data,))
        thread.start()

    def change_ai_multi_item(self, data):
        ai_type, model_type, list_group, list_camera = data
        camera_list = camera_model_manager.get_camera_list()
        group_list = group_model_manager.get_group_list()
        list_camera_model = [camera for camera in camera_list if
                             camera.get_property('name') in [camera_item.text() for camera_item in list_camera]]
        list_group_model = [group for group in group_list if
                            group.get_property('name') in [group_item.text() for group_item in list_group]]
        for group in list_group_model:
            list_ai_activate = self.get_ai_activate(group.get_property("aiFlowIds"))
            if ai_type == AiType.Face and AIType.HUMAN.name not in list_ai_activate:
                ai_flow = AiFlow(apply=True, cameraGroupIds=[group.get_property('id')], type=AIType.HUMAN.name)
                main_controller.create_aiflows(ai_flow)
            elif ai_type == AiType.Vehicle and AIType.VEHICLE.name not in list_ai_activate:
                ai_flow = AiFlow(apply=True, cameraGroupIds=[group.get_property('id')], type=AIType.VEHICLE.name)
                main_controller.create_aiflows(ai_flow)
            elif ai_type == AiType.Off:
                for id in group.get_property("aiFlowIds"):
                    aiflow = aiflow_model_manager.get_aiflow_model(id = id)
                    if aiflow is not None and aiflow.data.is_apply():
                        ai_flow = AiFlow(id = aiflow.id, apply=False,type=aiflow.type)
                        main_controller.update_aiflows(ai_flow)
        for camera in list_camera_model:
            list_ai_activate = self.get_ai_activate(camera.get_property("aiFlowIds"))
            if ai_type == AiType.Face and AIType.HUMAN.name not in list_ai_activate:
                ai_flow = AiFlow(apply=True, cameraId=camera.get_property('id'), type=AIType.HUMAN.name)
                main_controller.create_aiflows(ai_flow)
            elif ai_type == AiType.Vehicle and AIType.VEHICLE.name not in list_ai_activate:
                ai_flow = AiFlow(apply=True, cameraId=camera.get_property('id'), type=AIType.VEHICLE.name)   
                main_controller.create_aiflows(ai_flow)   
            elif ai_type == AiType.Off:
                for id in camera.get_property("aiFlowIds"):
                    aiflow = aiflow_model_manager.get_aiflow_model(id = id)
                    if aiflow is not None and aiflow.data.is_apply():
                        ai_flow = AiFlow(id = aiflow.id, apply=False,type=aiflow.type)
                        main_controller.update_aiflows(ai_flow)

    def get_list_ai_flow(self, aiFlowIds: List = [], controller=None):
        list_ai_flow = {}
        for aiFlowId in aiFlowIds:
            aiflow = aiflow_model_manager.get_aiflow_model(id = aiFlowId)
            if aiflow is not None:
                list_ai_flow[aiFlowId] = aiflow
        return list_ai_flow

    def get_ai_activate(self,aiFlowIds:List = []):
        list_ai_activate = []
        for aiFlowId in aiFlowIds:
            aiflow = aiflow_model_manager.get_aiflow_model(id = aiFlowId)
            if aiflow is not None and aiflow.data.is_apply():
                list_ai_activate.append(aiflow.type)
        return list_ai_activate

    def add_camera_to_group_from_treeview(self, group_model, index, selection_list_ids=None):
        controller = controller_manager.get_controller(server_ip=group_model.get_property('server_ip'))
        # replace(): tạo 1 instance mới của dataclass, không copy đến các đối tượng lồng nhau bên trong dataclass
        # deepcopy() dùng cho data class, copy cả tham chiếu đệ quy của dataclass, nó create new object
        # chỗ nào cần tạo 1 bản copy và sửa thì dùng deepcopy, chỗ nào chỉ cần copy dữ liệu thì dùng replace()
        model_temp = copy.deepcopy(group_model.data)
        group_temp = GroupModel(data=model_temp)
        for camera_id in selection_list_ids:
            if camera_id not in group_temp.get_property("cameraIds"):
                group_temp.get_property("cameraIds").append(camera_id)
        controller.update_camera_group_by_put(data=group_temp.data)

    def init_action_and_menu(self, item, item_tree_name=None, is_click_camera=True, camera_grid_widget=None):
        item_data = item.data(Qt.UserRole)
        list_type_ai_flow = {
            "Turn off AI": self.tr("Turn off AI"),
            AIFlowType.RECOGNITION: self.tr("Recognition"),
            AIFlowType.PROTECTION: self.tr("Protection"),
            AIFlowType.FREQUENCY: self.tr("Frequency"),
            AIFlowType.ACCESS: self.tr("Access"),
            AIFlowType.MOTION: self.tr("Motion"),
            AIFlowType.TRAFFIC: self.tr("Traffic"),
            AIFlowType.WEAPON: self.tr("Weapon"),
            AIFlowType.UFO: self.tr("UFO"),
            AIFlowType.FIRE: self.tr("Fire"),
        }
        self.sub_menu_ai_flow = CustomMenuWithCheckbox(item_data=item.data(Qt.UserRole),
                                                       list_type_ai_flow=list_type_ai_flow,
                                                       controller=controller_manager.get_controller(server_ip=item.server_ip),
                                                       item_model=item.item_model)
        self.sub_menu_ai_flow.action_aitype_signal.connect(self.action_aitype_signal)
        ###############################################
        self.menu_list_tab_available = SubMenuOpenCameraInTab(model = item.item_model, item_data=item.data(Qt.UserRole),
                                                              camera_name=item_tree_name,
                                                              show_grid_position=is_click_camera)
        self.menu_list_tab_available.open_in_tab_signal.connect(self.camera_open_in_tab)
        self.menu_list_tab_available.open_new_tab_signal.connect(self.camera_open_in_tab)
        self.menu_list_tab_available.open_group_in_view_signal.connect(self.camera_open_in_tab)
        if item_data == TreeType.Group:
            self.action_stream_group_to = QAction(self.tr("Stream group in"), self.main_menu,
                                                  menu=self.menu_list_tab_available)
        self.delete_action = QAction(text=self.tr("Delete\tDel"), parent=self.main_menu)
        self.delete_action.setShortcut(QKeySequence(Qt.Key.Key_Delete))
        self.delete_action.triggered.connect(lambda: (self.delete_single_item_trigger(item, controller_manager.get_controller(server_ip=item.server_ip))))
        self.remove_camera_action = QAction(text=self.tr("Remove from Group"), parent=self.main_menu)
        self.remove_camera_action.triggered.connect(lambda: (self.remove_camera_from_group(item, controller_manager.get_controller(server_ip=item.server_ip))))
        self.rename_action = QAction(text=self.tr("Rename\tF2"), parent=self.main_menu)
        self.rename_action.setShortcut(QKeySequence(Qt.Key.Key_F2))
        self.rename_action.triggered.connect(lambda: (self.rename_trigger(item)))
        self.choose_ai_flow_action = QAction(text=self.tr("AI flow"), parent=self.main_menu)
        self.choose_ai_flow_action.setMenu(self.sub_menu_ai_flow)
        self.open_settings_action = QAction(text=self.tr("Setting\tCtrl+I"), parent=self.main_menu)
        self.open_settings_action.setShortcut(QKeySequence(Qt.CTRL | Qt.Key_I))
        self.open_settings_action.triggered.connect(lambda: (self.open_setting_trigger(item)))
        if item_data == TreeType.Camera:
            selected_indexes = self.tree_view.selectedIndexes()
            selections_list = []
            if len(selected_indexes) == 1:
                camera_item = self.model.itemFromIndex(selected_indexes[0])
                selections_list.append(camera_item.item_model.get_property('id'))
            self.sub_menu_group_name = QMenu()
            self.sub_menu_group_name.setWindowFlags(
                Qt.WindowType.FramelessWindowHint | Qt.WindowType.Popup)
            self.sub_menu_group_name.setAttribute(
                Qt.WidgetAttribute.WA_TranslucentBackground, True)
            self.sub_menu_group_name.setStyleSheet(Style.PrimaryStyleSheet.get_context_menu_style(theme_instance=main_controller))
            list_groups = self.get_group_list(server_ip=item.item_model.get_property('server_ip'))
            for idx, (key, group) in enumerate(list_groups.items()):
                choose_group_action = QAction(text=f"{group.get_property('name')}", parent=self.sub_menu_group_name)
                choose_group_action.triggered.connect(partial(self.add_camera_to_group_from_treeview, group, idx, selections_list))
                self.sub_menu_group_name.addAction(choose_group_action)
            self.action_open_camera_to = QAction(self.tr("Open camera to ...    "), self.main_menu,
                                                 menu=self.menu_list_tab_available)
            self.add_camera_to_group_action = QAction(text=self.tr("Add camera to group"), parent=self.main_menu)
            self.add_camera_to_group_action.setMenu(self.sub_menu_group_name)

    def show_context_menu(self, pos):
        global_pos = self.tree_view.viewport().mapToGlobal(pos)
        selected_indexes = self.tree_view.selectedIndexes()
        if len(selected_indexes) == 0:
            self.tree_view.clearSelection()
            self.tree_view.selectionModel().clearSelection()
            self.tree_view.selectionModel().clear()
            self.tree_view.selectionModel().clearCurrentIndex()
            return
        if len(selected_indexes) == 1:
            item = self.model.itemFromIndex(selected_indexes[0])
            item_text = item.text()
            self.show_menu(position=global_pos, item=item, group_name=item_text, camera_name=item_text)
        else:
            self.show_menu_multi_select(position=global_pos, selected_indexes=selected_indexes)

    def _process_delayed_selection(self):
        """Xử lý selection sau khi debounce để tránh lag."""
        if self._pending_selection_data is not None:
            position, selected_indexes = self._pending_selection_data
            self._pending_selection_data = None
            self._show_menu_multi_select_internal(position, selected_indexes)

    def show_menu_multi_select(self, position, selected_indexes):
        """Debounce multi-select menu để tránh lag khi Shift+click."""
        # Lưu data và restart timer
        self._pending_selection_data = (position, selected_indexes)
        self.selection_debounce_timer.start(150)  # 150ms debounce

    def _show_menu_multi_select_internal(self, position, selected_indexes):
        """Internal method để xử lý multi-select menu."""
        unique_data_values = set()
        selected_items = []
        list_camera_item = []
        self.group_items = []
        self.cameras_not_in_group = []
        list_saved_view_selected = []
        list_virtual_selected = []
        selected_item_ids = []
        list_just_select_camera_ids = []
        for index in selected_indexes:
            item: CustomStandardItem = self.model.itemFromIndex(index)
            if item is not None:
                item_data = item.data(Qt.UserRole)
                if item_data is not None:
                    unique_data_values.add(item_data)
                    if item.item_model is not None:
                        selected_items.append(item)
                        selected_item_ids.append(item.item_model.get_property('id'))
        server_ip = selected_items[0].item_model.get_property('server_ip')
        if len(unique_data_values) == 1:
            item_data = unique_data_values.pop()
            if item_data == TreeType.Camera:
                list_camera_item = selected_items
                self.cameras_not_in_group = selected_items
                list_just_select_camera_ids = selected_item_ids
            elif item_data == TreeType.Group:
                for group in selected_items:
                    if group.rowCount() > 0:
                        for camera_index in range(group.rowCount()):
                            camera = group.child(camera_index)
                            list_camera_item.append(camera)
                self.group_items = selected_items
            elif item_data == TreeType.Saved_View_Item:
                list_saved_view_selected = selected_items
            elif item_data == TreeType.Virtual_Window_Item:
                list_virtual_selected = selected_items
            else:
                return
        else:
            for item in selected_items:
                item_data = item.data(Qt.UserRole)
                if item_data not in [TreeType.Camera, TreeType.Group]:
                    return

            self.group_items = [item for item in selected_items if item.data(Qt.UserRole) == TreeType.Group]
            camera_items = [item for item in selected_items if item.data(Qt.UserRole) == TreeType.Camera]
            for group in self.group_items:
                if group.rowCount() > 0:
                    for camera_index in range(group.rowCount()):
                        camera = group.child(camera_index)
                        list_camera_item.append(camera)
            for camera_item in camera_items:
                if camera_item not in list_camera_item:
                    list_camera_item.append(camera_item)
                    self.cameras_not_in_group.append(camera_item)
        list_camera_id = []
        list_camera_model = []
        for camera_item in list_camera_item:
            if camera_item.data(Qt.UserRole) == TreeType.Camera and camera_item.item_model is not None:
                if camera_item.data(Qt.UserRole) == TreeType.Camera:
                    list_camera_model.append(camera_item.item_model)
                    list_camera_id.append(camera_item.item_model.get_property('id'))
        # Create a context menu
        self.context_menu = QMenu()
        self.context_menu.setWindowFlags(
            Qt.WindowType.FramelessWindowHint | Qt.WindowType.Popup)
        self.context_menu.setAttribute(
            Qt.WidgetAttribute.WA_TranslucentBackground, True)
        self.context_menu.setStyleSheet(Style.PrimaryStyleSheet.get_context_menu_style(theme_instance=main_controller))
        self.multi_select_action_delete = QAction(text=self.tr("Delete\tDel"), parent=self.context_menu)
        self.multi_select_action_delete.setShortcut(QKeySequence(Qt.Key.Key_Delete))
        self.multi_select_action_delete.triggered.connect(partial(self.delete_multi_item, selected_items, server_ip))
        if len(list_saved_view_selected) < 1 or len(list_virtual_selected) < 1:
            self.menu_list_tab_available = SubMenuOpenCameraInTab(model = item.item_model, item_data=TreeType.Multi_Select_Item,
                                                                  camera_name=None,
                                                                  show_grid_position=False,
                                                                  list_camera_selection=list_camera_model)
            self.menu_list_tab_available.open_in_tab_signal.connect(self.camera_open_in_tab)
            self.menu_list_tab_available.open_new_tab_signal.connect(self.camera_open_in_tab)
            self.menu_list_tab_available.open_group_in_view_signal.connect(self.camera_open_in_tab)

            self.multi_select_action_open_to = QAction(text=self.tr("Open cameras to ..."), parent=self.context_menu,
                                                       menu=self.menu_list_tab_available)
            self.add_camera_to_group_action = None
            if len(list_just_select_camera_ids) > 0:
                self.sub_menu_group_name = QMenu()
                self.sub_menu_group_name.setWindowFlags(
                    Qt.WindowType.FramelessWindowHint | Qt.WindowType.Popup)
                self.sub_menu_group_name.setAttribute(
                    Qt.WidgetAttribute.WA_TranslucentBackground, True)
                self.sub_menu_group_name.setStyleSheet(Style.PrimaryStyleSheet.get_context_menu_style(theme_instance=main_controller))

                list_groups = self.get_group_list(server_ip=selected_items[0].item_model.get_property('server_ip'))
                for idx, (key, group) in enumerate(list_groups.items()):
                    choose_group_action = QAction(text=f"{group.get_property('name')}", parent=self.sub_menu_group_name)
                    choose_group_action.triggered.connect(
                        partial(self.add_camera_to_group_from_treeview, group, idx, list_just_select_camera_ids))
                    self.sub_menu_group_name.addAction(choose_group_action)
                self.add_camera_to_group_action = QAction(text=self.tr("Add cameras to group"), parent=self.context_menu)
                self.add_camera_to_group_action.setMenu(self.sub_menu_group_name)
            self.context_menu.addAction(self.multi_select_action_open_to)
            if self.add_camera_to_group_action is not None:
                self.context_menu.addAction(self.add_camera_to_group_action)
        self.context_menu.addAction(self.multi_select_action_delete)
        self.context_menu.exec(position)

    def show_menu(self, position, item, group_name="", camera_name=""):
        self.main_menu = QMenu()
        self.main_menu.setWindowFlags(
            Qt.WindowType.FramelessWindowHint | Qt.WindowType.Popup)
        self.main_menu.setAttribute(
            Qt.WidgetAttribute.WA_TranslucentBackground, True)
        self.main_menu.setStyleSheet(Style.PrimaryStyleSheet.get_context_menu_style(theme_instance=main_controller))
        self.sub_menu = CustomSubMenu()
        text = item.text()
        server = self.get_server(server_ip=item.server_ip)
        item_data = item.data(Qt.UserRole)
        if item_data == TreeType.Camera:
            available = True
            new_custom_tab_widget = main_controller.list_parent['CustomTabWidget']
            current_widget: CameraGridWidget = new_custom_tab_widget.getCurrentWidget()
            if isinstance(current_widget, CameraGridWidget):
                model = item.item_model
                if model.get_property("id") in current_widget.gridModel.get_property("listGridData"):
                    available = False

            self.init_action_and_menu(item=item, item_tree_name=camera_name, camera_grid_widget=current_widget)
            if not available:
                self.exit_stream_camera = QAction(text=self.tr("Exit Streaming Camera ") + f"{camera_name}",
                                                  parent=self.main_menu)
                self.exit_stream_camera.triggered.connect(
                    lambda: (main_controller.stop_live_camera_signal.emit(camera_name)))
                self.main_menu.addAction(self.exit_stream_camera)
                self.main_menu.addSeparator()

            id = shortcut_key_model_manager.get_shortcut_id(start_key=Qt.Key.Key_Slash,name=item.item_model.get_property('id') if item.item_model is not None else None)
            if id is not None:
                text = self.tr(
                    "Shortcut ID") + f' ({id})'
            else:
                text = self.tr(
                    "Set Shortcut ID")
            shortcut_id = QAction(text=text, parent=self.main_menu)
            shortcut_id.triggered.connect(
                lambda: (self.shortcut_id_triggered(item,TreeType.Camera)))
            self.main_menu.addAction(self.action_open_camera_to)
            self.main_menu.addSeparator()
            self.main_menu.addAction(self.add_camera_to_group_action)
            self.main_menu.addSeparator()
            if isinstance(item.parent().item_model,GroupModel):
                self.main_menu.addAction(self.remove_camera_action)
            else:
                self.main_menu.addAction(self.delete_action)
            self.main_menu.addSeparator()
            self.main_menu.addAction(self.rename_action)
            self.main_menu.addSeparator()
            self.main_menu.addAction(self.choose_ai_flow_action)
            self.main_menu.addSeparator()
            self.main_menu.addAction(self.open_settings_action)
            self.main_menu.addSeparator()
            self.main_menu.addAction(shortcut_id)
            self.main_menu.exec_(position)
        elif item_data == TreeType.Group:
            # Kiểm tra trong màn hình camera screen có camera nào trong group này không
            available = True
            new_custom_tab_widget = main_controller.list_parent['CustomTabWidget']
            current_widget: CameraGridWidget = new_custom_tab_widget.getCurrentWidget()
            if isinstance(current_widget, CameraGridWidget):
                idx = None
                for index, item_group in enumerate(group_model_manager.get_group_list()):
                    if text == item_group.get_property('name'):
                        idx = item_group.get_property('id')
                        break
                if idx is not None:
                    for key, grid_item in current_widget.tab_model.data.listGridData.items():
                        model = grid_item.model
                        if isinstance(model, CameraModel):
                            if idx in model.get_property("cameraGroupIds"):
                                available = False
                                break
            ###############################################
            self.init_action_and_menu(item=item, item_tree_name=camera_name, camera_grid_widget=current_widget, is_click_camera=False)
            if not available:
                self.exit_stream_group = QAction(text=self.tr("Exit streaming group ") + f"{group_name}",
                                                 parent=self.main_menu)
                self.exit_stream_group.triggered.connect(
                    lambda: main_controller.stop_live_group_signal.emit(group_name))
                self.main_menu.addAction(self.exit_stream_group)
                self.exit_stream_group.setDisabled(available)
            self.main_menu.addAction(self.action_stream_group_to)
            self.main_menu.addSeparator()
            self.main_menu.addAction(self.delete_action)
            self.main_menu.addSeparator()
            self.main_menu.addAction(self.rename_action)
            self.main_menu.addSeparator()
            self.main_menu.addSeparator()
            self.main_menu.addAction(self.open_settings_action)
            self.main_menu.exec_(position)
        elif item_data == TreeType.List_Virtual_Window:
            new_virtual_window = QAction(text=self.tr(
                "Add Virtual Window"), parent=self.main_menu)
            new_virtual_window.triggered.connect(
                lambda: (self.new_virtual_window_triggered(item)))
            remove_all_virtual_window = QAction(text=self.tr(
                "Remove All Virtual Window"), parent=self.main_menu)
            remove_all_virtual_window.triggered.connect(
                lambda: (self.remove_all_virtual_window_triggered(item)))
            close_all_virtual_window = QAction(text=self.tr(
                "Close All Virtual Window"), parent=self.main_menu)
            close_all_virtual_window.triggered.connect(
                lambda: (self.close_all_virtual_window_triggered(item)))
            self.main_menu.addAction(new_virtual_window)
            self.main_menu.addSeparator()
            self.main_menu.addAction(remove_all_virtual_window)
            self.main_menu.addSeparator()
            self.main_menu.addAction(close_all_virtual_window)
            if item.rowCount() == 0:
                remove_all_virtual_window.setDisabled(True)
                close_all_virtual_window.setDisabled(True)
            self.main_menu.exec_(position)
        elif item_data == TreeType.Virtual_Window_Item:
            text = item.text()
            gridModel = item.item_model
            if gridModel.get_property('id') not in main_controller.list_parent:
                self.sub_menu.aboutToHide.connect(self.handle_menu_about_to_hide)
                open_virtual_window = QAction(text=self.tr("Open"), parent=self.sub_menu)
                open_virtual_window.setShortcut(Qt.Key.Key_Return)
                open_virtual_window.setMenu(self.sub_menu)
                ######## check co virtual window nao dang mo ##########
                list_screen_index = {}
                for server in self.tree_data.servers:
                    for base_item in server.list_virtual_windows.child_item:
                        if base_item.model.get_property('id') in main_controller.list_parent:
                            index = main_controller.list_parent[base_item.model.get_property('id')][0]
                            list_screen_index[index] = base_item.name
                ############## check man hinh #######################
                current_screen = self.screen()
                screens = QApplication.screens()
                for screen in screens:
                    screen_index = QApplication.screens().index(screen)
                    virtual_opened = ''
                    if screen_index in list_screen_index:
                        virtual_opened = list_screen_index[screen_index]
                    if screen != current_screen:
                        name = f'{screen.name()} {virtual_opened}'
                    else:
                        name = f'{screen.name()}'
                    ########################################################
                    virtual_window = CustomAction(text=name, parent=self.sub_menu)
                    virtual_window.hovered.connect(
                        partial(self.show_border_triggered, screen_index, screen,virtual_window))
                    virtual_window.triggered.connect(
                        partial(self.open_to_window_triggered, screen_index, item))
                    self.sub_menu.addAction(virtual_window)
                self.main_menu.addAction(open_virtual_window)
            else:
                self.sub_menu.aboutToHide.connect(self.handle_menu_about_to_hide)
                switch_virtual_window = QAction(text=self.tr("Switch"), parent=self.sub_menu)
                switch_virtual_window.setMenu(self.sub_menu)
                ######## check co virtual window nao dang mo ##########
                list_screen_index = {}
                for server in self.tree_data.servers:
                    for base_item in server.list_virtual_windows.child_item:
                        if base_item.model.get_property("id",None) in main_controller.list_parent:
                            index = main_controller.list_parent[base_item.model.get_property("id",None)][0]
                            list_screen_index[index] = base_item.name
                ############## check man hinh #######################
                current_screen = self.screen()
                screens = QApplication.screens()
                for screen in screens:
                    screen_index = QApplication.screens().index(screen)
                    virtual_opened = ''
                    if screen_index in list_screen_index:
                        virtual_opened = list_screen_index[screen_index]
                    if screen != current_screen:
                        name = f'{screen.name()} - ({virtual_opened})' if virtual_opened != '' else f'{screen.name()}'
                    else:
                        name = f'{screen.name()} - ({virtual_opened})' if virtual_opened != '' else f'{screen.name()}'
                    ########################################################
                    virtual_window = QAction(text=name, parent=self.sub_menu)
                    virtual_window.hovered.connect(
                        partial(self.show_border_triggered, screen_index, screen,virtual_window))
                    virtual_window.triggered.connect(
                        partial(self.switch_to_window_triggered, screen_index, item))
                    self.sub_menu.addAction(virtual_window)
                self.main_menu.addAction(switch_virtual_window)
            edit_virtual_window = QAction(text=self.tr("Rename"), parent=self.main_menu)
            edit_virtual_window.setShortcut(Qt.Key.Key_F2)
            edit_virtual_window.triggered.connect(
                lambda: (self.edit_virtual_window_triggered(item)))
            remove_virtual_window = QAction(text=self.tr("Remove"), parent=self.main_menu)
            remove_virtual_window.setShortcut(Qt.Key.Key_Delete)
            remove_virtual_window.triggered.connect(
                lambda: (self.remove_virtual_window_triggered(item)))
            id = shortcut_key_model_manager.get_shortcut_id(start_key=Qt.Key.Key_Asterisk,name=item.item_model.get_property('id') if item.item_model is not None else None)
            if id is not None:
                shortcut_id_name = self.tr(
                    "Shortcut ID") + f' ({id})'
            else:
                shortcut_id_name = self.tr(
                    "Set Shortcut ID")
            shortcut_id = QAction(text=shortcut_id_name, parent=self.main_menu)
            shortcut_id.triggered.connect(
                lambda: (self.shortcut_id_triggered(item,TreeType.Virtual_Window_Item)))
            self.main_menu.addSeparator()
            self.main_menu.addAction(edit_virtual_window)
            self.main_menu.addSeparator()
            self.main_menu.addAction(remove_virtual_window)
            if text in main_controller.list_parent:
                close_virtual_window = QAction(text=self.tr("Close"), parent=self.main_menu)
                close_virtual_window.triggered.connect(
                    lambda: (self.close_virtual_window_triggered(item)))
                self.main_menu.addAction(close_virtual_window)
            self.main_menu.addSeparator()
            self.main_menu.addAction(shortcut_id)
            self.main_menu.exec_(position)

        elif item_data == TreeType.List_Saved_View:
            add_saved_view = QAction(text=self.tr(
                "Add Saved View"), parent=self.main_menu)
            add_saved_view.triggered.connect(
                lambda: (self.add_saved_view_triggered(item)))
            open_all_saved_view = QAction(text=self.tr(
                "Open All"), parent=self.main_menu)
            open_all_saved_view.triggered.connect(
                lambda: (self.open_all_saved_view_triggered(item)))
            remove_all_saved_view = QAction(text=self.tr(
                "Remove All"), parent=self.main_menu)
            remove_all_saved_view.triggered.connect(
                lambda: (self.remove_all_saved_view_triggered(item)))
            close_all_saved_view = QAction(text=self.tr(
                "Close All"), parent=self.main_menu)
            close_all_saved_view.triggered.connect(
                lambda: (self.close_all_saved_view_triggered(item)))
            self.main_menu.addAction(add_saved_view)
            self.main_menu.addSeparator()
            self.main_menu.addAction(open_all_saved_view)
            self.main_menu.addSeparator()
            self.main_menu.addAction(remove_all_saved_view)
            self.main_menu.addSeparator()
            self.main_menu.addAction(close_all_saved_view)
            if item.rowCount() == 0:
                open_all_saved_view.setDisabled(True)
                remove_all_saved_view.setDisabled(True)
                close_all_saved_view.setDisabled(True)
            self.main_menu.exec_(position)
        elif item_data == TreeType.Saved_View_Item:
            server = self.get_server(server_ip = item.server_ip)
            if not self.is_saved_view_opened(item):
                open_saved_view = QAction(text=self.tr("Open"), parent=self.sub_menu)
                open_saved_view.setShortcut(Qt.Key.Key_Return)
                open_saved_view.setMenu(self.sub_menu)
                self.main_menu.addAction(open_saved_view)
                new_tab_savedview = QAction(text=self.tr("New Tab"), parent=self.sub_menu)
                new_tab_savedview.triggered.connect(
                    partial(self.new_tab_savedview_triggered, item))
                self.sub_menu.addAction(new_tab_savedview)
                self.sub_menu.addSeparator()
                create_virtual_window = QAction(text=self.tr('Create Virtual Window'), parent=self.sub_menu)
                create_virtual_window.triggered.connect(
                    partial(self.create_virtual_window_triggered, item))
                self.sub_menu.addAction(create_virtual_window)
                # lấy danh sách Virtual Window
                ######## check co virtual window nao dang mo ##########
                list_screen_index = {}
                current_screen = self.screen()
                screens = QApplication.screens()
                for screen in screens:
                    screen_index = QApplication.screens().index(screen)
                    list_screen_index[screen_index] = screen.name()
                if server.list_virtual_windows.child_item is not None:
                    for base_item in server.list_virtual_windows.child_item:
                        if base_item.model.get_property('id') in main_controller.list_parent:
                            index = main_controller.list_parent[base_item.model.get_property('id')][0]
                            name = f'{base_item.name} ({list_screen_index[index]})'
                            virtual_window = QAction(text=name, parent=self.sub_menu)
                            virtual_window.triggered.connect(
                                partial(self.virtual_window_triggered, screen_index=index, item=item,
                                        virtual_window_model=base_item.model))
                            self.sub_menu.addAction(virtual_window)

                        else:
                            name = base_item.name
                            virtual_window = QAction(text=name, parent=self.sub_menu)
                            virtual_window.triggered.connect(
                                partial(self.virtual_window_triggered, item=item, virtual_window_model=base_item.model))
                            self.sub_menu.addAction(virtual_window)

            else:
                close_saved_view = QAction(text=self.tr("Close"), parent=self.main_menu)
                close_saved_view.triggered.connect(
                    lambda: (self.close_saved_view_triggered(item)))
                self.main_menu.addSeparator()
                self.main_menu.addAction(close_saved_view)

            edit_saved_view = QAction(text=self.tr("Rename"), parent=self.main_menu)
            edit_saved_view.setShortcut(Qt.Key.Key_F2)
            edit_saved_view.triggered.connect(
                lambda: (self.edit_saved_view_triggered(item)))

            remove_saved_view = QAction(text=self.tr("Remove"), parent=self.main_menu)
            remove_saved_view.setShortcut(Qt.Key.Key_Delete)
            remove_saved_view.triggered.connect(
                lambda: (self.remove_saved_view_triggered(item)))
            id = shortcut_key_model_manager.get_shortcut_id(start_key=Qt.Key.Key_Asterisk,name=item.item_model.get_property('id') if item.item_model is not None else None)
            if id is not None:
                text = self.tr(
                    "Shortcut ID") + f' ({id})'
            else:
                text = self.tr(
                    "Set Shortcut ID")
            shortcut_id = QAction(text=text, parent=self.main_menu)
            shortcut_id.triggered.connect(
                lambda: (self.shortcut_id_triggered(item,TreeType.Saved_View_Item)))
            self.main_menu.addSeparator()
            self.main_menu.addAction(edit_saved_view)
            self.main_menu.addSeparator()
            self.main_menu.addAction(remove_saved_view)
            self.main_menu.addSeparator()
            self.main_menu.addAction(shortcut_id)
            self.main_menu.exec_(position)

        elif item_data == TreeType.List_Map:
            self.open_map_position_action = SubMenuOpenMapInGrid(model = item.item_model,item_data=TreeType.List_Map, item=item)
            self.open_map_position_action.open_in_tab_signal.connect(self.map_open_in_tab)
            open_map = QAction(text=self.tr(
                "Open digital map"), parent=self.main_menu, menu=self.open_map_position_action)
            edit_map = QAction(text=self.tr(
                "Edit Map"), parent=self.main_menu)
            edit_map.triggered.connect(
                lambda: (self.edit_map_triggered(item, CommonEnum.TabType.MAPVIEW)))

            create_building = QAction(text=self.tr(
                "Create Building"), parent=self.main_menu)
            create_building.triggered.connect(
                lambda: (self.create_building(item)))
            remove_all_building = QAction(text=self.tr(
                "Remove all"), parent=self.main_menu)
            remove_all_building.triggered.connect(
                lambda: (self.remove_all_building_triggered(item)))

            self.main_menu.addAction(open_map)
            self.main_menu.addAction(edit_map)
            self.main_menu.addAction(create_building)
            self.main_menu.addAction(remove_all_building)
            self.main_menu.exec_(position)
        elif item_data == TreeType.BuildingItem:
            text = item.text()
            edit_building = QAction(text=self.tr(
                "Edit Building"), parent=self.main_menu)
            edit_building.triggered.connect(
                lambda: (self.edit_building_triggered(item)))
            create_floor = QAction(text=self.tr(
                "Create floor"), parent=self.main_menu)
            create_floor.triggered.connect(
                lambda: (self.create_floor(item)))
            remove_building = QAction(text=self.tr(
                "Remove building"), parent=self.main_menu)
            remove_building.triggered.connect(
                lambda: (self.remove_building_triggered(item)))

            self.main_menu.addAction(edit_building)
            self.main_menu.addAction(create_floor)
            self.main_menu.addAction(remove_building)
            self.main_menu.exec_(position)

        elif item_data == TreeType.FloorItem:
            # text = item.text()
            self.open_floor_position_action = SubMenuOpenFloorInGrid(model = item.item_model,item_data=TreeType.FloorItem, item=item)
            self.open_floor_position_action.open_in_tab_signal.connect(self.floor_open_in_tab)
            open_floor = QAction(text=self.tr(
                "Open floor"), parent=self.main_menu, menu=self.open_floor_position_action)
            edit_floor = QAction(text=self.tr(
                "Edit floor"), parent=self.main_menu)
            edit_floor.triggered.connect(
                lambda: (self.edit_floor_triggered(item,CommonEnum.TabType.FLOORVIEW)))
            remove_floor = QAction(text=self.tr(
                "Remove floor"), parent=self.main_menu)
            remove_floor.triggered.connect(
                lambda: (self.remove_floor_triggered(item)))

            self.main_menu.addAction(open_floor)
            self.main_menu.addAction(edit_floor)
            self.main_menu.addAction(remove_floor)
            self.main_menu.exec_(position)
        elif item_data == TreeType.List_Camera:
            self.create_group_action_from_parent = QAction(text=self.tr("Create group"), parent=self.main_menu)
            self.create_group_action_from_parent.triggered.connect(partial(self.create_group_single_item_trigger, item))
            self.main_menu.addAction(self.create_group_action_from_parent)
            self.main_menu.exec_(position)
        elif item_data == TreeType.Server:
            self.connect_3rd_party_server = QAction(text=self.tr("Connect to third-party server"), parent=self.main_menu)
            self.connect_3rd_party_server.triggered.connect(lambda: (self.connect_3rd_party_server_triggered(item=item)))
            self.main_menu.addAction(self.connect_3rd_party_server)
            self.main_menu.exec_(position)

    def retranslateUi(self):
        # Tạo danh sách mới để loại bỏ các item đã bị xóa
        valid_items = []
        for item in self.list_translate_item:
            try:
                # Kiểm tra item còn hợp lệ không
                if item is not None and hasattr(item, 'data'):
                    item_data = item.data(Qt.UserRole)
                    valid_items.append(item)
                    
                    if item_data == TreeType.List_Camera:
                        name = QCoreApplication.translate(
                            "MainTreeViewWidget", 'Camera List')
                        item.setText(name)
                    elif item_data == TreeType.List_Virtual_Window:
                        name = QCoreApplication.translate(
                            "MainTreeViewWidget", 'Virtual Window List')
                        item.setText(name)
                    elif item_data == TreeType.List_Saved_View:
                        name = QCoreApplication.translate(
                            "MainTreeViewWidget", 'Saved View List')
                        item.setText(name)
                    elif item_data == TreeType.List_Map:
                        name = QCoreApplication.translate(
                            "MainTreeViewWidget", 'Map List')
                        item.setText(name)
            except RuntimeError:
                # Item đã bị xóa, bỏ qua
                logger.debug(f"Item đã bị xóa trong retranslateUi, bỏ qua")
                continue
            except Exception as e:
                # Xử lý các lỗi khác
                logger.warning(f"Lỗi khi xử lý item trong retranslateUi: {e}")
                continue
        
        # Cập nhật danh sách chỉ giữ lại các item hợp lệ
        self.list_translate_item = valid_items
    
    def cleanup_translate_items(self):
        """Dọn dẹp danh sách translate items, loại bỏ các item đã bị xóa"""
        valid_items = []
        for item in self.list_translate_item:
            try:
                if item is not None and hasattr(item, 'data'):
                    # Thử truy cập data để kiểm tra item còn hợp lệ
                    item.data(Qt.UserRole)
                    valid_items.append(item)
            except (RuntimeError, AttributeError):
                # Item đã bị xóa, bỏ qua
                continue
        self.list_translate_item = valid_items
    
    def rebuild_translate_items(self):
        """Xây dựng lại danh sách translate items từ model hiện tại"""
        self.list_translate_item = []
        root_item = self.model.invisibleRootItem()
        self._collect_translate_items(root_item)
    
    def _collect_translate_items(self, parent_item):
        """Thu thập các item cần translate từ parent item"""
        for row in range(parent_item.rowCount()):
            child_item = parent_item.child(row)
            if child_item is not None:
                item_data = child_item.data(Qt.UserRole)
                if item_data in [TreeType.List_Camera, TreeType.List_Virtual_Window, 
                               TreeType.List_Saved_View, TreeType.List_Map]:
                    if child_item not in self.list_translate_item:
                        self.list_translate_item.append(child_item)
                # Đệ quy cho các item con
                self._collect_translate_items(child_item)

    def restyle_main_treeview_widget(self):
        self.tree_view.setStyleSheet(
            f"""
                    QTreeView {{
                        background-color: {main_controller.get_theme_attribute('Color', 'main_background')};
                        alternate-background-color: {main_controller.get_theme_attribute('Color', 'main_background')};
                        border: None;
                        /*color: {main_controller.get_theme_attribute('Color', 'text_color_all_app')};*/
                    }}
                    QTreeView::item {{
                        padding: 4px;
                        /*color: {main_controller.get_theme_attribute('Color', 'text_color_all_app')};*/
                    }}
                    QTreeView::item::selected {{
                        background-color: {main_controller.get_theme_attribute('Color', 'filter_background')};
                        color: {main_controller.get_theme_attribute('Color', 'primary')};
                        border: None;
                    }}
                    QTreeView::branch:has-children:closed {{
                        image: url({main_controller.get_theme_attribute('Image', 'treeview_expand_item')});
                    }}
                    QTreeView::branch:has-children:open {{
                        image: url({main_controller.get_theme_attribute('Image', 'treeview_collapse_item')});
                    }}
                    /* Thêm trạng thái selected cho branch */
                    QTreeView::branch:has-children:open:selected {{
                        image: url({main_controller.get_theme_attribute('Image', 'treeview_collapse_item')});
                    }}

                    QTreeView::branch:has-children:closed:selected {{
                        image: url({main_controller.get_theme_attribute('Image', 'treeview_expand_item')});
                    }}
                    QHeaderView::section {{
                        background-color: {main_controller.get_theme_attribute('Color', 'main_background')};
                        color: {main_controller.get_theme_attribute('Color', 'text_color_all_app')};
                    }}
                """)
        self.iterate_treeview()

    def iterate_treeview(self):
        def process_index(index):
            if not index.isValid():
                return
            item_data = self.model.data(index, Qt.UserRole)
            item = self.model.itemFromIndex(index)
            if item:
                if item_data == TreeType.Group:
                    item.setIcon(QIcon(main_controller.get_theme_attribute('Image', 'group_camera_treeview')))
                elif item_data == TreeType.Camera:
                    pass
                if item_data == TreeType.Server or item_data == TreeType.Invalid:
                    item.setIcon(QIcon(main_controller.get_theme_attribute('Image', 'treeview_server')))
                elif item_data == TreeType.List_Camera:
                    item.setIcon(QIcon(main_controller.get_theme_attribute('Image', 'list_devices')))
                elif item_data == TreeType.List_Virtual_Window:
                    item.setIcon(QIcon(main_controller.get_theme_attribute('Image', 'list_virtual_window')))
                elif item_data == TreeType.List_Saved_View:
                    item.setIcon(QIcon(main_controller.get_theme_attribute('Image', 'list_save_view')))
                elif item_data == TreeType.List_Map:
                    item.setIcon(QIcon(main_controller.get_theme_attribute('Image', 'list_map')))
                elif item_data == TreeType.Virtual_Window_Item or item_data == TreeType.Saved_View_Item:
                    item.setIcon(QIcon(main_controller.get_theme_attribute('Image', 'close_all_virtual')))
                elif item_data == TreeType.FloorItem:
                    item.setIcon(QIcon(Style.PrimaryImage.icon_floor))
                elif item_data == TreeType.Group:
                    item.setIcon(QIcon(main_controller.get_theme_attribute('Image', 'group_camera_treeview')))
                item_model = item.item_model
                if item_data == TreeType.Camera and (item_model.state_merged == CommonEnum.CameraState.DISCONNECTED_REC_PIN or \
                    item_model.state_merged == CommonEnum.CameraState.DISCONNECTED_REC_UNPIN or \
                    item_model.state_merged == CommonEnum.CameraState.DISCONNECTED_NOREC_PIN or \
                    item_model.state_merged == CommonEnum.CameraState.DISCONNECTED_NOREC_UNPIN):
                    item.setForeground(QBrush(QColor(169, 169, 169)))
                else:
                    item.setForeground(QBrush(QColor(main_controller.get_theme_attribute('Color', 'text_color_all_app'))))
            for row in range(self.model.rowCount(index)):
                child_index = self.model.index(row, 0, index)  # Column 0 for children
                process_index(child_index)
        for row in range(self.model.rowCount()):
            root_index = self.model.index(row, 0)  # Column 0 for root
            process_index(root_index)
class NewVirtualWindowDialog(NewBaseDialog):
    def __init__(self, parent=None, name=None, ok_title='Create', tab_type=CommonEnum.TabType.VIRTUALWINDOW):
        self.tab_type = tab_type
        self.ok_title = ok_title
        self.main_layout = QVBoxLayout()
        self.main_layout.setSpacing(0)
        virtual_window_name_widget = QWidget()
        virtual_window_name_layout = QVBoxLayout(virtual_window_name_widget)
        if self.tab_type == CommonEnum.TabType.SAVEDVIEW:
            self.virtual_window_name = InputWithTitle(title=self.tr(
                'Saved View Name'), key='saved_view_name')
        else:
            self.virtual_window_name = InputWithTitle(title=self.tr(
                'Virtual Window Name'), key='virtual_window_name')
        self.virtual_window_name.line_edit.returnPressed.connect(self.accept)
        if name is not None:
            self.virtual_window_name.line_edit.setText(name)
        self.virtual_window_name.callback_on_text_changed = self.callback_on_text_changed
        virtual_window_name_layout.addWidget(self.virtual_window_name)
        self.main_layout.addWidget(virtual_window_name_widget)
        if self.ok_title == self.tr('Create'):
            if self.tab_type == CommonEnum.TabType.SAVEDVIEW:
                self.title_name_label = self.tr("Add Saved View")
            else:
                self.title_name_label = self.tr("Add Virtual Window")
            footer_type = FooterType.CREATE_CANCEL
        else:
            self.title_name_label = self.tr("Edit Virtual Window")
            footer_type = FooterType.SAVE_CANCEL
        widget_main = QWidget()
        widget_main.setFixedWidth(Config.WIDTH_DIALOG_SHORTCUT)
        widget_main.setLayout(self.main_layout)
        super().__init__(parent, title=self.title_name_label, content_widget=widget_main,
                         width_dialog=Config.WIDTH_DIALOG_SHORTCUT, max_height_dialog=120, footer_type=footer_type)
        self.save_update_signal.connect(self.accept)

    def callback_on_text_changed(self, text=None, key=None):
        pass

    def create_clicked(self):
        pass

    def cancel_clicked(self):
        self.close()

class CreateFloorDialog(NewBaseDialog):
    def __init__(self, parent=None, name=None, ok_title='Create Floor', tab_type=CommonEnum.TabType.FLOORVIEW):
        self.tab_type = tab_type
        self.ok_title = ok_title
        self.main_layout = QVBoxLayout()
        self.main_layout.setSpacing(0)
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        self.content_name = "Name"
        if self.tab_type == CommonEnum.TabType.FLOORVIEW:
             self.content_name = InputWithTitle(title=self.tr(
                'Name floor'), key='name_floor')

        # HanhLT:  Khi enter thi co the create new virtual window luon -> cai thien UX
        self.content_name.line_edit.returnPressed.connect(self.accept)
        if name is not None:
             self.content_name.line_edit.setText(name)
        self.content_name.callback_on_text_changed = self.callback_on_text_changed
        content_layout.addWidget(self.content_name)
        self.main_layout.addWidget(content_widget)
        footer_type = FooterType.CREATE_CANCEL
        widget_main = QWidget()
        widget_main.setFixedWidth(Config.WIDTH_DIALOG_SHORTCUT)
        widget_main.setLayout(self.main_layout)
        super().__init__(parent, title=self.tr("Create floor"), content_widget=widget_main,
                         width_dialog=Config.WIDTH_DIALOG_SHORTCUT, max_height_dialog=120, footer_type=footer_type)
        self.save_update_signal.connect(self.accept)

    def callback_on_text_changed(self, text=None, key=None):
        pass

    def create_clicked(self):
        pass

    def cancel_clicked(self):
        self.close()
class CreateBuildingDialog(NewBaseDialog):
    def __init__(self, parent=None, name=None, ok_title='Create', tab_type=CommonEnum.TabType.BUIDINGVIEW):
        self.tab_type = tab_type
        self.ok_title = ok_title
        self.main_layout = QVBoxLayout()
        self.main_layout.setSpacing(0)
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        self.content_name = "Name"
        if self.tab_type == CommonEnum.TabType.BUIDINGVIEW:
             self.content_name = InputWithTitle(title=self.tr(
                'Building name'), key='building_name')
        self.content_name.line_edit.returnPressed.connect(self.accept)
        if name is not None:
             self.content_name.line_edit.setText(name)
        self.content_name.callback_on_text_changed = self.callback_on_text_changed
        content_layout.addWidget(self.content_name)
        self.main_layout.addWidget(content_widget)
        if self.ok_title == "Create":
            if self.tab_type == CommonEnum.TabType.BUIDINGVIEW:
                self.title_name_label = self.tr("Create Building")
            footer_type = FooterType.CREATE_CANCEL
        else:
            self.title_name_label = self.tr("Edit Building")
            footer_type = FooterType.SAVE_CANCEL
        widget_main = QWidget()
        widget_main.setFixedWidth(Config.WIDTH_DIALOG_VERTICAL)
        widget_main.setLayout(self.main_layout)
        super().__init__(parent, title=self.title_name_label, content_widget=widget_main,
                         width_dialog=Config.WIDTH_DIALOG_VERTICAL, max_height_dialog=120, footer_type=footer_type)
        self.save_update_signal.connect(self.accept)

    def callback_on_text_changed(self, text=None, key=None):
        pass

    def create_clicked(self):
        pass

    def cancel_clicked(self):
        self.close()
class ShortcutIDDialog(NewBaseDialog):
    def __init__(self, parent=None, name=None, ok_title='Create'):
        self.ok_title = ok_title
        self.main_layout = QVBoxLayout()
        self.main_layout.setSpacing(0)
        shortcut_id_widget = QWidget()
        shortcut_id_layout = QVBoxLayout(shortcut_id_widget)
        self.shortcut_id = LabelLineEdit(
            title=self.tr('Shortcut ID'), key='Shortcut ID')
        self.shortcut_id.main_layout.setAlignment(Qt.AlignmentFlag.AlignLeft)
        shortcut_id_layout.addWidget(self.shortcut_id)
        self.main_layout.addWidget(shortcut_id_widget)
        widget_main = QWidget()
        widget_main.setFixedWidth(Config.WIDTH_DIALOG_SHORTCUT)
        widget_main.setLayout(self.main_layout)
        if self.ok_title == 'Create':
            self.title_name_label = self.tr("Add Shortcut ID")
        else:
            self.title_name_label = self.tr("Edit Shortcut ID")
        super().__init__(parent, title=self.title_name_label, content_widget=widget_main, width_dialog=Config.WIDTH_DIALOG_SHORTCUT, max_height_dialog=120)
        self.save_update_signal.connect(self.accept)

    def create_clicked(self):
        pass

    def cancel_clicked(self):
        self.close()

class CustomAction(QAction):
    def __init__(self, text, parent=None):
        super().__init__(text, parent)
    def event(self,event):
        if event.type() == event.HoverEnter:
            print("Hover entered Custom Action")
        elif event.type() == event.HoverLeave:
            print("Hover left Custom Action")
        elif event.type() == event.KeyPress:
            print("Key pressed on Custom Action")
        elif event.type() == event.KeyRelease:
            print("Key released on Custom Action")
        return super().event(event)

